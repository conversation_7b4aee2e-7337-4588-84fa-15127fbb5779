:global {
  ////////////////////////////////////////////////////////////////
  // Light theme
  // ////////////////////////////////////////////////////////////
  .css-var-theme-light {
    // Global variables
    --ant-color-primary: #3278d9;
    --ant-color-success: #2abb71;
    --ant-color-success-border: #ccedd8;
    --ant-color-text: #000;
    --ant-color-text-secondary: #000000b2;
    --ant-color-text-tertiary: #00000066;
    --ant-color-border: #eaecef;
    --ant-color-bg-container: #fff;
    --ant-control-item-bg-hover: #eff1f4;
    --ant-control-item-bg-active: #ababab88;

    // Genie variables
    --genie-color-bg-container: #eff1f4;

    //.ant-menu {
    //  --ant-menu-item-selected-bg: #ddeeff;
    //  --ant-menu-item-selected-color: #3278d9;
    //}

    ////////////////////////////////
    // Input
    .ant-input,
    .ant-input-password {
      --genie-input-autofill-bg: #e8f0fe;
    }

    ////////////////////////////////
    // Layout
    .ant-layout {
      --ant-layout-body-bg: #fff;
    }

    ////////////////////////////////
    // Layout Sidebar Menu
    .ant-layout-sider {
      --ant-layout-light-sider-bg: #f7f9fa;
      --genie-sider-trigger-box-bg: #eaecef;
    }

    ////////////////////////////////
    // Pagination
    .ant-pagination {
      --ant-pagination-arrow-bg: #eff1f4;
      --ant-pagination-item-active-bg: #2a2c2f;
      --ant-pagination-item-bg: transparent;
      --ant-pagination-hover-bg: #eff1f4;
      --ant-pagination-active-bg: #eff1f4;
    }

    ////////////////////////////////
    // Tag
    .ant-tag {
      --ant-tag-active-bg: #f0f9ff;
      --ant-tag-active-color: #3278d9;
    }

    ///////////////////////////////////////////////////////////////////
    // Custom components

    // Login
    .login-wrapper {
      --genie-login-page-bg: #f7f9fa;
      --genie-login-page-logo-color: #111;
    }
  }

  ////////////////////////////////////////////////////////////////
  // Dark theme
  // ////////////////////////////////////////////////////////////
  .css-var-theme-dark {
    /////////////////////////////////
    // Global variables

    // Root variables
    --color-canvas-default: #3a4454;
    --color-canvas-subtle: #242b38;

    // Ant Design variables
    --ant-color-primary: #3278d9;
    --ant-color-success: #2abb71;
    --ant-color-success-border: #ccedd8;
    --ant-color-text: #fff;
    --ant-color-text-secondary: #ffffffb2;
    --ant-color-text-tertiary: #ffffff66;
    --ant-color-text-quaternary: #ffffff52;
    --ant-color-text-disabled: #ffffff52;
    --ant-color-text-description: #ffffff66;
    --ant-color-text-heading: #fff;
    --ant-color-text-placeholder: #ffffff66;
    --ant-color-bg-container: #242b38;
    --ant-color-bg-container-disabled: #3a4454;
    --ant-color-bg-elevated: #1c222e;
    --ant-color-border: #3a4454;
    --ant-color-icon: #ffffff52;
    --ant-color-icon-hover: #ffffffb2;
    --ant-color-error: #d94032;
    --ant-color-error-bg: #f44336;
    --ant-color-error-border: #f44336;
    --ant-color-error-border-hover: #ffa39e;
    --ant-color-error-hover: #ffffff;
    --ant-color-fill: #ffffff66;
    --ant-color-fill-secondary: #ffffff33;
    --ant-color-fill-tertiary: #ffffff11;
    --ant-opacity-image: 0.95;
    --ant-control-item-bg-hover: #3a4454;
    --ant-control-item-bg-active: #4b5565;

    // Genie variables
    --genie-color-bg-container: #242b38;

    ////////////////////////////////
    // Button
    .ant-btn {
      --ant-button-text-text-color: #ffffffb2;
      --ant-button-text-text-hover-color: #ffffff66;
      --ant-button-text-hover-bg: #242b38;
      --ant-button-text-hover-color: #fff;
      --ant-button-default-bg: #242b38;
      --ant-button-default-color: #fff;
      --ant-button-default-border-color: #3a4454;
      --ant-button-default-hover-bg: #3a4454;
      --ant-button-default-active-bg: #3a445488;
      --ant-button-border-color-disabled: #4c515c;

      &.ant-btn-color-dangerous {
        --ant-btn-danger-bg: #3a4454;
        --ant-btn-danger-color: #fff;
        --ant-btn-danger-border-color: #3a4454;
      }
    }

    .ant-btn-icon > svg > * {
      stroke: var(--ant-color-text);
    }

    ////////////////////////////////
    // Form
    .ant-form {
      .ant-form-item {
        --ant-form-label-color: #fff;
      }
    }

    ////////////////////////////////
    // Input
    .ant-select-selection-search-input,
    .ant-input-outlined,
    .ant-input,
    .ant-input-password,
    .ant-input-number {
      --ant-input-bg: #1c222e;
      --ant-input-hover-border-color: #3a4454;
      --ant-input-hover-bg: #3a4454;
      --ant-input-active-bg: #3a4454;
      --ant-input-number-hover-bg: #3a4454;
      --ant-input-number-active-bg: #3a4454;
      --ant-input-number-handle-bg: var(--ant-input-bg);
      --ant-input-number-handle-active-bg: #3a4454a4;
      --ant-input-number-handle-border-color: #3a4454;
      --ant-input-number-handle-hover-color: var(--ant-color-text);
      --ant-color-bg-container-disabled: #3a4454;
      --genie-input-autofill-bg: #1c222e;

      -webkit-input-placeholder: var(--ant-color-text);
      color: var(--ant-color-text);
      box-shadow: 0 0 0 100% var(--ant-input-bg) inset;
      caret-color: var(--ant-color-text);

      &:autofill {
        -webkit-text-fill-color: var(--ant-color-text);
      }

      &::placeholder {
        color: #ffffff66;
      }
    }

    ////////////////////////////////
    // Layout
    .ant-layout {
      --ant-layout-body-bg: #0f1625;
    }

    // Layout Sidebar Menu
    .ant-layout-sider {
      --ant-layout-light-sider-bg: #1c222e;
      --genie-sider-trigger-box-bg: #3a4454;
    }

    ////////////////////////////////
    // Menu
    .ant-menu {
      --ant-menu-group-title-color: #ffffff66;
      --ant-menu-item-bg: #0f1625;
      --ant-menu-item-color: #ffffffb2;
      --ant-menu-item-selected-bg: #1f2c4d;
      --ant-menu-item-selected-color: #3278d9;
      --ant-menu-item-hover-bg: #242b38;
      --ant-menu-item-hover-color: #fff;
      --ant-menu-item-active-bg: #242b38;
      --ant-menu-item-active-color: #fff;
      --ant-menu-popup-bg: #0f1625;

      .ant-menu-item.ant-menu-item-selected {
        --ant-color-text-secondary: #3278d9;
        --ant-color-text-placeholder: #3278d9;
      }
    }

    ////////////////////////////////
    // Modal
    .ant-modal {
      --ant-modal-title-color: #fff;
      --ant-modal-content-bg: #0f1625;
      --ant-modal-header-bg: #0f1625;
      --ant-modal-header-color: #fff;
      --ant-modal-close-color: #ffffffb2;
    }

    ////////////////////////////////
    // Pagination
    .ant-pagination {
      --ant-pagination-arrow-bg: #242b38;
      --ant-pagination-item-active-bg: #242b38;
      --ant-pagination-item-bg: transparent;
      --ant-pagination-hover-bg: #3a4454;
      --ant-pagination-active-bg: #3a4454;

      .ant-select-selector {
        --ant-select-selector-bg: #242b38;

        .ant-select-selection-search-input {
          --ant-input-bg: transparent;
        }

        &:focus-within .ant-select-selection-item {
          color: #ffffff66;
        }
      }

      .ant-select-dropdown {
        background-color: #242b38;
        --ant-select-option-selected-bg: #fefefe;
      }
    }

    ////////////////////////////////
    // Picker
    .ant-picker-outlined {
      --ant-date-picker-hover-bg: #3a4454;
      --ant-date-picker-active-bg: #3a4454;

      input::placeholder {
        color: #ffffff66;
      }
    }

    ////////////////////////////////
    // QRCode
    .ant-qrcode {
      background-color: #fff !important;
    }

    ///////////////////////////////
    // Radio Group
    .ant-radio-group {
      .ant-radio-button-wrapper {
        --ant-radio-button-bg: transparent;
        --ant-radio-button-color: #fff;
        --ant-radio-button-checked-bg: transparent;
        --ant-radio-button-checked-bg-disabled: #3a445454;
        --ant-radio-button-checked-color-disabled: #fff;
      }
    }

    ////////////////////////////////
    // Select
    .ant-select {
      --ant-select-selector-bg: #1c222e;
    }

    ////////////////////////////////
    // Skeleton
    .ant-skeleton {
      --ant-skeleton-gradient-from-color: #d3e7fd11;
      --ant-skeleton-gradient-to-color: #d3e7fd33;
    }

    ////////////////////////////////
    // Slider
    .ant-slider {
      --ant-slider-rail-bg: #3a4454;
      --ant-slider-rail-hover-bg: #4d5767;
    }

    ////////////////////////////////
    // Steps
    .ant-steps {
      --ant-color-icon: #3278d9;
      --ant-steps-wait-icon-color: #ffffff;
      --ant-steps-wait-icon-bg-color: #ffffff66;
      --ant-steps-finish-icon-bg-color: #a4c7e8;
      --ant-steps-finish-icon-border-color: #a4c7e8;

      .ant-steps-item-container:hover {
        .ant-steps-item-icon {
          .ant-steps-icon {
            --ant-color-primary: #47a2ea;
          }
        }
      }
    }

    ////////////////////////////////
    // Switch
    .ant-switch {
      --ant-color-text-quaternary: #3a4454;
    }

    ////////////////////////////////
    // Slider
    .ant-slider {
      --ant-slider-rail-bg: #3a4454;
      --ant-slider-track-bg: #3278d9;
      --ant-slider-rail-hover-bg: #4b5565;
    }

    ////////////////////////////////
    // Tabs
    .ant-tabs {
      --ant-tabs-item-color: #ffffffb2;
      --ant-tabs-item-active-color: #fff;
      --ant-tabs-item-hover-color: #fff;
      --ant-tabs-ink-bar-bg: #3278d9;
    }

    ////////////////////////////////
    // Tag
    .ant-tag {
      --ant-tag-default-bg: #242b38;
      --ant-tag-default-color: #fff;
      --genie-tag-active-bg: #1f2c4d;
      --genie-tag-active-color: #3278d9;
      --genie-tag-inactive-bg: #3a4454;
      --genie-tag-inactive-color: #ffffff;
      --genie-tag-enabled-bg: #ebfaf0;
      --genie-tag-enabled-color: #2abb71;
      --genie-tag-disabled-bg: #fff4f0;
      --genie-tag-disabled-color: #d94032;
    }

    ////////////////////////////////
    // Table
    .ant-table {
      --ant-table-header-bg: #3a4454;
      --ant-table-header-color: #fff;
      --ant-table-border-color: #3a4454;
      --ant-table-row-hover-bg: #3a445483;
      --ant-table-row-selected-bg: #3a445455;
      --ant-table-row-selected-hover-bg: #3a44549f;
    }

    ////////////////////////////////
    // Upload
    .ant-upload {
      button {
        color: #fff;
      }
    }

    ///////////////////////////////////////////////////////////////////
    // Ant Independent components

    // Message
    &.ant-message {
      .ant-message-notice-wrapper .ant-message-notice-content {
        --ant-message-content-bg: #242b38;
      }
    }

    // Select dropdown
    &.ant-select-dropdown
      .ant-select-item-option:not(.ant-select-item-option-disabled) {
      --ant-select-option-selected-color: #fff;
      --ant-select-option-selected-bg: #3a4454;
      --ant-select-option-active-bg: #deedff11;
    }

    ///////////////////////////////////////////////////////////////////
    // Custom components

    // Login page
    .login-wrapper {
      --genie-login-page-bg: #0f1625;
      --genie-login-page-logo-color: #bbb;
    }

    // React flow
    .react-flow {
      --xy-minimap-background-color-default: var(--ant-color-bg-container);
      --xy-minimap-mask-background-color-default: var(--ant-color-bg-elevated);
    }

    // Scrollbar
    div:hover::-webkit-scrollbar-thumb {
      background: #3a4454;
    }
  }
}
