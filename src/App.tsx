import { App as AntApp, ConfigProvider, theme } from 'antd'
import {
  AntdStaticFunction,
  NotificationGlobalConfig,
} from 'components/antd-static-function'
import { observer } from 'mobx-react'
import { NuqsAdapter } from 'nuqs/adapters/react'
import { Suspense, useEffect } from 'react'
import roleStore from 'stores/role'
import userStore from 'stores/user'
import { handleAfterMounted } from './global'
import AppRoutes from './routes'

import { useGlobalStore } from 'stores/global'

import './App.scss'
handleAfterMounted()

const App = () => {
  const isPortal = location.pathname.startsWith('/portal')
  const themeName = useGlobalStore((state) => state.theme)

  // Only support light and dark themes in the portal
  const cssVarKey = isPortal
    ? `css-var-theme-${themeName}`
    : `css-var-theme-light`

  const initPage = async () => {
    await userStore.init()
    await userStore.getCurrentUser()
    await roleStore.initRoleList()
  }

  useEffect(() => {
    // Only check user and redirect when in the portal
    if (isPortal) {
      initPage()
    }
  }, [])

  return (
    <Suspense>
      <ConfigProvider
        theme={{
          cssVar: {
            key: cssVarKey,
          },
          token: {
            fontFamily: `Helvetica, ${theme.defaultSeed.fontFamily}`,
            fontSize: 16,
            controlHeight: 36,
          },
        }}
      >
        <AntApp className="app-wrapper" notification={NotificationGlobalConfig}>
          <AntdStaticFunction />
          <NuqsAdapter>
            <AppRoutes />
          </NuqsAdapter>
        </AntApp>
      </ConfigProvider>
    </Suspense>
  )
}

export default observer(App)
