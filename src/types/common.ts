import type { RepositoryPaginationResponse } from 'api/data-contracts'

// not defined in api contract yet
export type PaginationRequestParams = {
  page?: number
  limit?: number
  sort?: string
}
export type SortOrder = 'asc' | 'desc'

/**
 * @example cosnt sorts = {name: 'asc', category:'desc'}
 */
export type SortValue<T extends string = string> = Record<T, SortOrder>

export type WithPaginationRequest<
  TParams extends Record<string, unknown> = {},
> = TParams & PaginationRequestParams

/**
 * @description A type that represents a paginated response with a list of items and pagination information
 * @param T - The type of the items in the list
 */
export type WithPaginationResponse<T> = {
  data: T[]
  pagination: Pick<
    RepositoryPaginationResponse,
    'totalItems' | 'currentPage' | 'perPage'
  >
}
