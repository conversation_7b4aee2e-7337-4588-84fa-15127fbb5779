/**
 * Result pattern
 * @see  https://www.dennisokeeffe.com/blog/2024-07-14-creating-a-result-type-in-typescript
 */
export type Result<TData, TError = Error> =
  | {
      success: true
      data: TData
    }
  | {
      success: false
      error: TError
    }

// Represents an array of string values, comma-separated, e.g "["1","2","3"]", T = set of values of String type
export type StringifiedArray<T extends string = string> =
  | `${T}`
  | `${T},${string}`

// Omit only the keys that exist in the object
export type StrictOmit<T, K extends keyof T> = Omit<T, K>
