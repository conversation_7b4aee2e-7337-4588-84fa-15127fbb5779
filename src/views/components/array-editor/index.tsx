import { Button, <PERSON>lex, Typography } from 'antd'
import {
  ALL_DATA_TYPE,
  ARRAY_DATA_TYPE,
  ISO_8601_DATE_FORMAT,
} from 'constants/common'
import { useState } from 'react'
import { dayjsUTC } from 'utils/filter'
import type { SmartApiValueType } from 'views/portal/agent/smart-api/types'
import { ArrayEditorModal } from './modal'

const getBaseType = (
  arrayType: (typeof ALL_DATA_TYPE)[number]
): SmartApiValueType => {
  return arrayType.match(/Array<(.+)>/)?.[1] as SmartApiValueType
}

const formatValue = (value: any, type: SmartApiValueType): string => {
  switch (type) {
    case 'Date':
      return dayjsUTC(value).format(ISO_8601_DATE_FORMAT)
    default:
      return JSON.stringify(value)
  }
}

type ArrayEditorProps = {
  type: (typeof ARRAY_DATA_TYPE)[number]
  value?: any[]
  onChange?: (value: any[]) => void
}

export function ArrayEditor(props: ArrayEditorProps) {
  const [isEditing, setIsEditing] = useState(false)
  const baseType = getBaseType(props.type)

  if (!baseType) {
    throw new Error(
      `Invalid type for array editor: ${props.type}, expected Array<...>`
    )
  }

  return (
    <Flex gap={24} justify="space-between" align="center">
      <Typography.Text ellipsis>
        {Array.isArray(props.value)
          ? props.value.map((v) => formatValue(v, baseType)).join(', ')
          : ''}
      </Typography.Text>
      <Button onClick={() => setIsEditing(true)} type="link">
        Edit
      </Button>
      <ArrayEditorModal
        value={props.value}
        open={isEditing}
        onCancel={() => setIsEditing(false)}
        onSave={(value) => {
          props.onChange?.(value)
          setIsEditing(false)
        }}
        type={props.type}
      />
    </Flex>
  )
}

export default ArrayEditor
