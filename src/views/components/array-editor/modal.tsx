import { MinusOutlined, PlusOutlined } from '@ant-design/icons'
import { Button, Flex, Form, Modal, Table } from 'antd'
import type {
  FormListFieldData,
  FormListOperation,
  FormListProps,
} from 'antd/es/form/FormList'
import type { ColumnType, TableProps } from 'antd/es/table'
import FormListButton from 'components/form-list-button'
import useResetModalOnCloseModal from 'hooks/use-reset-modal-on-close-modal'
import type { SmartApiValueType } from '../../portal/agent/smart-api/types'
import { getInitialValueForType, ValueInput } from '../value-input'
import { ARRAY_DATA_TYPE } from 'constants/common'
import { useState } from 'react'

interface ArrayEditorModalProps {
  value?: any[]
  open: boolean
  onCancel: () => void
  onSave: (value: any[]) => void
  type: (typeof ARRAY_DATA_TYPE)[number]
}

export function ArrayEditorModal(props: ArrayEditorModalProps) {
  const [form] = Form.useForm()
  const baseType = props.type.match(
    /Array<(.+)>/
  )?.[1] as (typeof ARRAY_DATA_TYPE)[number]

  if (!baseType) {
    throw new Error(
      `Invalid type for array editor: ${props.type}, expected Array<...>`
    )
  }

  useResetModalOnCloseModal({
    form,
    open: props.open,
    initialValues: { values: Array.isArray(props.value) ? props.value : [] },
  })

  function handleFinish(formValues: { values: any[] }) {
    const values = formValues.values ?? []
    props.onSave(values)
  }

  // Render

  const getColumns: (
    props: Partial<FormListOperation>
  ) => TableProps<FormListFieldData>['columns'] = (props) => {
    const { remove } = props
    return [
      {
        title: 'Value',
        key: 'value',
        render: (_: any, field: FormListFieldData) => {
          return (
            <Form.Item
              key={field.key}
              name={field.name}
              style={{ marginBottom: 0 }}
              rules={[{ required: true, message: 'Value cannot be empty' }]}
            >
              <ValueInput
                type={baseType}
                required
                placeholder={`Enter ${baseType}`}
              />
            </Form.Item>
          )
        },
      },
      {
        title: 'Action',
        key: 'action',
        align: 'center',
        width: 80,
        render: (_: any, field: FormListFieldData) => (
          <Button
            danger
            type="link"
            icon={<MinusOutlined />}
            onClick={() => remove?.(field.name)}
          />
        ),
      },
    ]
  }

  return (
    <Modal
      title="Edit values"
      open={props.open}
      onCancel={props.onCancel}
      centered
      destroyOnClose
      okButtonProps={{
        autoFocus: true,
        htmlType: 'submit',
      }}
      okText="Save"
      width={500}
      modalRender={(dom) => (
        <Form
          layout="vertical"
          form={form}
          onFinish={handleFinish}
          initialValues={{
            values: Array.isArray(props.value) ? props.value : [],
          }}
        >
          {dom}
        </Form>
      )}
    >
      <Form.List name="values">
        {(fields, { add, remove }) => {
          return (
            <Flex vertical gap={16}>
              <Table
                dataSource={fields}
                columns={getColumns({ remove, add })}
                pagination={false}
                size="middle"
                locale={{ emptyText: 'No values' }}
              />
              <FormListButton
                text="Add Value"
                icon={<PlusOutlined />}
                style={{ width: '100%' }}
                onClick={() => {
                  const defaultValue = getInitialValueForType(baseType)
                  add(defaultValue)
                }}
              />
            </Flex>
          )
        }}
      </Form.List>
    </Modal>
  )
}
