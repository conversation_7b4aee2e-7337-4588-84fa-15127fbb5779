/**
 * This function will return true if the value is a valid object
 * EG, 'true' -> false
 *     '123' -> false
 *     'null' -> false (if allowNull is false)
 *     '"I'm a string"' -> false
 */
export function isStringValidObject(value: string, allowNull = false) {
  try {
    var o = JSON.parse(value)
    // Handle non-exception-throwing cases:
    // Neither JSON.parse(false) or JSON.parse(1234) throw errors, hence the type-checking,

    if (o === null && allowNull) {
      return true
    }
    // JSON.parse(null) returns null, and typeof null === "object", but null is falsey
    // so if o is null => false && true -> false
    if (o && typeof o === 'object') {
      return true
    }
  } catch (e) {}

  return false
}

function isValidObject(value: unknown) {}
