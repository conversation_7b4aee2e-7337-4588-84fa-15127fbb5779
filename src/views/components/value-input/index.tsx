import { UploadOutlined } from '@ant-design/icons'
import type { FormRule } from 'antd'
import { Button, DatePicker, Input, InputNumber, Switch, Upload } from 'antd'
import type { PickerProps } from 'antd/es/date-picker/generatePicker'
import type { InputNumberProps } from 'antd/es/input-number'
import type { UploadFile } from 'antd/es/upload/interface'
import { PluginPluginDataType } from 'api/data-contracts'
import {
  ALL_DATA_TYPE,
  isArrayType,
  ISO_8601_DATE_TIME_FORMAT,
} from 'constants/common'
import type { Dayjs } from 'dayjs'
import type { SmartApiValueType } from 'views/portal/agent/smart-api/types'
import { ArrayEditor } from '../array-editor'
import { ObjectEditor } from '../object-editor'

/**
 * ValueInput, value is intentionally any type
 * to support all Smart API value types
 */
type ValueInputProps = {
  type?: (typeof ALL_DATA_TYPE)[number]
  required?: boolean
  placeholder?: string
  value?: any
  onChange?: (value: any) => void
  className?: string
  style?: React.CSSProperties

  [key: string]: any
}

// Convert Upload event to fileList
const getValueFromEvent = (e: any) => {
  if (Array.isArray(e)) {
    return e
  }
  return e?.fileList
}

export const ValueInput = ({
  type = PluginPluginDataType.String,
  required,
  placeholder,
  ...props
}: ValueInputProps) => {
  const commonDatePickerProps: Partial<PickerProps<Dayjs>> = {
    style: { width: '100%' },
    ...props,
  }

  const commonInputNumberProps: Partial<InputNumberProps> = {
    style: { width: '100%' },
    ...props,
  }

  // Move declarations outside switch
  const accept = type === 'Image' ? 'image/*' : '.pdf,.doc,.docx,.txt'
  const fileList: UploadFile<any>[] = props.value
    ? [
        {
          uid: '-1',
          name: props.value.name,
          status: 'done' as const,
          originFileObj: props.value,
        },
      ]
    : []

  if (isArrayType(type)) {
    return (
      <ArrayEditor type={type} value={props.value} onChange={props.onChange} />
    )
  }

  switch (type) {
    case 'Date':
      return (
        <DatePicker
          {...commonDatePickerProps}
          format="YYYY-MM-DD"
          placeholder={placeholder ?? 'Select date'}
        />
      )
    case 'Datetime':
      return (
        <DatePicker
          {...commonDatePickerProps}
          showTime
          format={ISO_8601_DATE_TIME_FORMAT}
          placeholder={placeholder ?? 'Select date and time'}
        />
      )
    case 'Number':
      return (
        <InputNumber
          {...commonInputNumberProps}
          placeholder={placeholder ?? 'Enter number'}
        />
      )
    case 'Integer':
      return (
        <InputNumber
          {...commonInputNumberProps}
          precision={0}
          placeholder={placeholder ?? 'Enter integer'}
        />
      )
    case 'Boolean':
      return (
        <Switch
          {...props}
          checkedChildren="true"
          unCheckedChildren="false"
          checked={Boolean(props.value)}
          defaultChecked={false}
        />
      )
    case 'Object':
      return <ObjectEditor value={props.value} onChange={props.onChange} />
    case 'Document':
    case 'Image':
      return (
        <Upload
          accept={accept}
          maxCount={1}
          listType={type === 'Image' ? 'picture' : 'text'}
          fileList={fileList}
          beforeUpload={() => false} // Prevent auto upload
          onChange={(info) => {
            const fileList = getValueFromEvent(info)
            props.onChange?.(fileList?.[0]?.originFileObj)
          }}
        >
          <Button icon={<UploadOutlined />}>Select File</Button>
        </Upload>
      )
    default:
      // Fallback to string input for unsupported types
      return <Input {...props} placeholder={placeholder ?? 'Enter value'} />
  }
}

export const getValueValidationRules = (
  type: SmartApiValueType,
  required?: boolean
): FormRule[] => [
  { required: required, message: 'Value is required' },
  {
    validator: (_: unknown, value: unknown) => {
      // For non-string types, null is allowed
      if (type !== 'String' && value === null) {
        return Promise.resolve()
      }

      if (type.startsWith('Array<')) {
        if (!Array.isArray(value)) {
          return Promise.reject(new Error('Please enter a valid array'))
        }
        return Promise.resolve()
      }

      switch (type) {
        case 'Number':
        case 'Integer':
          if (isNaN(Number(value))) {
            return Promise.reject(new Error('Please enter a valid number'))
          }
          if (type === 'Integer' && !Number.isInteger(Number(value))) {
            return Promise.reject(new Error('Please enter a valid integer'))
          }
          break
        case 'Boolean':
          if (typeof value !== 'boolean') {
            return Promise.reject(new Error('Please enter true or false'))
          }
          break
        case 'Date':
        case 'Datetime':
          if (!value) {
            return Promise.reject(new Error('Please select a date'))
          }
          break
        case 'Document':
        case 'Image':
          if (!value || !(value instanceof File)) {
            return Promise.reject(
              new Error(`Please upload a ${type.toLowerCase()}`)
            )
          }
          break
      }
      return Promise.resolve()
    },
  },
]

export const getInitialValueForType = (type: SmartApiValueType) => {
  if (type.startsWith('Array<')) {
    return undefined
  }

  switch (type) {
    case 'Boolean':
      return false
    case 'String':
      return ''
    case 'Number':
    case 'Integer':
    case 'Date':
    case 'Datetime':
    case 'Document':
    case 'Image':
    case 'Object':
      return undefined
    default:
      return ''
  }
}
