import SearchOutlined from '@ant-design/icons/SearchOutlined'
import {
  Badge,
  Button,
  Dropdown,
  Flex,
  Input,
  Select,
  Space,
  Typography,
} from 'antd'
import Filter from 'assets/images/filter.svg'
import Sort from 'assets/images/sort.svg'
import classNames from 'classnames'
import { isEmpty } from 'lodash-es'
import React from 'react'
import { useFilterDropdown } from './hooks/use_filter_dropdown'
import { useSortDropdown } from './hooks/use_sort_dropdown'
import styles from './index.module.scss'
import type { FilterOptionsGroup, FilterState, SortOption } from './types'

interface FilterBarProps {
  sortOptions?: readonly SortOption[]
  filterOptions?: readonly FilterOptionsGroup[]
  onChange: (values: Partial<FilterState>) => Promise<URLSearchParams> | void
  value: FilterState
}

const FilterBar: React.FC<FilterBarProps> = ({
  onChange: propOnChange,
  value: propValue,
  sortOptions,
  filterOptions,
}) => {
  const {
    selectedSortKeys,
    sortDropdownOpen,
    sortMenuItems,
    handleSortDropdownOpenChange,
    handleSortMenuItemClick,
  } = useSortDropdown({
    sortOptions,
    value: propValue,
    onChange: propOnChange,
  })

  const {
    handleFilterClear,
    handleFilterDeselect,
    handleFilterSelect,
    getFilterOption,
  } = useFilterDropdown({
    filterOptions,
    value: propValue,
    onChange: propOnChange,
  })

  return (
    <Space>
      <Input
        placeholder="Search"
        value={propValue.search}
        allowClear
        prefix={<SearchOutlined />}
        onChange={(e) => propOnChange({ ...propValue, search: e.target.value })}
      />
      <Dropdown
        menu={{ items: [] }}
        trigger={['click']}
        overlayClassName={styles.filterDropdown}
        dropdownRender={() => (
          <Flex className={styles.contentStyle} vertical>
            {filterOptions?.map((optionGroup) => (
              <Space
                direction="vertical"
                className={styles.item}
                key={optionGroup.key}
              >
                <Typography className={styles.itemTitle}>
                  {optionGroup.label}
                </Typography>
                <Select
                  style={{ display: 'block' }}
                  value={propValue.filter?.[optionGroup.key]}
                  onClear={() => handleFilterClear(optionGroup)}
                  onDeselect={(deselectValue) =>
                    handleFilterDeselect(optionGroup, deselectValue)
                  }
                  onSelect={(selectValue) =>
                    handleFilterSelect(optionGroup, selectValue)
                  }
                  mode="multiple"
                  filterOption={getFilterOption}
                  allowClear
                  options={optionGroup.options}
                />
              </Space>
            ))}
          </Flex>
        )}
      >
        <Badge
          count={Object.keys(propValue.filter ?? {}).length}
          color="blue"
          size="small"
        >
          <Button
            icon={
              <Filter
                className={classNames({
                  [styles.colorPrimary]: !isEmpty(propValue.filter),
                })}
              />
            }
          />
        </Badge>
      </Dropdown>

      {sortOptions ? (
        <Dropdown
          trigger={['click']}
          open={sortDropdownOpen}
          onOpenChange={handleSortDropdownOpenChange}
          overlayClassName={styles.sortDropdown}
          menu={{
            items: sortMenuItems,
            onClick: handleSortMenuItemClick,
            selectable: true,
            selectedKeys: selectedSortKeys,
            style: { minWidth: 250 },
          }}
        >
          <Button
            icon={
              <Sort
                className={classNames({
                  [styles.colorPrimary]: !isEmpty(propValue.sort),
                })}
              />
            }
          />
        </Dropdown>
      ) : null}
    </Space>
  )
}

export default FilterBar
