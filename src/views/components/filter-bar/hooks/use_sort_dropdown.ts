import type { DropdownProps, MenuProps } from 'antd'
import { useState } from 'react'
import type { FilterState, SortOption } from '../types'
import { toSortMenuItems, toSortValue } from '../utils'

interface UseSortDropdownParams {
  sortOptions?: readonly SortOption[]
  value: FilterState
  onChange: (values: Partial<FilterState>) => Promise<URLSearchParams> | void
}

export function useSortDropdown({
  sortOptions,
  value,
  onChange,
}: UseSortDropdownParams) {
  const [sortDropdownOpen, setSortDropdownOpen] = useState(false)

  const sortMenuItems: MenuProps['items'] =
    sortOptions?.map(toSortMenuItems) || []

  const selectedSortKeys = value.sort
    ? Object.entries(value.sort).map(([key, order]) => `${key}|${order}`)
    : []

  const handleSortDropdownOpenChange: DropdownProps['onOpenChange'] = (
    nextOpen,
    info
  ) => {
    if (info.source === 'trigger' || nextOpen) {
      setSortDropdownOpen(nextOpen)
    }
  }

  const handleSortMenuItemClick: MenuProps['onClick'] = ({ key }) => {
    if (!sortOptions) return
    const getGroupName = (key: string) => key.split('|')[0]
    // Remove selection if user click on selected item
    if (selectedSortKeys.includes(key)) {
      const newKeys = selectedSortKeys.filter((k) => k !== key)
      onChange({ ...value, sort: toSortValue(newKeys) })
    } else {
      // Make sure that each group can only have one selected item, e.g. either 'name|asc' or 'name|desc'
      const groupName = getGroupName(key) // e.g 'name|asc', group will be 'name'
      const newKeys = selectedSortKeys.filter(
        (k) => getGroupName(k) !== groupName
      )
      onChange({ ...value, sort: toSortValue([...newKeys, key]) })
    }
  }

  return {
    selectedSortKeys,
    sortDropdownOpen,
    sortMenuItems,
    handleSortDropdownOpenChange,
    handleSortMenuItemClick,
  }
}
