import {
  CheckCircleOutlined,
  Clock<PERSON>ircleOutlined,
  Exclamation<PERSON>ircleOutlined,
  LoadingOutlined,
} from '@ant-design/icons'
import { Flex, Typography } from 'antd'
import { NodeStatusNodeStatus } from 'api/data-contracts'
import cls from 'classnames'
import styles from './index.scss'

export type WorkflowNodeInfo = {
  nodeId: string
  nodeName: string
  nodeType: string
  startTime: string
  endTime: string
  status: NodeStatusNodeStatus
  logs: string
  details: string
  durationInMilliseconds: number
}

const getNodeStatusIcon = (status: NodeStatusNodeStatus) => {
  let icon = <></>
  switch (status) {
    case NodeStatusNodeStatus.Success:
      icon = <CheckCircleOutlined style={{ color: '#199459' }} />
      break
    case NodeStatusNodeStatus.Failed:
      icon = <ExclamationCircleOutlined style={{ color: '#D94032' }} />
      break
    case NodeStatusNodeStatus.Processing:
      icon = <LoadingOutlined style={{ color: '#3278D9' }} />
      break
    case NodeStatusNodeStatus.Initiating:
      icon = <ClockCircleOutlined />
      break
  }
  return icon
}

export interface WorkflowExecutionNodeProps {
  data: WorkflowNodeInfo
  index: number
  onLogsClick: () => void
}

export function WorkflowExecutionNode({
  data,
  index,
  onLogsClick,
}: WorkflowExecutionNodeProps) {
  return (
    <Flex
      className={cls({
        [styles.workflowStatusItem]: true,
        [styles.workflowStatusItemSuccess]: data.status === 'success',
        [styles.workflowStatusItemFailed]: data.status === 'failed',
        [styles.workflowStatusItemInitiating]: data.status === 'initiating',
        [styles.workflowStatusItemProcessing]: data.status === 'processing',
      })}
      style={{ width: '100%' }}
    >
      <Flex className="node-no">{index + 1}</Flex>
      <Flex vertical style={{ flex: 1 }}>
        <Typography.Link
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.currentTarget.classList.toggle('active')
              e.preventDefault()
              e.stopPropagation()
            }
          }}
          className="node-body"
          title={`[${data.status}] ${data.nodeName}`}
          onClick={(e) => {
            e.preventDefault()
            e.stopPropagation()
            onLogsClick()
          }}
        >
          <Flex justify="space-between">
            <Flex>
              <Flex className="node-status">
                {getNodeStatusIcon(data.status)}
              </Flex>
              <Flex className="node-name">{data.nodeName}</Flex>
            </Flex>

            <Flex className="node-duration">
              {(data.durationInMilliseconds / 1000).toFixed(2)}s
            </Flex>
          </Flex>
        </Typography.Link>
      </Flex>
    </Flex>
  )
}
