.container {
  background: #191b1e;
  width: 100%;
  height: 100%;
}

.tabs {
  height: 100%;
  :global {
    .ant-tabs-nav {
      margin: 0;
      padding: 0;

      &::before {
        display: none;
      }

      .ant-tabs-tab {
        margin: 0;
        padding: 8px 16px;
        background: transparent;
        border: none;
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        color: #fff;
        opacity: 0.85;

        .ant-tabs-tab-btn {
          transition: none;
        }

        .ant-tabs-tab-btn:active {
          color: #fff !important;
        }
        .ant-tabs-tab-remove:active {
          color: #fff !important;
        }
        &:hover {
          color: #fff;
          opacity: 1;
          font-weight: 700;
        }

        &.ant-tabs-tab-active {
          background: #2a2c2f;
          .ant-tabs-tab-btn {
            color: #fff;
            font-weight: 700;
          }
        }
      }

      .ant-tabs-ink-bar {
        display: none;
      }

      .ant-tabs-extra-content {
        padding-right: 8px;
      }
    }

    .ant-tabs-content {
      height: 100%;
      background: #2a2c2f;
      :global {
        .ant-tabs-tabpane {
          height: 100%;
        }
      }
    }
  }
}

.previewContainer,
.rawContainer {
  height: 100%;
  overflow: auto;
  position: relative;
  color: #d4d4d4;

  pre {
    height: 100%;
    padding-block-end: 24px; // leave some space at the bottom within the code highlighter
  }
}

.actions {
  display: flex;
  gap: 8px;
  padding: 4px;
}

.actionButton {
  width: 32px;
  height: 32px;
  padding: 6px;
  color: rgba(255, 255, 255, 0.6);
  border-radius: 8px;

  &:hover {
    color: #fff !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  &:active {
    color: #fff !important;
    background-color: rgba(255, 255, 255, 0.2) !important;
    transform: scale(0.95);
  }

  :global {
    .anticon {
      font-size: 20px;
    }
  }
}

.rawContent {
  margin: 0;
  font-family: 'Courier New', Courier, monospace;
  color: #fff;
  white-space: pre-wrap;
  word-break: break-all;
}
