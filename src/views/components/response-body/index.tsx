import { CopyOutlined, ReloadOutlined } from '@ant-design/icons'
import { But<PERSON>, Tabs } from 'antd'
import cls from 'classnames'
import AsyncButton from 'components/async-button'
import { useCallback, useState } from 'react'
import {
  Light as SyntaxHighlighter,
  type SyntaxHighlighterProps,
} from 'react-syntax-highlighter'
import json from 'react-syntax-highlighter/dist/esm/languages/hljs/json'
import styles from './index.module.scss'

import atomOneDark from 'react-syntax-highlighter/dist/esm/styles/hljs/atom-one-dark'

SyntaxHighlighter.registerLanguage('json', json)

interface ResponseBodyProps {
  response: unknown
  onReload?: () => Promise<void>
  placeholder?: string
  wrapperClassName?: string
  syntaxHighlighterProps?: Omit<SyntaxHighlighterProps, 'children'>
}

type TabKey = 'preview' | 'raw'

const getResponseText = (response: unknown, formatted = false): string => {
  if (typeof response === 'string') {
    return response
  }

  if (!response) {
    return ''
  }

  return formatted
    ? JSON.stringify(response, null, 2)
    : JSON.stringify(response)
}

const ResponseBody = ({
  response,
  onReload,
  syntaxHighlighterProps,
  wrapperClassName,
}: Readonly<ResponseBodyProps>) => {
  const [activeTab, setActiveTab] = useState<TabKey>('preview')

  const handleCopy = useCallback(async () => {
    if (!response) {
      return
    }

    try {
      const textToCopy = getResponseText(response, activeTab === 'preview')
      await navigator.clipboard.writeText(textToCopy)
    } catch (error) {
      console.error('Failed to copy text:', error)
    }
  }, [response, activeTab])

  const rawResponseText = getResponseText(response)
  const formattedResponseText = getResponseText(response, true)

  const items = [
    {
      key: 'preview',
      label: 'Preview',
      children: (
        <div className={styles.previewContainer}>
          <SyntaxHighlighter
            wrapLongLines
            language="json"
            style={atomOneDark}
            customStyle={{
              margin: 0,
              background: 'transparent',
              paddingBlockEnd: '24px',
            }}
            showLineNumbers
            lineNumberStyle={{
              color: 'rgba(255, 255, 255, 0.45)',
              paddingRight: '16px',
            }}
            {...syntaxHighlighterProps}
          >
            {formattedResponseText}
          </SyntaxHighlighter>
        </div>
      ),
    },
    {
      key: 'raw',
      label: 'Raw',
      children: (
        <div className={styles.rawContainer}>
          <pre className={styles.rawContent}>{rawResponseText}</pre>
        </div>
      ),
    },
  ]

  return (
    <div className={cls(styles.container, wrapperClassName)}>
      <Tabs
        className={styles.tabs}
        defaultActiveKey="preview"
        activeKey={activeTab}
        onChange={(key) => setActiveTab(key as TabKey)}
        tabBarGutter={4}
        items={items}
        tabBarExtraContent={{
          right: (
            <div className={styles.actions}>
              <Button
                type="text"
                icon={<CopyOutlined />}
                onClick={handleCopy}
                className={styles.actionButton}
              />
              {onReload && (
                <AsyncButton
                  type="text"
                  icon={<ReloadOutlined />}
                  onClick={onReload}
                  className={styles.actionButton}
                />
              )}
            </div>
          ),
        }}
      />
    </div>
  )
}

export default ResponseBody
