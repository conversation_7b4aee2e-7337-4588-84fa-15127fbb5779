.message {
  &-wrapper {
    display: flex;
    flex-flow: row nowrap;
    padding-top: 16px;
    margin-bottom: 16px;
  }

  &-reverse-direction {
    flex-flow: row-reverse nowrap;
  }

  &-avatar {
    display: inline-block;
    width: 100%;
    height: 100%;
    background: url('/assets/images/ste-mini.png') no-repeat;
    background-size: contain;

    &-wrapper {
      border-radius: 50%;
    }
  }

  &-content {
    & > p {
      margin: 0 0 8px;
    }

    &-wrapper {
      width: 100%;
      padding: 0 8px;
    }

    &-user > p {
      margin: 0;
    }

    &-operations {
      display: flex;
    }

    &-feedback-wrapper {
      margin-block-start: 24px;

      :global {
        .feedback-results-wrapper {
          color: #2a2c2f;
        }
      }
    }
  }
}
