.message-card-file {
  &-wrapper {
    display: flex;
    flex-flow: row-reverse wrap;
  }

  &-content {
    box-sizing: border-box;
    display: flex;
    flex-flow: column wrap;
    justify-content: space-between;
    width: 240px;
    height: 140px;
    padding: 24px;
    border-radius: 16px;
    border: 1px solid #d1d1d1;
    background-color: white;

    &-info {
      width: 100%;
      display: flex;
      flex-flow: column wrap;
      gap: 4px;
    }
  }

  &-icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    background-image: url('/assets/images/file2.svg');
    background-repeat: no-repeat;

    &-wrapper {
      width: 24px;
      height: 24px;
      padding: 8px;
      background-color: white;
      border-radius: 8px;
    }
  }

  &-name {
    font-size: 18px;
    font-weight: 600;
    line-height: 21.6px;
  }

  &-type {
    font-size: 14px;
    font-weight: 400;
    line-height: 16.8px;
  }
}
