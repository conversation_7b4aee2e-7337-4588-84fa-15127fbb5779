import { memo } from 'react'
import cls from 'classnames'

import styles from './index.scss'
import { Typography } from 'antd'

type MessageFileCardType = {
  name: string
  type: string
  wrapperClassName?: string
}

const MessageFileCard = (props: MessageFileCardType) => {
  const { name, type, wrapperClassName } = props
  return (
    <div
      className={cls(
        wrapperClassName,
        styles.messageCardFileWrapper,
        'message-card-file-wrapper'
      )}
    >
      <div
        className={cls(
          styles.messageCardFileContent,
          'message-card-file-content'
        )}
      >
        <div
          className={cls(
            styles.messageCardFileIconWrapper,
            'message-card-file-icon-wrapper'
          )}
        >
          <i className={styles.messageCardFileIcon} />
        </div>
        <div className={styles.messageCardFileContentInfo}>
          <Typography.Text ellipsis className={styles.messageCardFileName}>
            {name}
          </Typography.Text>
          <div className={styles.messageCardFileType}>{type}</div>
        </div>
      </div>
    </div>
  )
}

export default memo(MessageFileCard)
