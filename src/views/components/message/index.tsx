import { memo, useEffect, useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, message as AntMessage } from 'antd'
import {
  LoadingOutlined,
  LikeOutlined,
  DislikeOutlined,
  CopyOutlined,
} from '@ant-design/icons'
import copy from 'clipboard-copy'
import cls from 'classnames'
import CustomMarkdown from 'components/custom-markdown'
import PublicChatStore from 'stores/public-chat'
import { Conversation as MessageItemType } from 'stores/models/public-chat'
import Feedback from 'views/components/feedback'

import styles from './index.scss'
import MessageFileCard from './cards/file'
import { ChatPageExtraDataType } from 'views/new-chat'
import { v1ChatConversationsFeedbackCreate } from 'api/PublicApi'

type MessageType = {
  message: MessageItemType
  idx?: number
  onUpdate?: (msg: MessageItemType, idx?: number) => void
  feedback?: boolean
  extraData?: ChatPageExtraDataType
  showFileCard?: boolean
}

const Message = (props: MessageType) => {
  const { message, idx, feedback, showFileCard, extraData, onUpdate } = props
  const { id, role, like, unlike, content, document, isLoading } = message
  const {
    feedbackOperationLike,
    feedbackOperationCancelLike,
    feedbackOperationUnlike,
    feedbackOperationCancelUnlike,
    feedbackOperationLikeOptions,
    feedbackOperationUnlikeOptions,
    feedbackOperationPlaceholder,
  } = extraData ?? {}
  const isUser = role === 'user'

  const [isLikeLoading, setIsLikeLoading] = useState(false)
  const [isUnlikeLoading, setIsUnlikeLoading] = useState(false)
  const [isCopyLoading, setIsCopyLoading] = useState(false)
  const [isOpenFeedbackForm, setIsOpenFeedbackForm] = useState(false)
  const [curFeedback, setCurFeedback] = useState<'like' | 'unlike'>('like')

  const handleResponseClick = async (type: 'like' | 'unlike') => {
    const isLike = type === 'like'
    setCurFeedback(type)

    let setLoadingFunction = isLike ? setIsLikeLoading : setIsUnlikeLoading
    let newStatus = isLike ? !like : !unlike
    if (!(newStatus || (isLike ? unlike : like))) setIsOpenFeedbackForm(false)
    else setIsOpenFeedbackForm(true)

    setLoadingFunction(true)

    if (isLike) {
      PublicChatStore.like(id, newStatus)
        .catch((error) => {
          console.error('Error disliking/undisliking:', error)
          PublicChatStore.like(id, newStatus)
        })
        .finally(() => setLoadingFunction(false))
    } else {
      PublicChatStore.unlike(id, newStatus)
        .catch((error) => {
          console.error('Error disliking/undisliking:', error)
          PublicChatStore.unlike(id, newStatus)
        })
        .finally(() => setLoadingFunction(false))
    }
  }

  const handleCopyClick = () => {
    setIsCopyLoading(true)
    copy(content)
      .then(() => {
        AntMessage.success('Content copied to clipboard')
      })
      .catch(() => AntMessage.error('Failed to copy content'))
      .finally(() => setIsCopyLoading(false))
  }

  useEffect(() => {
    onUpdate?.(message, idx)
  }, [message.isLoading])

  return (
    <>
      {showFileCard && isUser && document?.document_id && (
        <MessageFileCard
          wrapperClassName={styles.messageAttachmentWrapper}
          name={document?.file_name}
          type={document?.file_name
            ?.split('.')
            .slice(-1)
            .join('')
            .toUpperCase()}
        />
      )}
      {!content && isUser ? null : (
        <div
          className={cls({
            'message-wrapper': true,
            'message-reverse-direction': isUser,
            [styles.messageWrapper]: true,
            [styles.messageReverseDirection]: isUser,
          })}
        >
          <div
            className={cls({
              'message-avatar-wrapper': true,
              'message-avatar-assistant': !isUser,
              'message-avatar-user': isUser,
              [styles.messageAvatarWrapper]: true,
            })}
          >
            <i
              className={cls({
                'message-avatar': true,
                [styles.messageAvatar]: true,
              })}
            />
          </div>

          <div
            className={cls({
              'message-content-wrapper': true,
              'message-content-user': isUser,
              'message-content-assistant': !isUser,
              [styles.messageContentWrapper]: true,
            })}
          >
            <div
              className={cls({
                'message-content': true,
                [styles.messageContentUser]: isUser,
                [styles.messageContent]: true,
              })}
            >
              <CustomMarkdown>{content}</CustomMarkdown>
            </div>

            {isLoading && <LoadingOutlined />}
            {!isUser && id && (
              <div
                className={cls(
                  styles.messageContentOperations,
                  'message-content-operations'
                )}
              >
                <Tooltip
                  title={
                    like ? feedbackOperationCancelLike : feedbackOperationLike
                  }
                  overlayClassName={cls(
                    styles.messageContentOperationsTooltipLike,
                    'message-content-operations-tooltip-like'
                  )}
                >
                  <Button
                    type={like ? 'primary' : 'text'}
                    icon={<LikeOutlined />}
                    onClick={() => handleResponseClick('like')}
                    loading={isLikeLoading}
                    className={cls(
                      styles.messageContentOperationsLike,
                      'message-content-operations-like',
                      'message-content-operations-btn'
                    )}
                  />
                </Tooltip>
                <Tooltip
                  title={
                    unlike
                      ? feedbackOperationCancelUnlike
                      : feedbackOperationUnlike
                  }
                  overlayClassName={cls(
                    styles.messageContentOperationsTooltipUnlike,
                    'message-content-operations-tooltip-unlike'
                  )}
                >
                  <Button
                    type={unlike ? 'primary' : 'text'}
                    icon={<DislikeOutlined />}
                    onClick={() => handleResponseClick('unlike')}
                    loading={isUnlikeLoading}
                    className={cls(
                      styles.messageContentOperationsUnlike,
                      'message-content-operations-unlike',
                      'message-content-operations-btn'
                    )}
                  />
                </Tooltip>
                <Tooltip
                  title={'Copy'}
                  overlayClassName={cls(
                    styles.messageContentOperationsTooltipCopy,
                    'message-content-operations-tooltip-copy'
                  )}
                >
                  <Button
                    type={'text'}
                    icon={<CopyOutlined />}
                    onClick={handleCopyClick}
                    loading={isCopyLoading}
                    className={cls(
                      styles.messageContentOperationsCopy,
                      'message-content-operations-copy',
                      'message-content-operations-btn'
                    )}
                  />
                </Tooltip>
              </div>
            )}
            {feedback && !isUser && id && (
              <Feedback
                open={isOpenFeedbackForm}
                onClose={() => setIsOpenFeedbackForm(false)}
                wrapperClassName={cls({
                  'message-content-feedback-wrapper': true,
                  [styles.messageContentFeedbackWrapper]: true,
                })}
                options={
                  (curFeedback === 'like'
                    ? feedbackOperationLikeOptions?.split(',').map((i) => {
                        const [label, value] = i.split('|')
                        return { label, value }
                      })
                    : feedbackOperationUnlikeOptions?.split(',').map((i) => {
                        const [label, value] = i.split('|')
                        return { label, value }
                      })) ?? []
                }
                placeHolder={feedbackOperationPlaceholder}
                onSubmit={async ({ feedback, resultList }) => {
                  await v1ChatConversationsFeedbackCreate({
                    feedback: feedback,
                    feedbackLabel: JSON.stringify(resultList),
                    id: message.id,
                  })
                }}
              />
            )}
          </div>
        </div>
      )}
    </>
  )
}

export default memo(Message)
