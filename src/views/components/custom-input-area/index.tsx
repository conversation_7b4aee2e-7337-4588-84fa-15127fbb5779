import { Button, Input, Tooltip, Upload, UploadFile, UploadProps } from 'antd'
import { TextAreaProps } from 'antd/es/input'
import cls from 'classnames'
import { CSSProperties, memo, useMemo, useState } from 'react'

import { v1ChatConversationsUploadDocumentCreate } from 'api/PublicApi'
import StaticFunction from 'components/antd-static-function'
import { supportedFileTypes } from 'constants/common'
import { useParams } from 'react-router'
import PublicChatStore from 'stores/public-chat'
import { getApiErrorMessage } from 'utils/axios/api-error-message'
import { formatFileSize } from 'utils/filter'
import { ChatQueryType } from 'views/new-chat/types'
import InputAreaFileCard from './components/file-card'
import styles from './index.scss'

const { TextArea } = Input

type CustomInputAreaType = {
  inputRef?: React.RefObject<HTMLTextAreaElement>

  sending?: boolean
  onSend?: (...args: any[]) => boolean | undefined
  onInputChange?: React.Dispatch<React.SetStateAction<string>>
  inputProps?: TextAreaProps
  uploadProps?: UploadProps &
    Partial<{
      open: boolean
      maxFileSize?: number
      enableFileTypeCheck?: boolean
    }>
  wrapperClassName?: string
}

const formatFileExtensions = (acceptString?: string): string => {
  if (!acceptString) return ''

  const formattedExtensions = acceptString
    .split(',')
    .map((ext) => ext.trim().replace(/^\./, '').toUpperCase())

  if (formattedExtensions.length <= 1) {
    return formattedExtensions.join('')
  }

  const lastItem = formattedExtensions.pop()
  return formattedExtensions.join(', ') + ' and ' + lastItem
}

const CustomInputArea = (props: CustomInputAreaType) => {
  const {
    onSend,
    onInputChange,
    inputRef,
    sending,
    inputProps,
    wrapperClassName,
    uploadProps,
  } = props
  const { notification } = StaticFunction
  const { uuid } = useParams<ChatQueryType>()
  const [uploadFileList, setUploadFileList] = useState<UploadFile[]>([])
  const gridTemplate = useMemo<CSSProperties['gridTemplate']>(() => {
    const isUpload = !!uploadFileList.length

    const gridTemplateStyle = []
    if (isUpload) gridTemplateStyle.push('upload-area upload-area')
    gridTemplateStyle.push('input-area input-area')
    gridTemplateStyle.push('left-tools right-tools')

    const str =
      gridTemplateStyle.reduce((pre, cur) => {
        pre += `"${cur}" `
        return pre
      }, '') + ` / 1fr auto`
    return str
  }, [!!uploadFileList.length, !!inputProps?.value])

  const handleUploadListChange: UploadProps['onChange'] = (info) => {
    let newFileList = info.fileList
    if (uploadProps?.maxCount !== undefined) {
      newFileList = newFileList.slice(-uploadProps.maxCount)
    }
    setUploadFileList(newFileList)
  }

  const handleUploadCustomRequest: UploadProps['customRequest'] = (option) => {
    const { file, onSuccess, onError, onProgress } = option
    if (!(file instanceof File)) return
    if (!uuid) return

    v1ChatConversationsUploadDocumentCreate(
      {
        file,
        channel_uuid: uuid,
        session_uuid: PublicChatStore.sessionUUID,
      },
      {
        onUploadProgress: (progressEvent) => {
          const { loaded, total } = progressEvent
          if (!total) return
          onProgress?.({ percent: Math.round((loaded * 100) / total) })
        },
        timeout: 60 * 1000,
      }
    )
      .then((uploadResp) => {
        const { id: documentId } = uploadResp.data
        onSuccess?.({ documentId, msg: 'success' })
      })
      .catch((err) => {
        onError?.(err, err)
      })
  }

  const handleUploadDeleteClick = (file: UploadFile) => {
    setUploadFileList((prev) => prev.filter((f) => f.uid !== file.uid))
  }

  const handleSendMessage = () => {
    if (uploadFileList.some((f) => f.status !== 'done')) {
      // Find the first file with an error to display its status text
      const fileWithError = uploadFileList.find(
        (f) => f.status === 'error' && f.error
      )

      notification.error({
        message: 'Send Message Error',
        description:
          getApiErrorMessage(fileWithError?.error?.response?.data) ??
          'Please upload the file again',
      })
      return
    }

    const file = uploadFileList[0]

    const isSent = onSend?.({
      document: {
        document_id: file?.response?.documentId,
        file_name: file?.name,
      },
    })

    if (isSent) {
      setUploadFileList([])
    }
  }

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key !== 'Enter') return
    if (e.metaKey || e.ctrlKey) {
      onInputChange?.((prev) => prev + '\n')
    } else {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleInputChange: React.ChangeEventHandler<HTMLTextAreaElement> = (
    e
  ) => {
    onInputChange?.(e.target.value)
  }

  const validateFileSize = (file: UploadFile, maxFileSize?: number | null) => {
    if (maxFileSize == null) return true // no limit to file size
    if (file.size && file.size > maxFileSize) {
      const maxSizeMB = formatFileSize(maxFileSize)
      notification.error({
        message: `Failed to process document: file size exceeds the maximum allowed size of ${maxSizeMB}`,
      })
      return false
    }
    return true
  }

  const validateFileType = (
    file: UploadFile,
    accept?: string,
    enableFileTypeCheck?: boolean
  ) => {
    if (!accept) return true // if accept is not set, it means all file types are supported
    if (!enableFileTypeCheck) return true
    const fileName = file.name || ''
    const fileExtension = fileName.split('.').pop()?.toLowerCase() || ''
    const acceptedExtensions = accept
      .split(',')
      .map((ext) => ext.trim().replace(/^\./g, '').toLowerCase())
    if (!acceptedExtensions.includes(fileExtension)) {
      const formattedExtensions = formatFileExtensions(accept)
      notification.error({
        message: `Failed to process document: only ${formattedExtensions} files are supported`,
      })
      return false
    }
    return true
  }

  return (
    <div
      className={cls(wrapperClassName, {
        'custom-input-area-wrapper': true,
        [styles.customInputAreaWrapper]: true,
      })}
      style={{ gridTemplate }}
    >
      {!!uploadFileList.length && (
        <div className={styles.customInputAreaUploadItemsWrapper}>
          {uploadFileList.map((file, idx) => (
            <InputAreaFileCard
              key={`${file?.name}-${idx}`}
              file={file}
              onDelete={handleUploadDeleteClick}
            />
          ))}
        </div>
      )}
      <TextArea
        ref={inputRef}
        className={cls(
          'custom-input-area-content-input',
          styles.customInputAreaContent
        )}
        onKeyDown={handleInputKeyDown}
        onChange={handleInputChange}
        {...inputProps}
      />
      <div className={styles.customInputAreaToolsWrapper}>
        {inputProps?.maxLength && (
          <span
            className={cls({
              [styles.customInputAreaToolsCount]: true,
              [styles.customInputAreaToolsCountLimited]:
                `${inputProps?.value}`.length === inputProps?.maxLength,
            })}
          >{`${`${inputProps?.value}`.length} / ${inputProps?.maxLength}`}</span>
        )}
        {uploadProps?.open && (
          <Upload
            className={cls(
              'custom-input-area-tools-upload',
              styles.customInputAreaToolsUpload
            )}
            customRequest={handleUploadCustomRequest}
            onChange={handleUploadListChange}
            showUploadList={false}
            accept={supportedFileTypes.map((f) => f.value).join(',')}
            beforeUpload={(file: UploadFile) => {
              let errorMessage = ''
              const fileUid = file.uid
              if (!validateFileSize(file, uploadProps?.maxFileSize)) {
                errorMessage = `File size exceeds the maximum allowed size of ${formatFileSize(uploadProps?.maxFileSize ?? 0)}`
              } else if (
                !validateFileType(
                  file,
                  uploadProps?.accept,
                  uploadProps?.enableFileTypeCheck
                )
              ) {
                errorMessage = `Only ${formatFileExtensions(uploadProps.accept)} files are supported`
              }
              if (errorMessage) {
                setUploadFileList((prev) => {
                  const exists = prev.find((f) => f.uid === fileUid)
                  const errorFile: UploadFile = {
                    ...file,
                    status: 'error',
                    error: { message: errorMessage },
                    percent: 0,
                    uid: fileUid,
                    name: file.name,
                  }
                  if (exists) {
                    return prev.map((f) => (f.uid === fileUid ? errorFile : f))
                  }
                  return [...prev, errorFile]
                })
                return Upload.LIST_IGNORE
              }
              return true
            }}
            fileList={uploadFileList}
            {...uploadProps}
          >
            {uploadProps.accept ? (
              <Tooltip
                title={
                  uploadProps.maxFileSize == null
                    ? `Only support: ${formatFileExtensions(uploadProps.accept)} files only`
                    : `Only support up to ${formatFileSize(uploadProps.maxFileSize)} for: ${formatFileExtensions(uploadProps.accept)} files only`
                }
                className="custom-input-area-tools-upload-tooltip"
              >
                <i
                  className={cls(
                    'custom-input-area-tools-upload-icon',
                    styles.customInputAreaToolsUploadIcon
                  )}
                />
              </Tooltip>
            ) : (
              <i
                className={cls(
                  'custom-input-area-tools-upload-icon',
                  styles.customInputAreaToolsUploadIcon
                )}
              />
            )}
          </Upload>
        )}

        <Button
          icon={
            <i
              className={cls(
                'custom-input-area-tools-send-icon',
                styles.customInputAreaToolsSendIcon
              )}
            />
          }
          loading={sending}
          onClick={handleSendMessage}
          disabled={!inputProps?.value}
          className={cls(
            'custom-input-area-tools-send',
            styles.customInputAreaToolsSend
          )}
        />
      </div>
    </div>
  )
}

export default memo(CustomInputArea)
