import { Typography, Button, UploadFile, Progress, ProgressProps } from 'antd'
import cls from 'classnames'
import { CloseOutlined, DeleteOutlined } from '@ant-design/icons'

import styles from './index.scss'

const { Text } = Typography

type FileCardType = {
  file: UploadFile

  wrapperClassName?: string
  onDelete?: (file: UploadFile) => void
  progressProps?: ProgressProps
}

const InputAreaFileCard = (props: FileCardType) => {
  const { onDelete, wrapperClassName, progressProps, file } = props
  const { name, percent, status } = file

  return (
    <div
      className={cls(wrapperClassName, {
        [styles.inputAreaFileCardWrapper]: true,
        [styles.inputAreaFileCardWrapperError]: status === 'error',
      })}
    >
      <div className={styles.inputAreaFileCardContent}>
        <div
          className={cls(
            'input-area-file-card-icon-wrapper',
            styles.inputAreaFileCardIconWrapper
          )}
        >
          <i
            className={cls(
              'input-area-file-card-icon',
              styles.inputAreaFileCardIcon
            )}
          />
        </div>
        <div className={styles.inputAreaFileCardInfoWrapper}>
          <Text
            className={cls({
              [styles.inputAreaFileCardInfoTitle]: true,
              [styles.inputAreaFileCardInfoTitleError]: status === 'error',
            })}
            ellipsis={{ tooltip: { trigger: 'hover' } }}
          >
            {name}
          </Text>
          <div
            className={cls({
              [styles.inputAreaFileCardInfoType]: true,
              [styles.inputAreaFileCardInfoTypeError]: status === 'error',
            })}
          >
            {name.split('.').slice(-1).join('').toUpperCase()}
          </div>
        </div>
        <Button
          className={cls('input-area-file-card-delete', {
            [styles.inputAreaFileCardDelete]: true,
            [styles.inputAreaFileCardDeleteError]: status === 'error',
          })}
          shape="circle"
          icon={status === 'error' ? <DeleteOutlined /> : <CloseOutlined />}
          onClick={() => onDelete?.(file)}
        />
      </div>

      {status && ['uploading'].includes(status) && (
        <Progress
          size={{ height: 2 }}
          className={styles.inputAreaFileCardProgress}
          showInfo={false}
          percent={percent}
          {...progressProps}
        />
      )}
    </div>
  )
}

export default InputAreaFileCard
