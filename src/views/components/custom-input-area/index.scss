.custom-input-area {
  &-wrapper {
    background-color: white;
    border: 1px solid #d1d1d1;
    border-radius: 16px;
    padding: 16px 24px;

    display: grid;
    align-items: center;

    &-no-upload {
      grid-template-areas: 'input-area right-tools';
      grid-template-columns: 1fr auto;
      grid-template-rows: auto;
    }

    &-no-input {
      grid-template-areas: 'input-area right-tools';
      grid-template-columns: 1fr auto;
      grid-template-rows: auto;
    }
  }

  &-upload-items {
    &-wrapper {
      grid-area: upload-area;

      display: flex;
      flex-flow: row wrap;
      column-gap: 8px;
      row-gap: 4px;
      margin-bottom: 12px;
    }
  }

  &-content {
    word-break: break-all;
    padding: 0;
    grid-area: input-area;
  }

  &-tools {
    &-wrapper {
      grid-area: right-tools;
      display: flex;
      align-items: center;
      column-gap: 8px;
      :global {
        .ant-upload {
          display: flex;
        }
      }
    }

    &-count {
      color: rgba(0, 0, 0, 0.45);

      &-limited {
        color: #ff4d4f;
      }
    }

    &-upload {
      cursor: pointer;
      &-icon {
        display: inline-block;
        width: 24px;
        height: 24px;
        background-image: url('/assets/images/clip.svg');
        background-repeat: no-repeat;
      }
    }
    &-send {
      border: unset;

      &-icon {
        display: inline-block;
        vertical-align: middle;
        width: 24px;
        height: 24px;
        background-image: url('/assets/images/arrow.svg');
        background-repeat: no-repeat;
      }

      &[disabled] {
        background: transparent;
        cursor: not-allowed;

        .custom-input-area-tools-send-icon {
          opacity: 0.25;
        }

        &:hover {
          background: transparent;
        }
      }
    }
  }
}
