.custom-carousel {
  &-wrapper {
    position: relative;
    cursor: pointer;
  }

  &-content {
    width: fit-content;
    display: flex;
    min-height: 100px;

    &-wrapper {
      overflow: auto;
      &::-webkit-scrollbar {
        display: none;
      }
    }
  }

  &-control {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);

    display: flex;
    align-items: center;
    justify-content: center;

    width: 28px;
    height: 28px;

    background-color: #fff;
    border-radius: 50%;

    border: 1px solid #d1d1d1;

    &-left {
    }

    &-right {
      right: 0;
    }

    &-hidden {
      display: none;
    }
  }
}
