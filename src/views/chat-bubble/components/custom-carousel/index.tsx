import React, { memo, useEffect, useMemo, useRef, useState } from 'react'
import cls from 'classnames'
import { LeftOutlined, RightOutlined } from '@ant-design/icons'

import styles from './index.module.scss'

type CustomCarouselType = {
  children?: React.ReactNode
}

const OFFSET = 160

const CustomCarousel = (props: CustomCarouselType) => {
  const { children } = props

  const [leftOffsetPos, setLeftOffsetPos] = useState(0)
  const [needScrollControls, setNeedScrollControls] = useState(false)

  const carouselWrapperRef = useRef<HTMLDivElement>(null)
  const carouselContentRef = useRef<HTMLDivElement>(null)
  const scrollEventRef = useRef({
    ticking: false,
  })

  useEffect(() => {
    if (carouselContentRef.current && carouselWrapperRef.current) {
      setNeedScrollControls(
        carouselContentRef.current?.scrollWidth >
          carouselWrapperRef.current?.scrollWidth
      )
    }
  }, [])

  const isScrollEnd = useMemo(() => {
    if (carouselContentRef.current) {
      return (
        carouselContentRef.current.scrollLeft +
          carouselContentRef.current.clientWidth ===
        carouselContentRef.current?.scrollWidth
      )
    }
    return false
  }, [leftOffsetPos])

  const handleScroll = () => {
    if (!scrollEventRef.current.ticking) {
      window.requestAnimationFrame(() => {
        setLeftOffsetPos(carouselContentRef.current?.scrollLeft ?? 0)
        scrollEventRef.current.ticking = false
      })

      scrollEventRef.current.ticking = true
    }
  }

  const handleControlClick = (dir: 'left' | 'right') => {
    const nextPosition = leftOffsetPos + OFFSET * (dir === 'left' ? -1 : 1)
    carouselContentRef.current?.scrollTo({
      left: nextPosition,
      behavior: 'smooth',
    })
  }

  useEffect(() => {
    if (!carouselWrapperRef.current || !carouselContentRef.current) return

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (
          entry.isIntersecting &&
          carouselContentRef.current?.scrollWidth !== undefined &&
          carouselWrapperRef.current?.scrollWidth !== undefined
        )
          setNeedScrollControls(
            carouselContentRef.current?.scrollWidth >
              carouselWrapperRef.current?.scrollWidth
          )
      })
    }, {})

    observer.observe(carouselWrapperRef.current)
    carouselContentRef.current?.addEventListener('scroll', handleScroll)

    return () => {
      carouselContentRef.current?.removeEventListener('scroll', handleScroll)
      observer.disconnect()
    }
  }, [])

  return (
    <div className={styles.customCarouselWrapper} ref={carouselWrapperRef}>
      {/* Control left */}
      <div
        className={cls({
          [styles.customCarouselControl]: true,
          [styles.customCarouselControlLeft]: true,
          [styles.customCarouselControlHidden]: leftOffsetPos === 0,
        })}
        onClick={() => handleControlClick('left')}
        role="button"
        tabIndex={0}
        onKeyDown={() => {}}
      >
        <LeftOutlined />
      </div>

      {/* Content */}
      <div
        className={styles.customCarouselContentWrapper}
        ref={carouselContentRef}
      >
        <div className={styles.customCarouselContent}>{children}</div>
      </div>

      {/* Control right */}
      <div
        className={cls({
          [styles.customCarouselControl]: true,
          [styles.customCarouselControlRight]: true,
          [styles.customCarouselControlHidden]:
            !needScrollControls || isScrollEnd,
        })}
        onClick={() => handleControlClick('right')}
        role="button"
        tabIndex={0}
        onKeyDown={() => {}}
      >
        <RightOutlined />
      </div>
    </div>
  )
}

export default memo(CustomCarousel)
