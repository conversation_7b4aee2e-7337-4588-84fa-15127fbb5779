import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd'
import cls from 'classnames'
import StaticFunction from 'components/antd-static-function'
import dayjs from 'dayjs'
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useParams } from 'react-router-dom'

import { Conversation, WorkflowNode } from 'stores/models/public-chat'
import PublicChatStore from 'stores/public-chat'

import { useMessageWorker } from 'hooks/use-message-worker'
import { safeJsonParse } from 'utils/common'
import { MessageWorkerMessage } from 'utils/worker/message'
import { BaseChatExtraDataType, ChatQueryType } from 'views/new-chat/types'
import Message from '../components/message'
import MsgCard from '../components/msg-card'
import CustomCarousel from './components/custom-carousel'
import Navbar from './components/navbar'

import { useCreateHtmlScript } from 'hooks/use-create-html-script'
import { assignIn } from 'lodash-es'
import CustomInputArea from 'views/components/custom-input-area'
import { useChatInputProps } from 'views/new-chat/hooks/use-chat-input-props'
import styles from './index.scss'

export type ChatBubbleExtraDataType = BaseChatExtraDataType & {
  cssChatBubble: string
  iframeOrigin: string
  iframeWidth: string
  iframeHeight: string
  [key: string]: any
}

const ChatBubble = () => {
  const { uuid } = useParams<ChatQueryType>()
  const { notification } = StaticFunction

  const [loading, setLoading] = useState<boolean>(true)
  const [sending, setSending] = useState<boolean>(false)
  const [started, setStarted] = useState<boolean>(false)
  // first time open the bubble page
  const [firstConversation, setFirstConversation] = useState(true)
  const [extraData, setExtraData] = useState<ChatBubbleExtraDataType>()
  const [startNode, setStartNode] = useState<WorkflowNode | null>(null)
  const [options, setOptions] = useState<string[]>([])
  const [userInput, setUserInput] = useState('')
  const [isTCModalVisible, setIsTCModalVisible] = useState(true)

  const extraCustomVariables = useRef<Record<string, any>>()
  const textAreaRef = useRef<HTMLTextAreaElement>(null)
  const conversionRef = useRef<HTMLDivElement>(null)
  const documentRef = useRef<{ document_id: string; file_name: string }>()

  const isExceedLimited = useMemo(() => {
    if (
      extraData?.maxCharLength === undefined ||
      extraData?.maxCharLength === 0
    )
      return false

    const maxCharLength = Number(extraData.maxCharLength)
    return userInput.length >= maxCharLength
  }, [extraData?.maxCharLength, userInput])

  const handleIframeCustomVariables = (e: MessageEvent) => {
    // filter default event message
    if (e.data?.type === `webpackOk`) return

    const { payload }: MessageWorkerMessage = e.data || {}

    if (
      !extraCustomVariables.current &&
      payload?.type === `initCustomVariables`
    )
      extraCustomVariables.current = payload?.data ?? {}
  }
  useMessageWorker(handleIframeCustomVariables)

  const handleSendMessage = useCallback(
    (param?: Partial<Conversation>) => {
      if (sending) return false
      if (userInput.trim() === '' && !param?.document?.document_id) {
        notification.error({
          message: 'Send Message Error',
          description: 'Please enter a prompt before sending',
        })
        return false
      }

      if (isExceedLimited) {
        notification.error({
          message: 'Send Message Error',
          description:
            'Message exceeds character limit. Please shorten your message',
        })
        return false
      }

      if (param?.document?.document_id) {
        documentRef.current = param?.document
      }

      const content = userInput
      setUserInput('')
      setSending(true)
      if (!started) {
        setStarted(true)
        setFirstConversation(false)
      }
      PublicChatStore.send(
        assignIn(param, { document: documentRef.current }, { content }),
        { uuid }
      )
        .then(() => {
          setSending(false)
        })
        .catch((error) => {
          notification.error({
            message: 'Send Message Error',
            description: error,
          })
          setSending(false)
        })
      return true
    },
    [documentRef.current, userInput, isExceedLimited, sending]
  )

  // 1. auto-increment
  // 2. update callback
  const handleScrollToNewMessage = () => {
    conversionRef?.current?.scrollTo({
      behavior: 'smooth',
      top: conversionRef?.current?.scrollHeight,
    })
  }

  const handleMsgCardClick = (msg: string) => {
    setUserInput(msg)
    textAreaRef.current?.focus()
  }

  useEffect(handleScrollToNewMessage, [PublicChatStore.conversations.length])

  useEffect(() => {
    if (!uuid) return
    PublicChatStore.GetChannel(uuid)
      .then((res) => {
        if (!res) throw new Error('No response from server')
        const { title, nodes, extraData: extraDataStr } = res.data

        document.title = title ?? ''
        // find the start node of agent
        const curStartNode = nodes.find(({ type }) => type === 'Start')
        if (!curStartNode) return
        setStartNode(curStartNode)

        // get the extra data
        const curExtraData =
          safeJsonParse<ChatBubbleExtraDataType>(extraDataStr)
        setExtraData(curExtraData)

        // opening questions
        setOptions(curExtraData?.openingQuestions ?? [])

        PublicChatStore.setDefaultConversation([
          {
            id: '',
            sessionUUID: '',
            userID: '',
            organizationID: '',
            content: curExtraData?.conversationOpener ?? '',
            role: 'assistant',
            tokens: 0,
            createdAt: dayjs().toISOString(),
            error: false,
            isLoading: false,
            currentNodeID: '',
            currentNodeLabel: '',
            startTime: '',
            endTime: '',
            like: false,
            unlike: false,
            document: {
              document_id: '',
              file_name: '',
            },
          },
        ])
      })
      .catch((err) => console.error(err))
      .finally(() => setLoading(false))
  }, [uuid])
  useCreateHtmlScript('style', extraData?.cssChatBubble)

  const chatInputProps = useChatInputProps({
    extraData,
    userInput,
    setUserInput,
    sending,
    startNodeData: startNode ?? undefined,
    textAreaRef,
  })

  return (
    <Spin
      spinning={loading}
      tip={'Loading...'}
      className={styles.chatBubbleLoading}
    >
      <div className={cls('chat-bubble-wrapper', styles.chatBubbleWrapper)}>
        {/* Bar */}
        <Navbar
          fqaBtnText={extraData?.faqButtonText}
          fqaContent={extraData?.userGuide}
        />
        {/* Title */}
        <div
          className={cls('chat-bubble-title-wrapper', {
            [styles.chatBubbleTitleWrapper]: true,
            [styles.chatBubbleStartedConversion]: !firstConversation && started,
          })}
        >
          <div
            className={cls(
              'chat-bubble-title-header',
              styles.chatBubbleTitleHeader
            )}
          >
            {extraData?.headerTitle}
          </div>
          <div
            className={cls(
              'chat-bubble-title-header',
              styles.chatBubbleTitleSub
            )}
          >
            {extraData?.subheader}
          </div>
        </div>

        {/* Guidance Group */}
        <div
          className={cls('chat-bubble-guidance-wrapper', {
            [styles.chatBubbleGuidanceWrapper]: true,
            [styles.chatBubbleStartedConversion]: !firstConversation && started,
          })}
        >
          <CustomCarousel>
            {options?.map?.((op, idx) => (
              <MsgCard
                type="text"
                onClick={() => handleMsgCardClick(op)}
                key={`${op}-${idx}`}
                wrapperClassName={cls(
                  'chat-bubble-guidance-card-wrapper',
                  styles.chatBubbleGuidanceCardWrapper
                )}
              >
                {op}
              </MsgCard>
            ))}
          </CustomCarousel>
        </div>

        {/* Chat Box */}
        <div
          ref={conversionRef}
          className={cls(
            styles.chatBubbleChatBoxWrapper,
            'chat-bubble-chat-box-wrapper'
          )}
        >
          {PublicChatStore.conversations?.map((msg, idx) => {
            return (
              <Message
                key={`msg-${msg.id}-${msg.content}-${idx}`}
                message={msg}
                onUpdate={handleScrollToNewMessage}
              />
            )
          })}
        </div>
        {/* InputArea */}
        <div
          className={cls(
            'chat-bubble-input-area-wrapper',
            styles.chatBubbleInputAreaWrapper
          )}
        >
          <CustomInputArea
            {...chatInputProps}
            wrapperClassName={cls('chat-bubble-input-area-exceed-limited', {
              [styles.chatBubbleInputAreaExceedLimited]: isExceedLimited,
            })}
            onSend={handleSendMessage}
          />
        </div>
        {/* Footer */}
        <div className={cls('chat-bubble-footer', styles.chatBubbleFooter)}>
          <div
            dangerouslySetInnerHTML={{
              __html: extraData?.disclaimerText ?? '',
            }}
          />
        </div>

        {/* Modal */}
        <Modal
          closable={false}
          maskClosable={false}
          open={isTCModalVisible}
          footer={
            <Button
              type="primary"
              onClick={() => setIsTCModalVisible(!isTCModalVisible)}
            >
              I Accept
            </Button>
          }
        >
          <div
            dangerouslySetInnerHTML={{
              __html: extraData?.termsAndConditions ?? '',
            }}
          />
        </Modal>
      </div>
    </Spin>
  )
}

export default memo(ChatBubble)
