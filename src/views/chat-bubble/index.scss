.chat-bubble {
  &-wrapper {
    font-family: 'Guardian Sans Regular', sans-serif;
    display: flex;
    flex-flow: column nowrap;
    height: 100vh;
    background: white;
  }

  &-loading {
    max-height: fit-content !important;
    background-color: white;
  }

  &-title {
    &-wrapper {
      padding: 0 16px;
      font-weight: 600;
      color: #f53f3f;
    }

    &-header {
      font-size: 24px;
    }
    &-sub {
      font-size: 22px;
    }
  }

  &-guidance {
    &-wrapper {
      padding: 8px 16px;
    }

    &-card-wrapper {
      border: 1px solid #f98981;
      background-color: #ffece8a1;

      &:not(:first-child) {
        margin-left: 12px;
      }
    }
  }

  &-chat-box {
    &-wrapper {
      flex: 1;
      overflow-x: hidden;
      overflow-y: auto;
      margin: 16px 0px;
      padding: 0 24px;
      word-break: break-word;
    }
  }

  &-input-area {
    padding: 0 2px 0 0;
    grid-area: input-area;
    font-size: 16px;

    &-wrapper {
      min-height: 88px;
      margin: 16px 16px 8px;
    }

    &-content {
      padding: 12px;
      border: 1px solid #d1d1d1;
      border-radius: 16px;

      align-items: flex-end;
      display: grid;
      gap: 8px;
      grid-template-areas:
        'input-area input-area input-area'
        'left-tools . right-tools';
      grid-template-columns: auto 1fr auto;
      grid-template-rows: auto 32px;
    }

    &-btn {
      grid-area: right-tools;
    }
  }

  &-footer {
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 8px;
  }

  // Status Change
  &-started-conversion {
    display: none;
    animation: fadeOutAndCollapse 1s forwards;
  }
}

@keyframes fadeOutAndCollapse {
  0% {
    display: block;
    opacity: 1;
    max-height: fit-content;
  }
  50% {
    opacity: 0;
    max-height: fit-content;
  }
  100% {
    opacity: 0;
    max-height: 0;
  }
}
