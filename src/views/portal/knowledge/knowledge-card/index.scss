.knowledge {
  &-card-wrapper {
    width: 384px;
    height: 163px;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid var(--ant-color-border);

    &:hover {
      box-shadow: 1px 3px 4px 0px var(--ant-color-border);
      cursor: pointer;
    }
  }

  &-avatar {
    background-color: var(--genie-color-bg-container);
    border: none;
  }

  &-name {
    color: var(--ant-color-text);
    font-size: 16px;
    font-weight: 700;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  &-meta-info {
    color: var(--ant-color-text-quaternary);
    font-size: 12px;
  }

  &-menu-button {
    color: #595c5f;
    padding: 10;
  }

  &-description {
    color: var(--ant-color-text-tertiary);
    font-size: 14px;
  }
}
