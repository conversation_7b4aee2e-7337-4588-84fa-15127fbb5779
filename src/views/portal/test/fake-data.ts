import { FormItemType } from 'components/new-dynamic-form/types'

export const fakeData: { data: Record<string, any>; structure: FormItemType } =
  {
    data: {
      section_Nkn4: { switch_oEvO: true },
      valueMaker_CuGq: {
        name: '1',
        dataType: 'String',
        type: 'input',
        value: '2',
      },
      valuePicker_D1Sk: {
        dataType: 'String',
        type: 'input',
        value: '3',
      },
      list_O9R5: [
        { name: '1', inputNumber_4NEk: 2 },
        { name: '3', inputNumber_4NEk: 4 },
      ],
    },
    structure: {
      formItemId: '55a10f99-c50e-487d-9a98-9a8dcab9a24f',
      path: ['55a10f99-c50e-487d-9a98-9a8dcab9a24f'],
      type: 'root',
      props: {
        formItemConfig: {
          name: 'root',
          required: true,
        },
      },
      children: [
        {
          formItemId: 'x30bCH8T',
          path: ['55a10f99-c50e-487d-9a98-9a8dcab9a24f', 'x30bCH8T'],
          type: 'section',
          props: {
            formItemConfig: {
              label: 'Section',
              name: 'section_Nkn4',
              required: false,
            },
          },
          children: [
            {
              formItemId: 'GMgdN4Ts',
              path: [
                '55a10f99-c50e-487d-9a98-9a8dcab9a24f',
                'x30bCH8T',
                'GMgdN4Ts',
              ],
              type: 'switch',
              props: {
                formItemConfig: {
                  label: 'Switch',
                  name: 'switch_oEvO',
                  required: false,
                },
                generalConfig: {
                  dataType: 'Boolean',
                },
              },
              children: [],
            },
          ],
        },
        {
          formItemId: 'e418cbe2-f245-4f53-872b-087413c4a904',
          path: [
            '55a10f99-c50e-487d-9a98-9a8dcab9a24f',
            'e418cbe2-f245-4f53-872b-087413c4a904',
          ],
          type: 'valueMaker',
          props: {
            formItemConfig: {
              label: 'Value Maker',
              layout: 'vertical',
              name: 'valueMaker_CuGq',
              required: false,
            },
            generalConfig: {
              dataType: 'Object',
            },
          },
          children: [],
        },
        {
          formItemId: '1b981b2f-b62b-4393-870b-c08743320ea6',
          path: [
            '55a10f99-c50e-487d-9a98-9a8dcab9a24f',
            '1b981b2f-b62b-4393-870b-c08743320ea6',
          ],
          type: 'valuePicker',
          props: {
            formItemConfig: {
              label: 'Value Picker',
              layout: 'vertical',
              name: 'valuePicker_D1Sk',
              required: false,
            },
            generalConfig: {
              dataType: 'Object',
            },
          },
          children: [],
        },
        {
          formItemId: 'aa64f8cf-7b3b-4928-a154-b4c725e01efe',
          path: [
            '55a10f99-c50e-487d-9a98-9a8dcab9a24f',
            'aa64f8cf-7b3b-4928-a154-b4c725e01efe',
          ],
          type: 'list',
          props: {
            formItemConfig: {
              label: 'People',
              name: 'list_O9R5',
              required: false,
            },
            generalConfig: {
              dataType: 'Array<Object>',
            },
          },
          children: [
            {
              formItemId: '2b4eebf7-7dd7-440c-ac36-69e4135f92fa',
              path: [
                '55a10f99-c50e-487d-9a98-9a8dcab9a24f',
                'aa64f8cf-7b3b-4928-a154-b4c725e01efe',
                '2b4eebf7-7dd7-440c-ac36-69e4135f92fa',
              ],
              type: 'input',
              props: {
                formItemConfig: {
                  label: 'Name',
                  name: 'name',
                  required: false,
                  wrapperCol: {
                    flex: 1,
                    xs: {
                      span: 24,
                    },
                  },
                },
                generalConfig: {
                  dataType: 'String',
                },
              },
              children: [],
            },
            {
              formItemId: '4236c06f-00d7-404d-8e00-6224095bd5dd',
              path: [
                '55a10f99-c50e-487d-9a98-9a8dcab9a24f',
                'aa64f8cf-7b3b-4928-a154-b4c725e01efe',
                '4236c06f-00d7-404d-8e00-6224095bd5dd',
              ],
              type: 'inputNumber',
              props: {
                formItemConfig: {
                  label: 'Age',
                  layout: 'horizontal',
                  name: 'inputNumber_4NEk',
                  required: false,
                  wrapperCol: {
                    flex: 1,
                    xs: {
                      span: 24,
                    },
                  },
                },
                generalConfig: {
                  dataType: 'Number',
                },
              },
              children: [],
            },
          ],
        },
      ],
    },
  }
