import { Empty, Flex, Skeleton } from 'antd'
import React from 'react'
import { Agent } from 'stores/models/agent'
import AgentCard, { ActionType } from './agent-card'
import styles from './agent-list.scss'

interface AgentListProps {
  agents: Agent[]
  loading: boolean
  onAction?: (type: ActionType, agent: Agent) => void
}

const AgentList: React.FC<AgentListProps> = ({ agents, loading, onAction }) => {
  return (
    <div className={styles.agentList}>
      <Skeleton loading={loading} active>
        {agents.length > 0 ? (
          agents.map((agent) => (
            <AgentCard
              key={agent.id}
              data={{
                ...agent,
                name: agent.agentName,
                iconId: agent.agentIconID,
                iconUuid: agent.agentIconUUID,
              }}
              onAction={onAction}
            />
          ))
        ) : (
          <Flex justify="start" style={{ width: '100%' }}>
            <Empty />
          </Flex>
        )}
      </Skeleton>
    </div>
  )
}

export default AgentList
