import Icon, { QuestionCircleOutlined } from '@ant-design/icons'
import { Avatar } from 'antd'
import { AgentTypeAgentType } from 'api/data-contracts'
import RobotColor from 'assets/images/robot-color.svg'
import SmartApiColor from 'assets/images/smart-api-color.svg'
import React from 'react'
import { getFileUrl, isEmpty } from 'utils/common'

interface AgentAvatarProps {
  agentType: AgentTypeAgentType // required for displaying default icon
  iconUuid?: string
  name?: string // for title attribute
  size?: number
}

const getAgentIcon = (agentType: AgentTypeAgentType) => {
  if (agentType === 'chatbot') {
    return <Icon component={RobotColor} style={{ fontSize: '52px' }} />
  } else if (agentType === 'smart_api') {
    return <Icon component={SmartApiColor} style={{ fontSize: '52px' }} />
  } else {
    return <QuestionCircleOutlined style={{ fontSize: '36px' }} />
  }
}

const AgentAvatar: React.FC<AgentAvatarProps> = ({
  agentType,
  iconUuid,
  name,
  size = 54,
}) => {
  if (!iconUuid || isEmpty(iconUuid)) {
    return (
      <Avatar
        shape="square"
        size={size}
        icon={getAgentIcon(agentType)}
        alt={`${name} default icon`}
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'transparent',
        }}
      />
    )
  }

  return (
    <Avatar
      shape="square"
      size={size}
      src={getFileUrl(iconUuid)}
      alt={`${name} icon`}
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    />
  )
}

export default AgentAvatar
