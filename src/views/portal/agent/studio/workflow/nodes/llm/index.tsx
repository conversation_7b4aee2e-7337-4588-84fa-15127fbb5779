import React, { memo, useEffect, useState } from 'react'
import { Position, useReactFlow } from '@xyflow/react'
import '@xyflow/react/dist/style.css'
import { RobotOutlined } from '@ant-design/icons'
import WorkflowStore from 'stores/workflow'
import { observer } from 'mobx-react'
import {
  OutputField,
  InputField,
  DataType,
  InputType,
  CustomNodeProps,
  NodeData,
} from 'views/portal/agent/studio/workflow/model'
import DeleteButton from '../../components/delete-button'
import { getNewNodeLabel } from '../../utils'
import CustomHandle from '../../components/custom-handle'

// Define default values
const defaultInputs: InputField[] = [
  {
    name: 'model_id',
    type: 'input' as InputType,
    dataType: 'Integer' as DataType,
    value: null,
    reference: '',
  },
  {
    name: 'max_tokens',
    type: 'input' as InputType,
    dataType: 'Number' as DataType,
    value: 1024,
    reference: '',
  },
  {
    name: 'stop_sequence',
    type: 'input' as InputType,
    dataType: 'String' as DataType,
    value: '',
    reference: '',
  },
  {
    name: 'temperature',
    type: 'input' as InputType,
    dataType: 'Number' as DataType,
    value: 1,
    reference: '',
  },
  {
    name: 'top_p',
    type: 'input' as InputType,
    dataType: 'Number' as DataType,
    value: 1,
    reference: '',
  },
  {
    name: 'conversation_context_number',
    type: 'input' as InputType,
    dataType: 'Number' as DataType,
    value: 20,
    reference: '',
  },
  {
    name: 'chat_context',
    type: 'input' as InputType,
    dataType: 'Boolean' as DataType,
    value: false,
    reference: '',
  },
  {
    name: 'system_prompt',
    type: 'input' as InputType,
    dataType: 'String' as DataType,
    value: '',
    reference: '',
  },
  {
    name: 'user_prompt',
    type: 'input' as InputType,
    dataType: 'String' as DataType,
    value: '',
    reference: '',
  },
]

const defaultOutputs: OutputField[] = [
  {
    name: 'text',
    type: 'String' as DataType,
    description: 'Generated Text',
    required: true,
    children: [],
  },
]

const getDefaultNodeData = (label: string): NodeData => ({
  label,
  description: 'Language Model for text generation',
  input: defaultInputs,
  output: defaultOutputs,
  intentBranch: [],
  conditionBranch: [],
  branchOutput: {},
})

const LLMNode: React.FC<CustomNodeProps> = observer((node) => {
  const { getNodes } = useReactFlow()
  const [nodeData, setNodeData] = useState<NodeData>(
    node.data.data || getDefaultNodeData('LLM')
  )

  useEffect(() => {
    if (!node.data.data) {
      const newLabel = getNewNodeLabel(getNodes(), node.type)
      const newNodeData = getDefaultNodeData(newLabel)
      setNodeData(newNodeData)
      if (node.data.onChange) {
        node.data.onChange(node.id, { data: newNodeData })
      }
    } else if (node.data.data.label !== nodeData.label) {
      setNodeData(node.data.data)
    }
  }, [node.data, getNodes])

  useEffect(() => {
    if (node.selected) {
      WorkflowStore.selectNode(node)
    }
  }, [node.selected])

  return (
    <div
      className={
        node.id === WorkflowStore.selectedNode?.id
          ? 'custom-node llm-node active'
          : 'custom-node llm-node'
      }
    >
      <CustomHandle
        node={node}
        type="target"
        position={Position.Left}
        isConnectable={node.isConnectable}
      />
      <div className="node-title">
        <div className="icon">
          <RobotOutlined />
        </div>
        <div className="text">{nodeData.label}</div>
      </div>
      <div className="node-desc">{nodeData.description}</div>
      <DeleteButton nodeID={node.id} />
      <CustomHandle
        node={node}
        type="source"
        position={Position.Right}
        isConnectable={node.isConnectable}
      />
    </div>
  )
})

export default memo(LLMNode)
