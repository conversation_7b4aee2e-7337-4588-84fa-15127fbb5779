import { ResponsesAgentResponse } from 'api/data-contracts'
import { useLocation } from 'react-router-dom'
import useOutletContextValue, {
  OutletContext,
} from '../../../../../hooks/use-outlet-context-value'

// Define the data type for the Studio context
type StudioData = {
  agent?: ResponsesAgentResponse
}

// Export the Studio-specific outlet context type
export type StudioOutletContext = OutletContext<StudioData>

const useStudioOutletContextValue = (): StudioOutletContext => {
  const location = useLocation()

  // Use the generic hook with the Studio-specific data type
  return useOutletContextValue<StudioData>({
    agent: location.state?.agent,
  })
}

export default useStudioOutletContextValue
