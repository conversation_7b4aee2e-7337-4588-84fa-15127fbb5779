import { Node, ReactFlowProps } from '@xyflow/react'
import BaseNode from './lib/base-node'
import { NodeTypeEnum, type BaseNodeType } from './lib/base-node/type'
import { getNodeClass } from './nodes'
import type { LoopNodeType } from './nodes/loop'
import NodeDefaultNode from './nodes/node-default'
import { RootNodeType } from './nodes/root'

export const convertDataToNodeTree = (
  node: BaseNodeType<'data'>,
  parent?: BaseNode
) => {
  if (node instanceof BaseNode) return node as unknown as BaseNode
  const { data } = node
  const NodeClass = getNodeClass(data.nodeType, NodeDefaultNode)
  const nodeInstance = new NodeClass(node)

  if (nodeInstance.type === NodeTypeEnum.Root) {
    nodeInstance.node.freeNodes = (node as RootNodeType<'data'>).freeNodes.map(
      (node) => convertDataToNodeTree(node, nodeInstance)
    )
  }

  if (
    nodeInstance.type === NodeTypeEnum.Workflow &&
    nodeInstance.node.data.nodeType === 'loop'
  ) {
    ;(nodeInstance as any).node.loopChildren = (
      node as LoopNodeType<'data'>
    ).loopChildren.map((node) => convertDataToNodeTree(node, nodeInstance))
    ;(nodeInstance as any).node.freeNodes = (
      node as LoopNodeType<'data'>
    ).freeNodes.map((node) => convertDataToNodeTree(node, nodeInstance))
  }

  if (parent) {
    nodeInstance.node.data.parents.push(parent)
  }

  if (data?.children?.length > 0) {
    const nodeList = data.children.map((child) =>
      convertDataToNodeTree(child, nodeInstance)
    )
    nodeInstance.node.data.children = nodeList
  }

  if (!nodeInstance.node.data.nodeData) return nodeInstance
  if ('propertyAndEndpointForms' in nodeInstance.node.data.nodeData) {
    delete nodeInstance.node.data.nodeData.propertyAndEndpointForms
  }
  if ('configurationFormInputs' in nodeInstance.node.data.nodeData) {
    delete nodeInstance.node.data.nodeData.configurationFormInputs
    Reflect.set(
      nodeInstance.node.data.inputs,
      'configurationForm',
      nodeInstance.node.data.nodeData.configurationFormInputs
    )
  }

  return nodeInstance
}

export type ReactFlowTreeType = {
  nodes: ReactFlowProps['nodes']
  edges: ReactFlowProps['edges']
}

export const convertNodeTreeToReactFlow = (
  allNodes: Record<string, BaseNode>
): ReactFlowTreeType => {
  const convertNode = (currentNode: BaseNode): Node => {
    const hasCustomView = getNodeClass(currentNode.node.data.nodeType)
    const viewType = hasCustomView
      ? currentNode.node.data.nodeType
      : `nodeDefault`

    return {
      ...currentNode.node,
      type: viewType,
    }
  }

  const result: {
    nodes: ReactFlowProps['nodes']
    edges: ReactFlowProps['edges']
  } = {
    nodes: [],
    edges: [],
  }

  Object.values(allNodes).forEach((node) => {
    result.nodes?.push(convertNode(node))
    if (node.node.edges?.length) {
      result.edges?.push(...node.node.edges)
    }
  })

  // loopNode

  return result
}
