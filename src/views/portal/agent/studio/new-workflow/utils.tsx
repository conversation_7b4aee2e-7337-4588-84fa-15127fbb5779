import { Node, ReactFlowProps } from '@xyflow/react'
import BaseNode, { BaseNodeType } from './lib/base-node'
import { getNodeClass } from './nodes'
import NodeDefaultNode from './nodes/node-default'

export const convertDataToNodeTree = (
  node: BaseNodeType<'data'>,
  parent?: BaseNode
) => {
  if (node instanceof BaseNode) return node as unknown as BaseNode
  const { data } = node

  const NodeClass = getNodeClass(data.nodeType, NodeDefaultNode)
  const nodeInstance = new NodeClass(node)

  if (parent) {
    nodeInstance.node.data.parents.push(parent)
  }
  if (data?.children?.length > 0) {
    const nodeList = data.children.map((child) =>
      convertDataToNodeTree(child, nodeInstance)
    )
    nodeInstance.node.data.children = nodeList
  }

  return nodeInstance
}

export const convertNodeTreeToReactFlow = (
  allNodes: Record<string, BaseNode>
): {
  nodes: ReactFlowProps['nodes']
  edges: ReactFlowProps['edges']
} => {
  const convertNode = (currentNode: BaseNode): Node => {
    const hasCustomView = getNodeClass(currentNode.node.data.nodeType)
    const viewType = hasCustomView
      ? currentNode.node.data.nodeType
      : `nodeDefault`

    return {
      id: currentNode.node.data.id,
      type: viewType,
      position: currentNode.node.position,
      data: currentNode.node.data,
      handles: currentNode.node.handles,
      parentId: currentNode.node?.parentId,
      extent: currentNode.node?.extent,
    }
  }

  const result: {
    nodes: ReactFlowProps['nodes']
    edges: ReactFlowProps['edges']
  } = {
    nodes: [],
    edges: [],
  }

  Object.values(allNodes).forEach((node) => {
    result.nodes?.push(convertNode(node))
    if (node.node.edges?.length) {
      result.edges?.push(...node.node.edges)
    }
  })

  return result
}
