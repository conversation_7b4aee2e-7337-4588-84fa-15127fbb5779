import { ConditionGroup } from 'components/new-dynamic-form/components/condition'
import BaseNode, {
  BaseNodeType,
  BaseNodeViews,
  NodeTypeEnum,
} from '../../lib/base-node'
import { FormItemType } from 'components/new-dynamic-form/types'
import { OutputType } from 'views/portal/plugin/components/output'

export type WorkflowNodeBaseDataType = Partial<{
  type: string
  propertyForms: Array<{
    name: string
    structure: FormItemType
    output: OutputType
  }>
  propertyForm: {
    name: string
    structure: FormItemType
    output: OutputType
  }
}>

export type WorkflowNodeInputsDataType = {
  // configurationForm?: Record<string, any>
  propertyForm?: { conditionBranches: { branches: ConditionGroup } }
}

export type WorkflowNodeType = BaseNodeType<
  'instance',
  WorkflowNodeBaseDataType,
  WorkflowNodeInputsDataType
>
export type WorkflowNodeDataType = BaseNodeType<
  'data',
  WorkflowNodeBaseDataType,
  WorkflowNodeInputsDataType
>

class WorkflowNode extends BaseNode<WorkflowNodeType> {
  readonly type = NodeTypeEnum.Workflow

  constructor(node: WorkflowNodeDataType, views: BaseNodeViews = {}) {
    super(node, views)
  }
}

export default WorkflowNode
