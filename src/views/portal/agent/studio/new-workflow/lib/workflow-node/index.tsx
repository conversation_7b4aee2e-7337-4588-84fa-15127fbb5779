import { ConditionGroup } from 'components/new-dynamic-form/components/condition'
import BaseNode from '../../lib/base-node'
import { FormItemType } from 'components/new-dynamic-form/types'
import { OutputType } from 'views/portal/plugin/components/output'

import {
  BaseNodeType,
  BaseNodeViews,
  NodeTypeEnum,
} from '../../lib/base-node/type'

export type WorkflowNodeBaseDataType = Partial<{
  type: string
  propertyForms: Array<{
    name: string
    structure: FormItemType
    output: OutputType
  }>
  propertyForm: Partial<{
    name: string
    structure: FormItemType
    output: OutputType
  }>
  description: string
  iconId: string
  name: string
}>

export type WorkflowNodeInputsDataType = {
  propertyForm?: Record<string, any>
}

export type WorkflowNodeType = BaseNodeType<
  'instance',
  WorkflowNodeBaseDataType,
  WorkflowNodeInputsDataType
>
export type WorkflowNodeDataType = BaseNodeType<
  'data',
  WorkflowNodeBaseDataType,
  WorkflowNodeInputsDataType
>

class WorkflowNode<
  T extends WorkflowNodeType = WorkflowNodeType,
> extends BaseNode<T> {
  readonly type = NodeTypeEnum.Workflow
  constructor(node: WorkflowNodeDataType, views: BaseNodeViews = {}) {
    super(node, views)
  }
}

export default WorkflowNode
