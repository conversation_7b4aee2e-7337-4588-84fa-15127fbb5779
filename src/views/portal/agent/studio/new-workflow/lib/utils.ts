import { useWorkflowStore } from 'stores/new-workflow'
import RootNode from '../nodes/root'
import BaseNode from './base-node'
import { NodeTypeEnum } from './base-node/type'

export const createNode = (
  newNode: BaseNode,
  nodes?: {
    sourceNode: BaseNode
    targetNode?: BaseNode
    sourceHandleId?: string | null
    targetHandleId?: string | null
  }
) => {
  // If u want to create a empty node, code here (No source and target)
  if (!nodes) return

  // At least one edges form sourceNode to newNode
  const { sourceNode, targetNode } = nodes
  const { handles: newNodeHandles } = newNode.node

  const { handles: sourceHandles, position: sourcePos } = sourceNode.node

  // Check if sourceNode is a loop container to create sub-flow nodes
  const isLoopContainer = sourceNode.node.data.nodeType === 'loop'

  // if there is no targetNode yet
  if (!targetNode) {
    const sourceHandleId =
      nodes.sourceHandleId ??
      sourceHandles.filter((h) => h.type === 'source')[0].id ??
      sourceNode.node.id
    const targetHandleId =
      nodes.targetHandleId ??
      newNodeHandles.filter((h) => h.type === 'target')[0].id ??
      newNode.node.id

    const { data: sourceData } = sourceNode.node

    newNode.node.position = {
      x: sourcePos.x + 200 + sourceData.children.length * 10,
      y: sourcePos.y + sourceData.children.length * 10,
    }

    newNode.node.data.parents.push(sourceNode)
    sourceNode.node.data.children.push(newNode)

    if (isLoopContainer) {
      newNode.node.parentId = sourceNode.node.id
      newNode.node.extent = 'parent'
    } else {
      sourceNode.node.edges?.push({
        id: `${sourceHandleId}_${targetHandleId}`,
        source: sourceNode.node.id,
        target: newNode.node.id,
        sourceHandle: sourceHandleId,
        targetHandle: targetHandleId,
        type: 'custom-edge',
      })
    }
  } else {
    // defined newNode position
    const { position: targetPos } = targetNode.node
    newNode.node.position = {
      x:
        (sourcePos.x + targetPos.x) / 2 +
        sourceNode.node.data.children.length * 10,
      y:
        (sourcePos.y + targetPos.y) / 2 +
        sourceNode.node.data.children.length * 10,
    }

    // 1. remove source and target nodes relationship
    // 1.1 source edges and children
    sourceNode.node.data.children = sourceNode.node.data.children.filter(
      (child) => child.node.id !== targetNode.node.id
    )
    sourceNode.node.edges = sourceNode.node.edges?.filter(
      (edge) => edge.target !== targetNode.node.id
    )
    // 1.2 target parents
    targetNode.node.data.parents = targetNode.node.data.parents.filter(
      (parent) => parent.node.id !== sourceNode.node.id
    )

    // 2.1 update source node children and edges
    sourceNode.node.data.children.push(newNode)
    const sourceHandleId = sourceHandles.filter((h) => h.type === 'source')[0]
      .id
    const newNodeTargetHandleId = newNodeHandles.filter(
      (h) => h.type === 'target'
    )[0].id
    sourceNode.node.edges?.push({
      id: `${sourceHandleId}_${newNodeTargetHandleId}`,
      source: sourceNode.node.id,
      target: newNode.node.id,
      sourceHandle: sourceHandleId,
      targetHandle: newNodeTargetHandleId,
      type: 'custom-edge',
    })
    // 2.2 update newNode parents
    newNode.node.data.parents.push(sourceNode)

    // 3. update target node parents and edges
    const { handles: targetHandles } = targetNode.node
    const targetHandleId = targetHandles.filter((h) => h.type === 'target')[0]
      ?.id
    const newNodeSourceHandleId = newNodeHandles.filter(
      (h) => h.type === 'source'
    )[0]?.id
    if (!newNodeSourceHandleId) {
      const { workflowTree } = useWorkflowStore.getState()
      workflowTree?.node.freeNodes.push(targetNode)
      return
    } else {
      newNode.node.data.children.push(targetNode)
      // update targetNode parents
      targetNode.node.data.parents.push(newNode)
      newNode.node.edges?.push({
        id: `${newNodeSourceHandleId}_${targetHandleId}`,
        source: newNode.node.id,
        target: targetNode.node.id,
        sourceHandle: newNodeSourceHandleId,
        targetHandle: targetHandleId,
        type: 'custom-edge',
      })
    }
  }
}

export const deleteNode = (node: BaseNode, rootNode: RootNode) => {
  if (!node) return
  const { parents, children } = node.node.data

  children.forEach((child) => {
    child.node.data.parents = child.node.data.parents.filter(
      (parent) => parent.node.id !== node.node.id
    )

    if (!child.node.data.parents.length) {
      rootNode.node.freeNodes.push(child)
    }
  })

  parents.forEach((parent) => {
    parent.node.data.children = parent.node.data.children.filter(
      (child) => child.node.id !== node.node.id
    )
    parent.node.edges = parent.node.edges?.filter(
      (edge) => edge.target !== node.node.id
    )
  })
}

export const getAllParents = (node: BaseNode) => {
  const res: Record<string, BaseNode> = {}
  const traverseParents = (currentNode: BaseNode) => {
    if (currentNode.type !== NodeTypeEnum.Root) {
      const { parents, id } = currentNode.node.data
      parents.forEach((parentNode) => {
        Reflect.set(res, id, parentNode)
        traverseParents(parentNode)
      })
    }
  }
  traverseParents(node)
  return res
}
