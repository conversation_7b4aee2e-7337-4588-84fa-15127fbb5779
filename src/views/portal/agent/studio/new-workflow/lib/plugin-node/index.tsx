import { nanoid } from 'nanoid'
import BaseNode, {
  BaseNodeType,
  BaseNodeViews,
  NodeTypeEnum,
} from '../../lib/base-node'
import { Position } from '@xyflow/react'
import { FormItemType } from 'components/new-dynamic-form/types'

export type PluginNodeBaseDataType = Partial<{
  propertyAndEndpointForms: Array<{
    name: string
    structure: FormItemType
    endpoint: any
  }>
  propertyForm: {
    name: string
    structure: FormItemType
    endpoint: any
  }
}>

export type PluginNodeInputsDataType = {
  // configurationForm?: Record<string, any>
  propertyForm?: Record<string, any>
}

export type PluginNodeType = BaseNodeType<
  'instance',
  PluginNodeBaseDataType,
  PluginNodeInputsDataType
>
export type PluginNodeDataType = BaseNodeType<
  'data',
  PluginNodeBaseDataType,
  PluginNodeInputsDataType
>

class PluginNode extends BaseNode<PluginNodeType> {
  readonly type = NodeTypeEnum.Plugin
  static getDefaultNodeData = (id: string): PluginNodeDataType => {
    return {
      id,
      handles: [
        {
          id: `${id}_target_${nanoid()}`,
          type: 'target',
          position: Position.Left,
          nodeId: id,
          x: 0,
          y: 0,
          width: 0,
          height: 0,
        },
        {
          id: `${id}_source_${nanoid()}`,
          type: 'source',
          position: Position.Right,
          nodeId: id,
          x: 0,
          y: 0,
          width: 0,
          height: 0,
        },
      ],
      data: {
        id,
        name: '',
        nodeType: 'plugin',
        parents: [],
        children: [],
      },
      type: NodeTypeEnum.Root,
      edges: [],
      position: { x: 0, y: 0 },
    }
  }

  constructor(node: PluginNodeDataType, views: BaseNodeViews = {}) {
    super(node, views)
  }
}

export default PluginNode
