import { SaveOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import {
  Background,
  Controls,
  Edge,
  MiniMap,
  Node,
  OnEdgesChange,
  OnNodesChange,
  ReactFlow,
  ReactFlowProvider,
  useEdgesState,
  useNodesState,
} from '@xyflow/react'
import { <PERSON><PERSON>, <PERSON>dal } from 'antd'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'

import { useMainLayoutContext } from 'layouts/portal/page-main-layout/context'
import { useWorkflowStore } from 'stores/new-workflow'
import { sleep } from 'utils/common'
import { isLocal } from 'utils/env'
import { defaultEdgeOptions } from '../workflow/constants'
import { edgeTypes, getDefaultTree, nodeTypes } from './constants'
import BaseNode from './lib/base-node'
import { convertNodeTreeToReactFlow } from './utils'
import Send from 'assets/images/send.svg'
import { AgentTypeAgentType } from 'api/data-contracts'

import Sidebar from './components/sidebar'
import AgentAvatar from '../../components/agent-avatar'
import CheckList from './components/checklist'
import { CheckListIcon } from './components/checklist/check-list-icon'

import styles from './index.scss'
const Workflow = () => {
  const {
    workflowTree,
    allNodes,
    sidebarConfig,
    checkListData,
    setWorkflowTree,
    setNode,
    setNodeThrottle,
    setSidebarConfig,
    refreshWorkflowTree,
  } = useWorkflowStore()
  const { setRegisterEvents, setCaption, setTitle, setIcon, setActions } =
    useMainLayoutContext() ?? {}

  const [nodes, setNodes, onNodesChange] = useNodesState<Node>([])
  const [edges, setEdges, onEdgesChange] = useEdgesState<Edge>([])
  const [lastSaveTime, setLastSaveTime] = useState<dayjs.Dayjs | null>(null)
  const [checkListModalOpen, setCheckListModalOpen] = useState(false)
  const caption = useMemo(() => {
    return lastSaveTime ? lastSaveTime.fromNow() : 'Not saved yet'
  }, [lastSaveTime])

  const fetchData = async () => {
    await sleep(1000)
    const defaultTree = getDefaultTree()
    setWorkflowTree(defaultTree)

    // const tree = convertDataToNodeTree() as RootNode
    // setWorkflowTree(tree)
  }

  const handleNodesChange: OnNodesChange = useCallback(
    (changes) => {
      if (!allNodes) return

      changes.forEach((change: any) => {
        const { type, id, position } = change

        if (type === 'position') {
          const currentNode: BaseNode = Reflect.get(allNodes, id)

          if (!currentNode) return
          Reflect.set(currentNode.node, 'position', position)
          setNodeThrottle(id, currentNode)
        }
      })

      onNodesChange(changes)
    },
    [onNodesChange, allNodes]
  )

  const handleEdgesChange: OnEdgesChange = useCallback(
    (changes) => {
      if (!allNodes) return
      changes.forEach((change: any) => {
        const { type, id } = change

        if (type === 'remove') {
          const edge = edges.find((edge) => edge.id === id)
          if (!edge) return
          const sourceNode = Reflect.get(allNodes, edge?.source)
          if (!sourceNode) return
          sourceNode.node.edges = sourceNode.node.edges?.filter(
            (edge) => edge.id !== id
          )

          const targetNode = Reflect.get(allNodes, edge?.target)
          targetNode.node.data.parents = targetNode.node.data.parents.filter(
            (parent) => parent.node.id !== sourceNode.node.id
          )

          if (!targetNode.node.data.parents.length && workflowTree) {
            workflowTree.freeNodes.push(targetNode)
          }

          setNode(sourceNode.node.data.id, sourceNode)
          setNode(targetNode.node.data.id, targetNode)
          refreshWorkflowTree()
        }
      })

      onEdgesChange(changes)
    },
    [onEdgesChange, allNodes, edges]
  )

  const handlePaneClick = () => {
    if (sidebarConfig.type === 'createNode')
      setSidebarConfig({ open: false, type: '', createNodeInfo: undefined })
  }

  const handleWorkflowSave = () => {
    setLastSaveTime(dayjs())
    console.error('Save')
  }

  const handleWorkflowPublish = () => {
    console.error('Publish')
  }

  const initCaption = () => {
    setCaption?.(caption)
  }

  const handleTest = () => {
    console.group('Test')
    console.error({
      workflowTree,
      workflowTreeOutput: workflowTree?.toPrint(),
    })
    console.error({ nodes, edges })
    console.groupEnd()
  }

  useEffect(() => {
    fetchData()
    setRegisterEvents?.([
      initCaption,
      () =>
        setIcon?.(
          <AgentAvatar
            agentType={AgentTypeAgentType.Chatbot}
            iconUuid={'routeStates.data.agent?.agentIconUUID'}
            name={'Agent Name'}
          />
        ),
      () => setTitle?.('Agent Name'),
      () => setActions?.(headerActionOperations),
    ])
  }, [])

  useEffect(() => {
    // allNodes update with workflow. But allNodes has more effect
    // Keep the nodes don't change too many times
    if (!allNodes) return
    const reactFlowInfo = convertNodeTreeToReactFlow(allNodes)
    setNodes(reactFlowInfo.nodes ?? [])
    setEdges(reactFlowInfo.edges ?? [])
  }, [workflowTree])

  useEffect(() => {
    initCaption()
  }, [lastSaveTime])

  // Render
  const headerActionOperations = useMemo(() => {
    const actionItems = [
      {
        key: 'save',
        icon: <SaveOutlined style={{ fontSize: 20 }} />,
        onClick: handleWorkflowSave,
        // disabled: !hasChanges,
        text: 'Save',
        style: { fontWeight: 700 },
      },
      {
        key: 'checklist',
        icon: <CheckListIcon isChecked={!!checkListData.length} />,
        onClick: () => setCheckListModalOpen(true),
        text: 'Checklist',
        style: { fontWeight: 700 },
      },
      {
        key: 'publish',
        icon: <Send style={{ fontSize: 20, verticalAlign: 'middle' }} />,
        onClick: handleWorkflowPublish,
        text: 'Publish',
        style: { fontWeight: 700 },
      },
    ]
    return actionItems
  }, [checkListData.length])

  useEffect(() => {
    setActions?.(headerActionOperations)
  }, [headerActionOperations])

  return (
    <div className={styles.workflowWrapper}>
      {isLocal && (
        <span
          onClick={handleTest}
          style={{ position: 'absolute', zIndex: 1, bottom: 0, right: 0 }}
        >
          <Button>Data</Button>
        </span>
      )}
      <ReactFlow
        fitView
        defaultEdgeOptions={defaultEdgeOptions}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        nodes={nodes}
        edges={edges}
        onNodesChange={handleNodesChange}
        onEdgesChange={handleEdgesChange}
        onPaneClick={handlePaneClick}
      >
        <Background gap={10} size={2} color="#EAECEF" />
        <Sidebar />

        <div className={styles.workflowToolWrapper}>
          <Controls
            position={'top-left'}
            orientation={'horizontal'}
            className={styles.workflowControls}
          />
          <MiniMap position={'top-left'} className={styles.workflowMiniMap} />
        </div>

        <Modal
          title="Checklist"
          open={checkListModalOpen}
          onCancel={() => setCheckListModalOpen(false)}
          footer={null}
        >
          <span>Make sure all issues are resolved before publish</span>
          {checkListData.length ? (
            <CheckList items={checkListData} />
          ) : (
            <div>No issues</div>
          )}
        </Modal>
      </ReactFlow>
    </div>
  )
}

const WorkflowWrapper = () => {
  return (
    <ReactFlowProvider>
      <Workflow />
    </ReactFlowProvider>
  )
}

export default memo(WorkflowWrapper)
