.default-node-sidebar {
  &-wrapper {
    display: flex;
    flex-flow: column nowrap;
  }

  &-header {
    &-wrapper {
      display: flex;
      flex-flow: column nowrap;
      padding: 16px;
      border-bottom: 1px solid #eaecef;
      row-gap: 12px;
    }

    &-title {
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      justify-content: space-between;
      column-gap: 8px;
      height: 44px;

      &-icon {
        width: 32px;
        height: 32px;
        align-items: center;
        justify-content: center;
      }

      &-text {
        flex: 1;

        color: #2a2c2f;
        font-size: 18px;
        font-weight: 700;
      }
    }

    &-description {
      color: #000;
      font-size: 16px;
      font-weight: 400;
      // min-height: 60px;

      &-text {
        padding: 0px 16px;
      }
    }
  }

  &-content {
    &-output {
      &-label {
        color: rgba(0, 0, 0, 0.88);
        font-size: 14px;
        font-weight: 700;
      }

      &-item {
        display: flex;
        column-gap: 8px;
        &-type {
          color: rgb(136, 136, 136);
        }
      }
    }
  }
}
