import {
  ApiOutlined,
  CloseOutlined,
  DownOutlined,
  SettingOutlined,
} from '@ant-design/icons'
import {
  <PERSON><PERSON>,
  Collapse,
  FormInstance,
  Modal,
  Tree,
  TreeSelectProps,
} from 'antd'
import { useEffect, useRef, useState } from 'react'
import { useWorkflowStore } from 'stores/new-workflow'

import CustomAvatar from 'components/custom-avatar'
import CustomEditableText from 'components/custom-editable-text'
import { FormContext } from 'components/new-dynamic-form/context'
import FormItemRender from 'components/new-dynamic-form/form-item-render'

import type { FormItemType } from 'components/new-dynamic-form/types'

import { PluginPluginOutputFormType } from 'api/data-contracts'
import PluginColor from 'assets/images/plugin-color.svg'
import { convertFormItemsToTreeData } from 'components/new-dynamic-form'
import { set } from 'lodash-es'
import { sleep } from 'utils/common'
import {
  OutputDataType,
  OutputType,
} from 'views/portal/plugin/components/output'
import { OutputTable } from 'views/portal/plugin/components/output-table'
import BaseNode from '../../lib/base-node'
import PluginNode from '../../lib/plugin-node'
import { getAllParents } from '../../lib/utils'
import WorkflowNode from '../../lib/workflow-node'
import styles from './index.scss'

type NodeSidebarProps = {
  node: BaseNode
  children?: React.ReactNode
  onFormValuesChange?: (changedValues: any, values: any) => void
}

const DefaultNodeSidebar = (props: NodeSidebarProps) => {
  const { node: currentNode, children, onFormValuesChange } = props
  const {
    allNodes,
    currentNodeId,
    checkListData,
    setCurrentNodeId,
    setSidebarConfig,
    setNode,
    setCheckListData,
    refreshWorkflowTree,
  } = useWorkflowStore()
  const [propertyForm, setPropertyForm] = useState<FormItemType>()
  const [referenceTreeData, setReferenceTreeData] = useState<
    TreeSelectProps['treeData']
  >([])
  const [outputModalOpen, setOutputModalOpen] = useState(false)
  const [outputData, setOutputData] = useState<OutputType>({
    type: PluginPluginOutputFormType.Fixed,
    structure: [],
  })
  const formRef = useRef<{ form: FormInstance }>()

  const handleClose = () => {
    setSidebarConfig({ open: false, type: '' })
    setCurrentNodeId('')
  }

  const handleNodeInfoChange = (value: string, key: string) => {
    if (!currentNode) return
    Reflect.set(currentNode.node.data, key, value)
    setNode(currentNodeId, currentNode)
  }

  const handleNameValidator = (name: string) => {
    if (!name) return { isValid: false, errorMessage: 'Name is required' }

    if (allNodes) {
      const isDuplicated = Object.values(allNodes)?.some(
        (node) =>
          node.node.data.name === name && node.node.data.id !== currentNodeId
      )

      if (isDuplicated)
        return { isValid: false, errorMessage: 'Name is duplicated' }
    }

    return { isValid: true }
  }

  const handleFormValuesChange = (changedValues: any, values: any) => {
    if (!currentNode) return

    onFormValuesChange?.(changedValues, values)
    handleFormValid()
    set(currentNode.node.data, 'inputs.propertyForm', values)
    setNode(currentNodeId, currentNode)
    refreshWorkflowTree()
  }

  const needParentsInputs = (form: FormItemType): boolean => {
    if (['valueMaker', 'valuePicker', 'condition'].includes(form.type))
      return true
    return form.children?.some(needParentsInputs) || false
  }

  const handleFormValid = async () => {
    await sleep(0)
    formRef.current?.form
      .validateFields()
      .then(() => {
        setCheckListData(
          checkListData.filter(
            (item) => item.title !== currentNode.node.data.name
          )
        )
      })
      .catch((errorInfo) => {
        const { errorFields } = errorInfo

        const curCheckList = errorFields.map((field: any) => ({
          title: currentNode.node.data.name,
          icon: (
            <CustomAvatar
              iconUUID={currentNode.node.data.nodeData?.iconUUID}
              size={16}
              shape="square"
              defaultIcon={<PluginColor fontSize={16} />}
            />
          ),
          messages: [{ type: 'error', message: field.errors[0] }],
        }))

        setCheckListData(
          checkListData
            .filter((item) => item.title !== currentNode.node.data.name)
            .concat(curCheckList)
        )
      })
  }

  const handleOutputDataChange = (data: OutputDataType[]) => {
    if (currentNode instanceof WorkflowNode) {
      set(currentNode.node.data, 'nodeData.propertyForm.output.structure', data)
    } else if (currentNode instanceof PluginNode) {
      set(
        currentNode.node.data,
        'nodeData.propertyForm.endpoint.output.structure',
        data
      )
    }
    setNode(currentNodeId, currentNode)
    setOutputData({ ...outputData, structure: data })
  }

  useEffect(() => {
    if (currentNode.node.data.inputs) {
      const { propertyForm } = currentNode.node.data.inputs
      formRef.current?.form.setFieldsValue(propertyForm)
    }

    let curPropertyForm =
      currentNode?.node.data.nodeData?.propertyForm?.structure
    if (!curPropertyForm) return
    setPropertyForm(curPropertyForm)

    if (needParentsInputs(curPropertyForm)) {
      const parents = getAllParents(currentNode)
      const referenceData: TreeSelectProps['treeData'] = []
      Object.values(parents).map((node) => {
        const form = node.node.data.nodeData?.propertyForm
        if (form) {
          const data = convertFormItemsToTreeData(form.children, {
            pathPrefix: node.node.data.name,
          })
          referenceData.push({
            title: node.node.data.name,
            value: node.node.data.name,
            children: data,
          })
        }
      })

      setReferenceTreeData(referenceData)
    }

    handleFormValid()

    // output
    let res: OutputType = {
      type: PluginPluginOutputFormType.Fixed,
      structure: [],
    }
    if (currentNode instanceof WorkflowNode) {
      res = {
        structure:
          currentNode.node.data.nodeData?.propertyForm?.output.structure ?? [],
        type:
          currentNode.node.data.nodeData?.propertyForm?.output.type ??
          PluginPluginOutputFormType.Fixed,
      }
    } else if (currentNode instanceof PluginNode) {
      res = {
        structure:
          currentNode.node.data.nodeData?.propertyForm?.endpoint?.output
            .structure ?? [],
        type:
          currentNode.node.data.nodeData?.propertyForm?.endpoint?.output.type ??
          PluginPluginOutputFormType.Fixed,
      }
    }

    setOutputData(res)
  }, [currentNode])

  return (
    <div className={styles.defaultNodeSidebarWrapper}>
      <div className={styles.defaultNodeSidebarHeaderWrapper}>
        <div className={styles.defaultNodeSidebarHeaderTitle}>
          <CustomAvatar
            iconUUID={currentNode?.node.data.nodeData?.iconUUID}
            shape="square"
            size={32}
            rootClassName={styles.defaultNodeSidebarHeaderTitleIcon}
            defaultIcon={<ApiOutlined />}
          />
          <CustomEditableText
            wrapperClassName={styles.defaultNodeSidebarHeaderTitleText}
            value={currentNode?.node.data.name ?? ''}
            onChange={(v) => handleNodeInfoChange(v, 'name')}
            validator={handleNameValidator}
          />
          <CloseOutlined onClick={handleClose} />
        </div>

        <CustomEditableText
          type="textarea"
          wrapperClassName={styles.defaultNodeSidebarHeaderDescription}
          textClassName={styles.defaultNodeSidebarHeaderDescriptionText}
          value={
            currentNode?.node.data?.description ??
            currentNode?.node.data.nodeData?.description
          }
          onChange={(v) => handleNodeInfoChange(v, 'description')}
        />
      </div>
      <div
        className={styles.defaultNodeSidebarContentWrapper}
        key={currentNode.node.id}
      >
        {children ? (
          children
        ) : (
          <FormContext.Provider
            value={{
              root: propertyForm,
              setRoot: setPropertyForm,
              referenceTreeData,
            }}
          >
            <FormItemRender
              ref={formRef}
              data={propertyForm?.children ?? []}
              canEdit={false}
              onFormValuesChange={handleFormValuesChange}
            />

            <Collapse
              ghost
              className={styles.defaultNodeSidebarContentOutput}
              items={[
                {
                  key: 'output',
                  label: (
                    <span
                      className={styles.defaultNodeSidebarContentOutputLabel}
                    >
                      Output
                    </span>
                  ),
                  children: (
                    <>
                      {outputData.type ===
                      PluginPluginOutputFormType.Customized ? (
                        <Button
                          icon={<SettingOutlined />}
                          onClick={() => setOutputModalOpen(true)}
                        >
                          Output Structure Settings
                        </Button>
                      ) : (
                        !!outputData.structure.length && (
                          <Tree
                            defaultExpandAll
                            showLine
                            switcherIcon={<DownOutlined />}
                            treeData={outputData.structure}
                            fieldNames={{ title: 'name' }}
                          />
                        )
                      )}
                    </>
                  ),
                },
              ]}
            />
          </FormContext.Provider>
        )}
      </div>

      <Modal open={outputModalOpen} onCancel={() => setOutputModalOpen(false)}>
        <OutputTable
          data={outputData.structure}
          onChange={handleOutputDataChange}
        />
      </Modal>
    </div>
  )
}

export default DefaultNodeSidebar
