const fakeData: any[] = [
  {
    id: 53,
    name: 'MyPlugin',
    uuid: '1b667c31-a0cb-45c0-8a83-ee972d0b6c73',
    description: 'To integrate enterprise API into Genie Studio',
    iconID: 4498,
    icon: {
      id: 4498,
      filename: 'Screenshot 2025-03-18 at 5.14.22 PM.png',
      uuid: 'ab3a1f60-c20e-4f7f-87ed-590dacfabe29',
      fileType: '.png',
      fileBelongs: 'plugin',
      path: 'plugin/20250407/b9cc936b-d090-42cc-87c6-554396ec01a1/Screenshot 2025-03-18 at 5.14.22 PM.png',
      size: 59453,
      organizationID: 1,
      userID: 43,
      username: '<PERSON>',
      createdAt: '2025-04-07T03:48:47.364Z',
      updatedAt: '2025-04-07T03:48:47.364Z',
    },
    type: 'internal',
    categoryID: 1,
    category: {
      id: 1,
      uuid: 'b52565e9-6e1e-4905-8bf7-d9985bea791e',
      name: 'Plugin Node',
      description: 'data',
      iconID: null,
      userID: 1,
      organizationID: 1,
    },
    endpoint: '',
    userID: 43,
    organizationID: 1,
    createdAt: '2025-04-07T03:52:29.723Z',
    updatedAt: '2025-04-25T03:24:20.352Z',

    pluginID: 53,
    version: '086b25e6-734a-42b6-a082-1f7ce3713abf',
    status: 'draft',
    configurationForm: {
      formItemId: '55a10f99-c50e-487d-9a98-9a8dcab9a24f',
      path: ['55a10f99-c50e-487d-9a98-9a8dcab9a24f'],
      type: 'root',
      props: {
        formItemConfig: {
          name: 'root',
          required: true,
        },
      },
      children: [
        {
          formItemId: 'x30bCH8T',
          path: ['55a10f99-c50e-487d-9a98-9a8dcab9a24f', 'x30bCH8T'],
          type: 'section',
          props: {
            formItemConfig: {
              label: 'Section',
              name: 'section_Nkn4',
              required: false,
            },
          },
          children: [
            {
              formItemId: 'GMgdN4Ts',
              path: [
                '55a10f99-c50e-487d-9a98-9a8dcab9a24f',
                'x30bCH8T',
                'GMgdN4Ts',
              ],
              type: 'switch',
              props: {
                formItemConfig: {
                  label: 'Switch',
                  name: 'switch_oEvO',
                  required: false,
                },
                generalConfig: {
                  dataType: 'Boolean',
                },
              },
              children: [],
            },
          ],
        },
        {
          formItemId: 'e418cbe2-f245-4f53-872b-087413c4a904',
          path: [
            '55a10f99-c50e-487d-9a98-9a8dcab9a24f',
            'e418cbe2-f245-4f53-872b-087413c4a904',
          ],
          type: 'valueMaker',
          props: {
            formItemConfig: {
              label: 'Value Maker',
              layout: 'vertical',
              name: 'valueMaker_CuGq',
              required: false,
            },
            generalConfig: {
              dataType: 'Object',
            },
          },
          children: [],
        },
        {
          formItemId: '1b981b2f-b62b-4393-870b-c08743320ea6',
          path: [
            '55a10f99-c50e-487d-9a98-9a8dcab9a24f',
            '1b981b2f-b62b-4393-870b-c08743320ea6',
          ],
          type: 'valuePicker',
          props: {
            formItemConfig: {
              label: 'Value Picker',
              layout: 'vertical',
              name: 'valuePicker_D1Sk',
              required: false,
            },
            generalConfig: {
              dataType: 'Object',
            },
          },
          children: [],
        },
        {
          formItemId: 'aa64f8cf-7b3b-4928-a154-b4c725e01efe',
          path: [
            '55a10f99-c50e-487d-9a98-9a8dcab9a24f',
            'aa64f8cf-7b3b-4928-a154-b4c725e01efe',
          ],
          type: 'list',
          props: {
            formItemConfig: {
              label: 'People',
              name: 'list_O9R5',
              required: false,
            },
            generalConfig: {
              dataType: 'Array<Object>',
            },
          },
          children: [
            {
              formItemId: '2b4eebf7-7dd7-440c-ac36-69e4135f92fa',
              path: [
                '55a10f99-c50e-487d-9a98-9a8dcab9a24f',
                'aa64f8cf-7b3b-4928-a154-b4c725e01efe',
                '2b4eebf7-7dd7-440c-ac36-69e4135f92fa',
              ],
              type: 'input',
              props: {
                formItemConfig: {
                  label: 'Name',
                  name: 'name',
                  required: false,
                  wrapperCol: {
                    flex: 1,
                    xs: {
                      span: 24,
                    },
                  },
                },
                generalConfig: {
                  dataType: 'String',
                },
              },
              children: [],
            },
            {
              formItemId: '4236c06f-00d7-404d-8e00-6224095bd5dd',
              path: [
                '55a10f99-c50e-487d-9a98-9a8dcab9a24f',
                'aa64f8cf-7b3b-4928-a154-b4c725e01efe',
                '4236c06f-00d7-404d-8e00-6224095bd5dd',
              ],
              type: 'inputNumber',
              props: {
                formItemConfig: {
                  label: 'Age',
                  layout: 'horizontal',
                  name: 'inputNumber_4NEk',
                  required: false,
                  wrapperCol: {
                    flex: 1,
                    xs: {
                      span: 24,
                    },
                  },
                },
                generalConfig: {
                  dataType: 'Number',
                },
              },
              children: [],
            },
          ],
        },
      ],
    },
    propertyAndEndpointForms: [
      {
        name: 'Feature 1',
        endpoint: {
          url: {
            method: 'POST',
            path: 'https://www.google.com/{name}/search?q={age}',
          },
          output: {
            type: 'customized',
            structure: [
              {
                key: '6a2f6811-391a-4f58-9b05-626f6491d14c',
                name: 'Test1',
                type: 'String',
                required: true,
                deep: 1,
              },
              {
                key: 'bf9c3577-207b-4afe-9da9-d3b4e1cb1215',
                name: 'Test2',
                type: 'Object',
                required: false,
                deep: 1,
                children: [
                  {
                    key: '7e50f2c6-d23d-4db3-a787-1725f0cf2dcf',
                    name: 'Test3',
                    type: 'String',
                    required: true,
                    deep: 2,
                  },
                ],
              },
              {
                key: '830af807-d3c7-4376-aad7-9ffaa4b62caf',
                name: 'Test4',
                type: 'Array<String>',
                required: false,
                deep: 1,
              },
            ],
          },
          query: [
            {
              id: 'A3mL2Uk6b3yBmzo-GfLUT',
              key: 'name',
              type: 'reference',
              dataType: 'String',
              value: 'configuration.list_O9R5.name',
              reference: 'name',
              description: '',
              selected: true,
            },
            {
              id: 'XyIxkelZ60R6o-e8z1_2J',
              key: 'age',
              type: 'reference',
              dataType: 'Number',
              value: 'configuration.list_O9R5.inputNumber_4NEk',
              reference: 'age',
              description: '',
              selected: true,
            },
          ],
          header: [],
          body: {
            type: 'form-data',
            payload: [],
          },
        },
        structure: {
          formItemId: '2704892a-5f9e-4588-b3c6-08694e079785',
          path: ['2704892a-5f9e-4588-b3c6-08694e079785'],
          type: 'root',
          props: {
            formItemConfig: {
              name: 'root',
              required: true,
            },
          },
          extends: {
            name: 'Feature 1',
          },
          children: [
            {
              formItemId: '16f88caf-8bf7-4ff6-a8b0-9f8590dcdc0e',
              path: [
                '2704892a-5f9e-4588-b3c6-08694e079785',
                '16f88caf-8bf7-4ff6-a8b0-9f8590dcdc0e',
              ],
              type: 'section',
              props: {
                formItemConfig: {
                  label: 'Section',
                  name: 'section_iVZx',
                  required: false,
                },
              },
              children: [
                {
                  path: [
                    '2704892a-5f9e-4588-b3c6-08694e079785',
                    '16f88caf-8bf7-4ff6-a8b0-9f8590dcdc0e',
                    'aESkAh80',
                  ],
                  formItemId: 'aESkAh80',
                  type: 'timePicker',
                  children: [],
                  props: {
                    formItemConfig: {
                      name: 'timePicker_cMxU',
                      label: 'Time Picker',
                      required: true,
                    },
                    generalConfig: {
                      dataType: 'Datetime',
                    },
                  },
                },
                {
                  path: [
                    '2704892a-5f9e-4588-b3c6-08694e079785',
                    '16f88caf-8bf7-4ff6-a8b0-9f8590dcdc0e',
                    'hy14NZqN',
                  ],
                  formItemId: 'hy14NZqN',
                  type: 'valuePicker',
                  children: [],
                  props: {
                    formItemConfig: {
                      name: 'valuePicker_v_By',
                      label: 'Value Picker',
                      layout: 'vertical',
                      required: true,
                    },
                    generalConfig: {
                      dataType: 'Object',
                    },
                  },
                },
                {
                  path: [
                    '2704892a-5f9e-4588-b3c6-08694e079785',
                    '16f88caf-8bf7-4ff6-a8b0-9f8590dcdc0e',
                    'etKG6Pcc',
                  ],
                  formItemId: 'etKG6Pcc',
                  type: 'valueMaker',
                  children: [],
                  props: {
                    formItemConfig: {
                      name: 'valueMaker_w_FX',
                      label: 'Value Maker',
                      layout: 'vertical',
                    },
                    generalConfig: {
                      dataType: 'Object',
                    },
                  },
                },
                {
                  formItemId: 'f870685a-23e5-40ce-9a18-8f5bd2a23228',
                  path: [
                    '2704892a-5f9e-4588-b3c6-08694e079785',
                    '16f88caf-8bf7-4ff6-a8b0-9f8590dcdc0e',
                    'f870685a-23e5-40ce-9a18-8f5bd2a23228',
                  ],
                  type: 'slider',
                  props: {
                    formItemConfig: {
                      label: 'Slider',
                      name: 'slider_fbDi',
                      required: false,
                    },
                    generalConfig: {
                      dataType: 'Number',
                    },
                  },
                  children: [],
                },
              ],
            },
            {
              formItemId: 'gaE0KpdL',
              path: ['2704892a-5f9e-4588-b3c6-08694e079785', 'gaE0KpdL'],
              type: 'section',
              props: {
                formItemConfig: {
                  label: 'Section',
                  name: 'section_vKid',
                  required: false,
                  wrapperCol: {
                    flex: 1,
                    xs: {
                      span: 24,
                    },
                  },
                },
              },
              children: [
                {
                  formItemId: '8dgVqk_y',
                  path: [
                    '2704892a-5f9e-4588-b3c6-08694e079785',
                    'gaE0KpdL',
                    '8dgVqk_y',
                  ],
                  type: 'list',
                  props: {
                    formItemConfig: {
                      label: 'Person',
                      name: 'person',
                      required: false,
                      layout: 'horizontal',
                    },
                    generalConfig: {
                      dataType: 'Array<Object>',
                    },
                  },
                  children: [
                    {
                      formItemId: 'ga5sDSSh',
                      path: [
                        '2704892a-5f9e-4588-b3c6-08694e079785',
                        'gaE0KpdL',
                        '8dgVqk_y',
                        'ga5sDSSh',
                      ],
                      type: 'input',
                      props: {
                        formItemConfig: {
                          label: 'Name',
                          name: 'name',
                          required: false,
                          wrapperCol: {
                            flex: 1,
                            xs: {
                              span: 24,
                            },
                          },
                          layout: 'horizontal',
                        },
                        generalConfig: {
                          dataType: 'String',
                        },
                        inputConfig: {
                          type: 'text',
                        },
                      },
                      children: [],
                      extends: {
                        listField: {
                          name: 0,
                          key: 0,
                          fieldKey: 0,
                        },
                      },
                      index: 4,
                    },
                    {
                      path: [
                        '2704892a-5f9e-4588-b3c6-08694e079785',
                        'gaE0KpdL',
                        '8dgVqk_y',
                        '66i2Yk7-',
                      ],
                      formItemId: '66i2Yk7-',
                      type: 'inputNumber',
                      children: [],
                      props: {
                        formItemConfig: {
                          name: 'age',
                          label: 'Age',
                          wrapperCol: {
                            flex: 1,
                            xs: {
                              span: 24,
                            },
                          },
                          layout: 'horizontal',
                        },
                        generalConfig: {
                          dataType: 'Number',
                        },
                        inputNumberConfig: {},
                      },
                      extends: {
                        listField: {
                          name: 0,
                          key: 0,
                          fieldKey: 0,
                        },
                      },
                      index: 4,
                    },
                  ],
                  index: 0,
                },
              ],
            },
          ],
        },
      },
    ],
  },

  {
    name: 'My Career Future',
    uuid: '20e94672-6ac1-430a-961c-7c7b01b8b196',
    description: 'To get list of jobs in Singapore',
    iconID: null,
    type: 'external',
    categoryID: 1,
    category: {
      id: 1,
      uuid: 'b52565e9-6e1e-4905-8bf7-d9985bea791e',
      name: 'Plugin Node',
      description: 'data',
      iconID: null,
      userID: 1,
      organizationID: 1,
    },
    endpoint: '',
    userID: 1,
    organizationID: 1,
    id: 93,
    pluginID: 55,
    version: 'a89643c5-bb93-4632-9c91-************',
    status: 'draft',
    createdAt: '2025-04-22T09:18:11.461Z',
    updatedAt: '2025-04-22T09:18:11.461Z',
    configurationForm: {
      formItemId: '2a7decde-7a1d-41e9-a16d-462931f6528f',
      path: ['2a7decde-7a1d-41e9-a16d-462931f6528f'],
      type: 'root',
      props: {
        formItemConfig: {
          name: 'root',
          required: true,
        },
      },
      children: [
        {
          formItemId: '0b9cd2ab-7278-40af-8ea5-13d9791c0ec4',
          path: [
            '2a7decde-7a1d-41e9-a16d-462931f6528f',
            '0b9cd2ab-7278-40af-8ea5-13d9791c0ec4',
          ],
          type: 'input',
          props: {
            formItemConfig: {
              label: 'Description',
              name: 'input_4K3t',
              required: false,
            },
            generalConfig: {
              dataType: 'String',
            },
          },
          children: [],
        },
        {
          formItemId: 'd87d85d4-3e9f-4b11-83a5-3d67156f54df',
          path: [
            '2a7decde-7a1d-41e9-a16d-462931f6528f',
            'd87d85d4-3e9f-4b11-83a5-3d67156f54df',
          ],
          type: 'input',
          props: {
            formItemConfig: {
              label: 'API Key',
              name: 'input_MaH_',
              required: false,
            },
            generalConfig: {
              dataType: 'String',
            },
          },
          children: [],
        },
      ],
    },
    propertyAndEndpointForms: [
      {
        name: 'Get Job',
        structure: {
          formItemId: 'f44f097b-4249-4bec-8aaa-6ff9ab209ba2',
          path: ['f44f097b-4249-4bec-8aaa-6ff9ab209ba2'],
          type: 'root',
          props: {
            formItemConfig: {
              name: 'root',
              required: true,
            },
          },
          extends: {
            name: 'Get Job',
          },
          children: [
            {
              formItemId: '178d1e4e-34dd-4041-8690-4b5cd869b44e',
              path: [
                'f44f097b-4249-4bec-8aaa-6ff9ab209ba2',
                '178d1e4e-34dd-4041-8690-4b5cd869b44e',
              ],
              type: 'section',
              props: {
                formItemConfig: {
                  label: 'Section',
                  name: 'section_4OKU',
                  required: false,
                },
              },
              children: [
                {
                  formItemId: '3c76f545-6c73-48f9-b639-294e181b1093',
                  path: [
                    'f44f097b-4249-4bec-8aaa-6ff9ab209ba2',
                    '178d1e4e-34dd-4041-8690-4b5cd869b44e',
                    '3c76f545-6c73-48f9-b639-294e181b1093',
                  ],
                  type: 'input',
                  props: {
                    formItemConfig: {
                      label: 'Query',
                      name: 'query_2OMI',
                      required: false,
                    },
                    generalConfig: {
                      condition: [
                        [
                          {
                            operator: '===',
                            targetKey: '',
                            targetVal: '',
                          },
                        ],
                      ],
                      dataType: 'String',
                    },
                  },
                  children: [],
                },
                {
                  formItemId: '43b2d462-127e-4552-9e52-f28bb9809cdd',
                  path: [
                    'f44f097b-4249-4bec-8aaa-6ff9ab209ba2',
                    '178d1e4e-34dd-4041-8690-4b5cd869b44e',
                    '43b2d462-127e-4552-9e52-f28bb9809cdd',
                  ],
                  type: 'input',
                  props: {
                    formItemConfig: {
                      label: 'Location',
                      name: 'location__3AB',
                      required: false,
                    },
                    generalConfig: {
                      dataType: 'String',
                    },
                  },
                  children: [],
                },
                {
                  formItemId: '289ac841-f5db-4130-82c5-f2da044b622b',
                  path: [
                    'f44f097b-4249-4bec-8aaa-6ff9ab209ba2',
                    '178d1e4e-34dd-4041-8690-4b5cd869b44e',
                    '289ac841-f5db-4130-82c5-f2da044b622b',
                  ],
                  type: 'input',
                  props: {
                    formItemConfig: {
                      label: 'Salary Minimum',
                      name: 'input_ebM-',
                      required: false,
                    },
                    generalConfig: {
                      dataType: 'String',
                    },
                  },
                  children: [],
                },
              ],
            },
          ],
        },
        endpoint: {
          url: {
            method: 'GET',
            path: 'https://api.careerfuture.com/v1/jobs',
          },
          query: [
            {
              id: '5DzQO5n5mF2zv3_xwCAq_',
              key: 'query',
              type: 'reference',
              dataType: 'String',
              value: 'section_4OKU.query_2OMI',
              reference: 'query',
              description: '',
              selected: true,
            },
            {
              id: 'ru8gCtEK48iy4k9o6kBxi',
              key: 'location',
              type: 'reference',
              dataType: 'String',
              value: 'section_4OKU.location__3AB',
              reference: 'location',
              description: '',
              selected: true,
            },
            {
              id: 'ZW4Qdnokw_rqz6i835q_E',
              key: 'salary_min',
              type: 'reference',
              dataType: 'String',
              value: 'section_4OKU.input_ebM-',
              reference: 'section_4OKU.input_ebM-',
              description: '',
              selected: true,
            },
          ],
          header: [
            {
              id: 'iyKjIM2CwKOJUPPfCBk4_',
              key: 'Content-Type',
              type: 'input',
              dataType: 'String',
              value: 'application/json',
              reference: '',
              description: '',
              selected: true,
            },
          ],
          body: {
            type: 'form-data',
            payload: [],
          },
          output: {
            type: 'fixed',
            structure: [],
          },
        },
      },
      {
        name: 'Search Job',
        structure: {
          formItemId: '74b04291-c602-42aa-a669-77bb660325bb',
          path: ['74b04291-c602-42aa-a669-77bb660325bb'],
          type: 'root',
          props: {
            formItemConfig: {
              name: 'root',
              required: true,
            },
          },
          extends: {
            name: 'Search Job',
          },
          children: [
            {
              formItemId: 'e326e732-8471-4d2e-928b-36cb0972bb36',
              path: [
                '74b04291-c602-42aa-a669-77bb660325bb',
                'e326e732-8471-4d2e-928b-36cb0972bb36',
              ],
              type: 'section',
              props: {
                formItemConfig: {
                  label: 'Section',
                  name: 'section_hnsW',
                  required: false,
                },
              },
              children: [
                {
                  formItemId: '815e01f4-ac6d-479e-94d1-8b8268b88194',
                  path: [
                    '74b04291-c602-42aa-a669-77bb660325bb',
                    'e326e732-8471-4d2e-928b-36cb0972bb36',
                    '815e01f4-ac6d-479e-94d1-8b8268b88194',
                  ],
                  type: 'input',
                  props: {
                    formItemConfig: {
                      label: 'Query',
                      name: 'query_HZve',
                      required: false,
                    },
                    generalConfig: {
                      dataType: 'String',
                    },
                  },
                  children: [],
                },
                {
                  formItemId: 'c2e952b7-b40a-4484-8dc8-5f97eff31d98',
                  path: [
                    '74b04291-c602-42aa-a669-77bb660325bb',
                    'e326e732-8471-4d2e-928b-36cb0972bb36',
                    'c2e952b7-b40a-4484-8dc8-5f97eff31d98',
                  ],
                  type: 'input',
                  props: {
                    formItemConfig: {
                      label: 'Salary Range',
                      name: 'salary_range_qWu9',
                      required: false,
                    },
                    generalConfig: {
                      dataType: 'String',
                    },
                  },
                  children: [],
                },
              ],
            },
          ],
        },
        endpoint: {
          url: {
            method: 'POST',
            path: 'https://api.careerfuture.com/v1/jobs',
          },
          query: [
            {
              id: 'oJrueycKUAdqNW5hPuCIr',
              key: 'query',
              type: 'reference',
              dataType: 'String',
              value: 'section_hnsW.query_HZve',
              reference: 'section_hnsW.query_HZve',
              description: '',
              selected: true,
            },
            {
              id: 'Z3ptLgDglbvslfUtaLAQ4',
              key: 'salary_range',
              type: 'reference',
              dataType: 'String',
              value: 'section_hnsW.salary_range_qWu9',
              reference: 'section_hnsW.salary_range_qWu9',
              description: '',
              selected: true,
            },
          ],
          header: [],
          body: {
            type: 'form-data',
            payload: [],
          },
          output: {
            type: 'fixed',
            structure: [],
          },
        },
      },
    ],
  },
]

export default fakeData
