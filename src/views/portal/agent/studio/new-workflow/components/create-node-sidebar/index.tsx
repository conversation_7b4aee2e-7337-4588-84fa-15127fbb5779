import { memo, useEffect, useMemo, useState } from 'react'
import { Menu, MenuProps } from 'antd'
import { LeftOutlined } from '@ant-design/icons'
import { merge } from 'lodash-es'
import { nanoid } from 'nanoid'

import CustomAvatar from 'components/custom-avatar'
import PluginColor from 'assets/images/plugin-color.svg'

import styles from './index.scss'
import fakeData from './fake'
import { useWorkflowStore } from 'stores/new-workflow'
import { FormItemType } from 'components/new-dynamic-form/types'
import BaseNode, { BaseNodeType, NodeTypeEnum } from '../../lib/base-node'
import { getDefaultNodeData, getNodeClass } from '../../nodes'
import { WorkflowTypeNodes } from '../../nodes/constant'
import { createNode } from '../../lib/utils'
import NodeDefaultNode from '../../nodes/node-default'
import { WorkflowNodeBaseDataType } from '../../lib/workflow-node'
import { PluginNodeBaseDataType } from '../../lib/plugin-node'

type NodeItemType = {
  name: string
  id: string
  nodeData: WorkflowNodeBaseDataType | PluginNodeBaseDataType
  iconId?: string
  nodeType?: string
}

type SelectorNodeType = {
  title: string
  nodes: NodeItemType[]
  type: NodeTypeEnum
}

const defaultWorkflowNodeList: SelectorNodeType[] = [
  {
    title: 'Workflow Node',
    nodes: Object.entries(WorkflowTypeNodes)
      .map(([k, v]) => {
        const id = `${k}_${nanoid(8)}`
        const defaultData = v.getDefaultNodeData(id)
        return {
          name: defaultData.data?.name ?? k,
          id: defaultData.id ?? id,
          nodeData: defaultData.data.nodeData ?? {},
          nodeType: defaultData.data.nodeType,
        }
      })
      .filter((node) => node.nodeType !== 'start'),
    type: NodeTypeEnum.Workflow,
  },
]

export type CreateNodeSidebarProps = {
  sourceNodeId: string
  targetNodeId?: string
  sourceHandleId?: string | null
  targetHandleId?: string | null
}

const CreateNodeSidebar = (props: CreateNodeSidebarProps) => {
  const { sourceNodeId, targetNodeId, sourceHandleId, targetHandleId } = props
  const { allNodes, refreshWorkflowTree, setSidebarConfig } = useWorkflowStore()
  const [nodeList, setNodeList] = useState<SelectorNodeType[]>([])

  const [selectorNode, setSelectorNode] = useState<NodeItemType>()

  const initPage = async () => {
    // const pluginResp = await v1PluginInstalledList({ page: 1, limit: 100 })
    // if (!pluginResp.data) return
    // const { data } = pluginResp.data

    const pluginNodeList = fakeData.reduce<SelectorNodeType[]>((acc, cur) => {
      const { category, id, name, icon } = cur
      const curNodeItem = {
        name,
        id: `${id}`,
        iconId: icon?.uuid,
        nodeData: cur,
      }
      const existingCategory = acc.find((item) => item.title === category.name)
      if (existingCategory) existingCategory.nodes.push(curNodeItem)
      else
        acc.push({
          title: category.name,
          nodes: [curNodeItem],
          type: NodeTypeEnum.Plugin,
        })
      return acc
    }, [])

    // 1. insert new node between source and target
    let filterWorkflowNodeList = defaultWorkflowNodeList
    if (sourceNodeId && allNodes) {
      const sourceNode = Reflect.get(allNodes, sourceNodeId)

      // @TODO: Maybe have two End Node in a branch
      if (sourceNode.node.data.children.length) {
        filterWorkflowNodeList = filterWorkflowNodeList.map((category) => ({
          ...category,
          nodes:
            category.type === NodeTypeEnum.Workflow
              ? category.nodes.filter((node) => node.nodeType !== 'end')
              : category.nodes,
        }))
      }
    }

    console.error(
      [...filterWorkflowNodeList, ...pluginNodeList].filter(
        (category) => category.nodes.length
      )
    )

    setNodeList(
      [...filterWorkflowNodeList, ...pluginNodeList].filter(
        (category) => category.nodes.length
      )
    )
  }

  const handleNodeItemClick = (node: NodeItemType, type: NodeTypeEnum) => {
    const propertyFormItems = getPropertyFormItems(node, type)
    if (!propertyFormItems.length) {
      const newNode = createNewNodeInstance(node, type)
      handleCreateNewNode(newNode)
      setSidebarConfig({ open: false, type: '' })
    } else {
      setSelectorNode(node)
    }
  }

  const handlePropertyFormItemClick = (type: NodeTypeEnum, form: any) => {
    if (!selectorNode) return
    const newNode = createNewNodeInstance(selectorNode, type, form)
    handleCreateNewNode(newNode)
    setSidebarConfig({ open: false, type: '' })
  }

  const createNewNodeInstance = (
    node: NodeItemType,
    type: NodeTypeEnum,
    form?: { name: string; structure: FormItemType; endpoint: any }
  ) => {
    const { name, nodeType } = node
    const curNodeType = nodeType ?? 'nodeDefault'

    const NodeClass = getNodeClass(curNodeType, NodeDefaultNode)
    const curNodeData: DeepPartial<BaseNodeType<'data'>> = {
      data: { nodeData: { ...node.nodeData, propertyForm: form }, name },
    }

    if (allNodes) {
      const list = Object.values(allNodes)
        .filter(
          (node) =>
            node instanceof NodeClass && node.node.data.name.startsWith(name)
        )
        .sort()

      let idx = 1
      for (const cur of list) {
        if (cur.node.data.name.startsWith(`${name}_${idx}`)) {
          idx++
          continue
        }
        break
      }

      merge(curNodeData, { data: { name: `${name}_${idx}` } })
    }

    return new NodeClass(
      merge(getDefaultNodeData(type, curNodeType), curNodeData)
    )
  }

  const handleCreateNewNode = (newNode: BaseNode) => {
    if (!allNodes) return
    if (!targetNodeId) {
      createNode(newNode, {
        sourceNode: Reflect.get(allNodes, sourceNodeId),
        sourceHandleId,
        targetHandleId,
      })
    } else {
      createNode(newNode, {
        sourceNode: Reflect.get(allNodes, sourceNodeId),
        targetNode: Reflect.get(allNodes, targetNodeId),
        sourceHandleId,
        targetHandleId,
      })
    }

    refreshWorkflowTree()
  }

  const getPropertyFormItems = (node: NodeItemType, type: NodeTypeEnum) => {
    let curNodePropertyFormItems:
      | WorkflowNodeBaseDataType['propertyForms']
      | PluginNodeBaseDataType['propertyAndEndpointForms'] = []
    if (type === NodeTypeEnum.Workflow) {
      curNodePropertyFormItems = (node.nodeData as WorkflowNodeBaseDataType)
        ?.propertyForms
    } else if (type === NodeTypeEnum.Plugin) {
      curNodePropertyFormItems = (node.nodeData as PluginNodeBaseDataType)
        ?.propertyAndEndpointForms
    }
    // console.error('curNodePropertyFormItems', curNodePropertyFormItems)
    if (!curNodePropertyFormItems) return []
    const propertyFormItems = curNodePropertyFormItems.map((form) => ({
      key: form.structure.formItemId,
      label: form.name,
      onClick: () => handlePropertyFormItemClick(type, form),
    }))

    return propertyFormItems
  }

  const propertyFormItems = useMemo(() => {
    if (!selectorNode) return []

    return getPropertyFormItems(
      selectorNode,
      selectorNode.nodeType ? NodeTypeEnum.Workflow : NodeTypeEnum.Plugin
    )
  }, [selectorNode])

  useEffect(() => {
    initPage()
  }, [sourceNodeId])

  // Render
  const nodeItems: MenuProps['items'] = useMemo(
    () =>
      nodeList.map((section) => ({
        key: `section-${section.title}`,
        type: 'group',
        label: section.title,
        children: section.nodes.map((node, toolIndex) => ({
          key: `${section.title}-${toolIndex}`,
          icon: (
            <CustomAvatar
              iconUUID={node.iconId}
              size={16}
              shape="square"
              defaultIcon={<PluginColor fontSize={16} />}
            />
          ),
          label: node.name,
          onClick: () => handleNodeItemClick(node, section.type),
        })),
      })),
    [nodeList]
  )

  return (
    <div className={styles.createNodeSidebarWrapper}>
      <div className={styles.createNodeSidebarHeader}>
        {selectorNode && (
          <LeftOutlined
            onClick={() => setSelectorNode(undefined)}
            className={styles.createNodeSidebarHeaderBackIcon}
          />
        )}
        <CustomAvatar
          iconUUID={selectorNode?.iconId}
          className={styles.createNodeSidebarHeaderAvatar}
          shape="square"
          size={32}
          defaultIcon={<PluginColor fontSize={36} />}
        />

        <div className={styles.createNodeSidebarHeaderTitle}>
          Select {selectorNode ? 'Property Form' : 'Node'}
        </div>
      </div>

      <Menu
        selectedKeys={[]}
        mode="vertical"
        items={!selectorNode ? nodeItems : propertyFormItems}
      />
    </div>
  )
}

export default memo(CreateNodeSidebar)
