import { Badge } from 'antd'
import CheckList from 'assets/images/check-list.svg'

interface CheckListIconProps {
  className?: string
  isChecked?: boolean
}

export const CheckListIcon = ({ className, isChecked }: CheckListIconProps) => {
  const icon = (
    <CheckList
      className={className}
      style={{ fontSize: 20, verticalAlign: 'middle' }}
      aria-label="Check List Icon"
      tabIndex={0}
    />
  )

  if (isChecked) {
    return (
      <Badge dot color="#FF5C5C" offset={[-4, 4]}>
        {icon}
      </Badge>
    )
  }

  return icon
}
