import { Space } from 'antd'
import React, { memo } from 'react'
import { ChecklistItem } from './item'
import type { CheckListItemType } from './types'

interface CheckListProps {
  items: CheckListItemType[]
}

const CheckList = (props: CheckListProps) => {
  const { items = [] } = props
  return (
    <Space direction="vertical" size={12} style={{ width: '100%' }}>
      {React.Children.toArray(items.map((item) => <ChecklistItem {...item} />))}
    </Space>
  )
}

export default memo(CheckList)
