.card {
  border-radius: var(--ant-border-radius-lg);
  border: 1px solid var(--ant-color-border);
  background-color: var(--ant-layout-body-bg);
}

.header {
  padding: var(--ant-padding-xs);
}

.headerIcon {
  font-size: 24px;
}

.headerTitle {
  padding: 0;
  margin: 0;
  font-size: var(--ant-font-size);
  font-weight: 700;
}

.message {
  padding-block: var(--ant-padding-xs);
  padding-inline: var(--ant-padding-sm);
  background: var(--genie-color-bg-container);
  color: var(--ant-color-text-secondary);
  font-size: var(--ant-font-size-sm);
}

.messageIcon {
}
