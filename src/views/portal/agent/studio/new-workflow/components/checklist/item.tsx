import { Empty, Flex, Space } from 'antd'
import React from 'react'
import styles from './index.scss'
import { Message } from './message'
import type { CheckListMessageType } from './types'

interface CheckListItemProps {
  icon: React.ReactNode
  title: string
  messages?: CheckListMessageType[]
}

export function ChecklistItem(props: CheckListItemProps) {
  if (!props.messages?.length) {
    return (
      <Flex className={styles.card}>
        <Empty description="No messages" />
      </Flex>
    )
  }

  return (
    <Flex className={styles.card} vertical>
      <Space size="small" className={styles.header}>
        <div className={styles.headerIcon}>{props.icon}</div>
        <h2 className={styles.headerTitle}>{props.title}</h2>
      </Space>
      {React.Children.toArray(
        props.messages?.map((message) => <Message {...message} />)
      )}
    </Flex>
  )
}
