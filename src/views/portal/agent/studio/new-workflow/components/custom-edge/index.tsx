import Icon from '@ant-design/icons'
import {
  <PERSON>Edge,
  EdgeLabelRenderer,
  EdgeProps,
  getBezierPath,
} from '@xyflow/react'
import SvgPlus from 'assets/images/plus.svg'
import cls from 'classnames'
import { memo } from 'react'
import { useWorkflowStore } from 'stores/new-workflow'
import styles from './index.scss'

const CustomEdge = (props: EdgeProps) => {
  const {
    sourceX,
    sourceY,
    targetX,
    targetY,
    sourcePosition,
    targetPosition,
    source,
    target,
    sourceHandleId,
    targetHandleId,
  } = props

  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  })

  const { setSidebarConfig, allNodes } = useWorkflowStore()

  const handleClick = (e: React.MouseEvent) => {
    // prevent event bubbling to parent containers
    e.stopPropagation()
    e.preventDefault()

    // check if source or target node is inside a LoopNode
    const sourceNode = allNodes && Reflect.get(allNodes, source)
    const targetNode = allNodes && Reflect.get(allNodes, target)

    const isInsideLoop =
      (sourceNode?.node.parentId &&
        allNodes &&
        Reflect.get(allNodes, sourceNode.node.parentId)?.node.data.nodeType ===
          'loop') ||
      (targetNode?.node.parentId &&
        allNodes &&
        Reflect.get(allNodes, targetNode.node.parentId)?.node.data.nodeType ===
          'loop')

    setSidebarConfig({
      open: true,
      type: isInsideLoop ? 'createLoopNode' : 'createNode',
      createNodeInfo: {
        sourceNodeId: source,
        targetNodeId: target,
        sourceHandleId,
        targetHandleId,
      },
    })
  }

  return (
    <>
      <BaseEdge {...props} path={edgePath} />
      <EdgeLabelRenderer>
        <div
          className={cls('nodrag', 'nopan', styles['custom-edge'])}
          style={{
            transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
            zIndex: 1000,
          }}
        >
          <Icon
            className={styles['custom-edge-label-mid']}
            component={SvgPlus}
            onClick={handleClick}
            style={{
              pointerEvents: 'all',
              cursor: 'pointer',
              zIndex: 1001,
            }}
          />
        </div>
      </EdgeLabelRenderer>
    </>
  )
}

export default memo(CustomEdge)
