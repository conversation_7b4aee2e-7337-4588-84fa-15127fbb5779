import { memo } from 'react'
import {
  BaseEdge,
  EdgeLabelRenderer,
  EdgeProps,
  getBezierPath,
} from '@xyflow/react'
import cls from 'classnames'
import Icon from '@ant-design/icons'
import SvgPlus from 'assets/images/plus.svg'
import styles from './index.scss'
import { useWorkflowStore } from 'stores/new-workflow'

const CustomEdge = (props: EdgeProps) => {
  const {
    sourceX,
    sourceY,
    targetX,
    targetY,
    sourcePosition,
    targetPosition,
    source,
    target,
    sourceHandleId,
    targetHandleId,
  } = props

  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  })

  const { setSidebarConfig } = useWorkflowStore()

  const handleClick = () => {
    setSidebarConfig({
      open: true,
      type: 'createNode',
      createNodeInfo: {
        sourceNodeId: source,
        targetNodeId: target,
        sourceHandleId,
        targetHandleId,
      },
    })
  }

  return (
    <>
      <BaseEdge {...props} path={edgePath} />
      <EdgeLabelRenderer>
        <div
          className={cls('nodrag', 'nopan', styles.customEdge)}
          style={{
            transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
          }}
        >
          <Icon
            className={styles.customEdgeLabelMid}
            component={SvgPlus}
            onClick={handleClick}
          />
        </div>
      </EdgeLabelRenderer>
    </>
  )
}

export default memo(CustomEdge)
