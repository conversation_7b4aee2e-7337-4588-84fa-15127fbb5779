import { Drawer } from 'antd'
import { memo, useMemo, useRef } from 'react'
import { useWorkflowStore } from 'stores/new-workflow'

import { SidebarViewMap } from '../../nodes'
import styles from './index.scss'

import type BaseNode from '../../lib/base-node'
import CreateLoopNodeSidebar from '../create-loop-node-sidebar'
import CreateNodeSidebar from '../create-node-sidebar'

type SidebarProps = {}

const SidebarWrapper = (props: SidebarProps) => {
  const { sidebarConfig, currentNodeId, allNodes } = useWorkflowStore()
  const { open, type: sidebarType, createNodeInfo } = sidebarConfig

  const parentRef = useRef(null)
  const curNode = useMemo(() => {
    if (!allNodes) return
    const currentNode = Reflect.get(allNodes, currentNodeId)

    return currentNode
  }, [currentNodeId, allNodes])

  // Render
  const getCurrentNodeSidebar = (nodeInstance: BaseNode) => {
    if (!nodeInstance) return null
    const { node } = nodeInstance
    let SidebarView = Reflect.get(SidebarViewMap, node.data.nodeType)

    if (!SidebarView) {
      SidebarView = SidebarViewMap.nodeDefault
    }

    return <SidebarView node={nodeInstance} />
  }

  return (
    <div className={styles.sidebarRootWrapper} ref={parentRef}>
      <Drawer
        className={styles.SidebarWrapper}
        mask={false}
        closeIcon={null}
        getContainer={parentRef.current ?? window.document.body}
        open={open}
        forceRender
        width={452}
      >
        {sidebarType === 'nodeDetail' &&
          curNode &&
          getCurrentNodeSidebar(curNode)}
        {sidebarType === 'createNode' && createNodeInfo && (
          <CreateNodeSidebar {...createNodeInfo} />
        )}
        {sidebarType === 'createLoopNode' && createNodeInfo && (
          <CreateLoopNodeSidebar {...createNodeInfo} />
        )}
      </Drawer>
    </div>
  )
}

export default memo(SidebarWrapper)
