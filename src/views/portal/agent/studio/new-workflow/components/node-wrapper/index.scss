.node-wrapper {
  &-wrapper {
    position: relative;
    padding: 12px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    border-radius: 8px;
    border: 1px solid #eaecef;
    background: #fff;
    max-width: 300px;

    &:hover {
      .node-wrapper-delete {
        opacity: 1;
      }
    }
  }

  &-info {
    &-wrapper {
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      column-gap: 4px;
    }

    &-title {
      color: #2a2c2f;
      font-size: 16px;
      line-height: 24px;
      font-weight: 700;
    }
  }

  &-description {
    color: #2a2c2f;
    font-size: 14px;
    font-weight: 500;
  }

  &-content {
    width: 100%;
  }

  &-active {
    border-radius: 8px;
    border: 1px solid #3278d9;
    box-shadow:
      0px 1px 2px 0px rgba(10, 13, 18, 0.05),
      0px 0px 0px 4px #def;

    background-color: white;
  }

  &-delete {
    position: absolute;
    top: -12px;
    right: -12px;
    cursor: pointer;
    opacity: 0;
    color: #a3a3a3;
    width: 16px;
    height: 16px;

    &:hover {
      color: #3278d9;
    }
  }
}
