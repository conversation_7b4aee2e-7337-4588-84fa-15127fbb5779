import React, { useMemo } from 'react'
import cls from 'classnames'
import { ApiOutlined } from '@ant-design/icons'
import { CloseCircleOutlined } from '@ant-design/icons'

import { useWorkflowStore } from 'stores/new-workflow'
import { isLocal } from 'utils/env'
import { deleteNode } from '../../lib/utils'

import CustomAvatar from 'components/custom-avatar'
import styles from './index.scss'

import type { NodeProps } from '@xyflow/react'
import type { BaseNodeType } from '../../lib/base-node/type'
import { WorkflowNodeIcon } from './constant'

const NodeWrapper = (WrappedComponent: React.ComponentType<any>) => {
  const NodeView = (props: NodeProps<BaseNodeType<'data'>>) => {
    const { id, name, nodeData, description, nodeType } = props.data
    const {
      allNodes,
      currentNodeId,
      workflowTree,
      setCurrentNodeId,
      setSidebarConfig,
      refreshWorkflowTree,
    } = useWorkflowStore()
    const isActiveNode = id === currentNodeId
    const canDelete = useMemo(() => {
      if (nodeType === 'start') return false
      if (allNodes) {
        const lastEndNodes = Object.values(allNodes).filter(
          (node) => node.node.data.nodeType === 'end'
        )
        if (lastEndNodes.length <= 1) return false
      }
      return true
    }, [nodeType, allNodes])

    const handleNodeClick = () => {
      setCurrentNodeId(id)
      setSidebarConfig({ open: true, type: 'nodeDetail' })
      if (isLocal) {
        if (!allNodes) return
        console.error('handleNodeClick', Reflect.get(allNodes, id))
      }
    }

    const handleNodeDeleteClick: React.MouseEventHandler<HTMLDivElement> = (
      e
    ) => {
      if (!allNodes || !workflowTree) return
      e.stopPropagation()
      const currentNode = Reflect.get(allNodes, id)

      deleteNode(currentNode, workflowTree)
      setSidebarConfig({ open: false, type: '' })
      refreshWorkflowTree()
    }

    return (
      <div
        className={cls(styles.nodeWrapperWrapper, {
          [styles.nodeWrapperActive]: isActiveNode,
        })}
        onClick={handleNodeClick}
      >
        {canDelete && (
          <CloseCircleOutlined
            className={styles.nodeWrapperDelete}
            onClick={handleNodeDeleteClick}
          />
        )}

        <div className={styles.nodeWrapperInfoWrapper}>
          <CustomAvatar
            size={32}
            iconUUID={nodeData?.icon?.uuid}
            defaultIcon={WorkflowNodeIcon[nodeType] ?? <ApiOutlined />}
          />
          <div className={styles.nodeWrapperInfoTitle}>{name}</div>
        </div>

        <div className={styles.nodeWrapperDescription}>
          {description ?? nodeData?.description}
        </div>

        <div className={styles.nodeWrapperContent}>
          <WrappedComponent {...props} />
        </div>
      </div>
    )
  }

  return NodeView
}

export default NodeWrapper
