import { LeftOutlined } from '@ant-design/icons'
import { Menu, MenuProps } from 'antd'
import { merge } from 'lodash-es'
import { nanoid } from 'nanoid'
import { memo, useEffect, useMemo, useState } from 'react'

import PluginColor from 'assets/images/plugin-color.svg'
import CustomAvatar from 'components/custom-avatar'

import { v2WorkflowNodesGroupedByCategoryList } from 'api/Api'
import { FormItemType } from 'components/new-dynamic-form/types'
import { useWorkflowStore } from 'stores/new-workflow'
import BaseNode from '../../lib/base-node'
import { BaseNodeType, NodeTypeEnum } from '../../lib/base-node/type'
import { PluginNodeBaseDataType } from '../../lib/plugin-node'
import { createNode } from '../../lib/utils'
import { WorkflowNodeBaseDataType } from '../../lib/workflow-node'
import { getDefaultNodeData, getNodeClass } from '../../nodes'
import { WorkflowTypeNodes } from '../../nodes/constant'
import LoopStartNode from '../../nodes/loop/components/loop-start'
import NodeDefaultNode from '../../nodes/node-default'
import styles from '../create-node-sidebar/index.scss'

export type NodeDataType = WorkflowNodeBaseDataType | PluginNodeBaseDataType

type NodeItemType = {
  name: string
  id: string
  nodeData: NodeDataType
  iconId?: string
  nodeType?: string
}

type SelectorNodeType = {
  title: string
  nodes: NodeItemType[]
  type: NodeTypeEnum
}

// for loop containers, we want to show all workflow nodes including loopStart
const defaultWorkflowNodeList: SelectorNodeType[] = [
  {
    title: 'Workflow Node',
    nodes: Object.entries(WorkflowTypeNodes)
      .map(([k, v]) => {
        const id = `${k}_${nanoid(8)}`
        const defaultData = v.getDefaultNodeData(id)
        return {
          name: defaultData.data?.name ?? k,
          id: defaultData.id ?? id,
          nodeData: defaultData.data.nodeData ?? {},
          nodeType: defaultData.data.nodeType,
        }
      })
      .filter(
        (node) =>
          node.nodeType !== 'start' &&
          node.nodeType !== 'end' &&
          node.nodeType !== 'loopStart' &
      ), // exclude regular start, but allow loopStart
    type: NodeTypeEnum.Workflow,
  },
]

export type CreateLoopNodeSidebarProps = {
  sourceNodeId: string // this should be the loop container ID
  targetNodeId?: string
  sourceHandleId?: string | null
  targetHandleId?: string | null
}

const CreateLoopNodeSidebar = (props: CreateLoopNodeSidebarProps) => {
  const { sourceNodeId, targetNodeId, sourceHandleId, targetHandleId } = props
  const {
    allNodes,
    refreshWorkflowTree,
    setSidebarConfig,
    setCurrentNodeId,
    setNode,
  } = useWorkflowStore()
  const [nodeList, setNodeList] = useState<SelectorNodeType[]>([])
  const [selectorNode, setSelectorNode] = useState<NodeItemType>()
  const [propertyFormItems, setPropertyFormItems] = useState<
    MenuProps['items']
  >([])

  const initPage = async () => {
    const pluginResp = await v2WorkflowNodesGroupedByCategoryList()
    if (!pluginResp.data) return
    const { categories } = pluginResp.data

    const pluginNodeList = categories.reduce<SelectorNodeType[]>((pre, cur) => {
      const { name, nodes } = cur
      const curCategory = {
        title: name,
        nodes: nodes.map((plugin) => ({
          name: plugin.name,
          id: `${plugin.pluginId}`,
          nodeData: { ...plugin, iconId: `${plugin.iconId}` },
          iconId: `${plugin.iconId}`,
          nodeType: plugin.name,
        })),
        type: NodeTypeEnum.Plugin,
      }

      pre.push(curCategory)
      return pre
    }, [])

    // for loop containers, we don't need to filter end nodes
    let filterWorkflowNodeList = defaultWorkflowNodeList

    setNodeList(
      [...filterWorkflowNodeList, ...pluginNodeList].filter(
        (category) => category.nodes.length
      )
    )
  }

  const handleNodeItemClick = (node: NodeItemType, type: NodeTypeEnum) => {
    setSelectorNode(node)
    const propertyFormItems = getPropertyFormItems(node, type)
    if (!propertyFormItems.length) {
      const newNode = createNewNodeInstance(node, type)
      setSelectorNode(undefined)
      handleCreateNewNode(newNode)
    } else {
      setPropertyFormItems(propertyFormItems)
    }
  }

  const handlePropertyFormItemClick = (
    node: NodeItemType,
    type: NodeTypeEnum,
    form: any
  ) => {
    const newNode = createNewNodeInstance(node, type, form)
    handleCreateNewNode(newNode)
  }

  const createNewNodeInstance = (
    node: NodeItemType,
    type: NodeTypeEnum,
    form?: { name: string; structure: FormItemType; endpoint: any }
  ) => {
    const { name, nodeType } = node
    const curNodeType = nodeType ?? 'nodeDefault'

    const NodeClass = getNodeClass(curNodeType, NodeDefaultNode)
    const curNodeData: DeepPartial<BaseNodeType<'data'>> = {
      data: {
        nodeData: { ...node.nodeData, propertyForm: form },
        name,
        description: node.nodeData?.description ?? '',
      },
    }

    if (allNodes) {
      const list = Object.values(allNodes)
        .filter(
          (node) =>
            node instanceof NodeClass && node.node.data.name.startsWith(name)
        )
        .sort()

      let idx = 1
      for (const cur of list) {
        if (cur.node.data.name.startsWith(`${name}_${idx}`)) {
          idx++
          continue
        }
        break
      }

      merge(curNodeData, { data: { name: `${name}_${idx}` } })
    }

    return new NodeClass(
      merge(getDefaultNodeData(type, curNodeType), curNodeData)
    )
  }

  const handleCreateNewNode = (newNode: BaseNode) => {
    if (!allNodes) return

    // special handling for loop node creation
    if (newNode.node.data.nodeType === 'loop') {
      handleCreateLoopNode(newNode)
      return
    }

    // for nodes created inside loop containers, set parentId and extent
    const sourceNode = Reflect.get(allNodes, sourceNodeId)
    if (sourceNode?.node.data.nodeType === 'loop') {
      // set React Flow sub-flow properties
      newNode.node.parentId = sourceNodeId
      newNode.node.extent = 'parent'
    }

    // use standard node creation logic
    if (!targetNodeId) {
      createNode(newNode, {
        sourceNode: sourceNode,
        sourceHandleId,
        targetHandleId,
      })
    } else {
      createNode(newNode, {
        sourceNode: sourceNode,
        targetNode: Reflect.get(allNodes, targetNodeId),
        sourceHandleId,
        targetHandleId,
      })
    }

    setSidebarConfig({ open: true, type: 'nodeDetail' })
    setCurrentNodeId(newNode.node.data.id)
    refreshWorkflowTree()
  }

  const handleCreateLoopNode = (loopNode: BaseNode) => {
    if (!allNodes) return

    // first create the loop node using standard logic
    const sourceNode = Reflect.get(allNodes, sourceNodeId)
    if (!targetNodeId) {
      createNode(loopNode, {
        sourceNode: sourceNode,
        sourceHandleId,
        targetHandleId,
      })
    } else {
      createNode(loopNode, {
        sourceNode: sourceNode,
        targetNode: Reflect.get(allNodes, targetNodeId),
        sourceHandleId,
        targetHandleId,
      })
    }

    // now create the mandatory LoopStartNode inside the loop
    const loopStartId = `${loopNode.node.data.id}_start_${nanoid(4)}`
    const loopStartNodeData = LoopStartNode.getDefaultNodeData(loopStartId, {
      parentId: loopNode.node.data.id,
      extent: 'parent',
      position: { x: 60, y: 60 }, // positioned inside the loop container
    })

    const loopStartNode = new LoopStartNode(loopStartNodeData)

    // set up parent-child relationship
    loopStartNode.node.data.parents.push(loopNode)
    loopNode.node.data.children.push(loopStartNode)

    // add LoopStartNode to workflow store
    setNode(loopStartNode.node.data.id, loopStartNode)

    setSidebarConfig({ open: true, type: 'nodeDetail' })
    setCurrentNodeId(loopNode.node.data.id)
    refreshWorkflowTree()
  }

  const getPropertyFormItems = (node: NodeItemType, type: NodeTypeEnum) => {
    let curNodePropertyFormItems:
      | WorkflowNodeBaseDataType['propertyForms']
      | PluginNodeBaseDataType['propertyAndEndpointForms'] = []
    if (type === NodeTypeEnum.Workflow) {
      curNodePropertyFormItems = (node.nodeData as WorkflowNodeBaseDataType)
        ?.propertyForms
    } else if (type === NodeTypeEnum.Plugin) {
      curNodePropertyFormItems = (node.nodeData as PluginNodeBaseDataType)
        ?.propertyAndEndpointForms
    }
    if (!curNodePropertyFormItems) return []
    const propertyFormItems = curNodePropertyFormItems.map((form) => ({
      key: form.structure.formItemId,
      label: form.name,
      onClick: () => handlePropertyFormItemClick(node, type, form),
    }))

    return propertyFormItems
  }

  useEffect(() => {
    initPage()
  }, [sourceNodeId])

  // Render
  const nodeItems: MenuProps['items'] = useMemo(
    () =>
      nodeList.map((section) => ({
        key: `section-${section.title}`,
        type: 'group',
        label: section.title,
        children: section.nodes.map((node, toolIndex) => ({
          key: `${section.title}-${toolIndex}`,
          icon: (
            <CustomAvatar
              iconUUID={node.iconId}
              size={16}
              shape="square"
              defaultIcon={<PluginColor fontSize={16} />}
            />
          ),
          label: node.name,
          onClick: () => handleNodeItemClick(node, section.type),
        })),
      })),
    [nodeList]
  )

  return (
    <div className={styles.createNodeSidebarWrapper}>
      <div className={styles.createNodeSidebarHeader}>
        {selectorNode && (
          <LeftOutlined
            onClick={() => setSelectorNode(undefined)}
            className={styles.createNodeSidebarHeaderBackIcon}
          />
        )}
        <CustomAvatar
          iconUUID={selectorNode?.iconId}
          className={styles.createNodeSidebarHeaderAvatar}
          shape="square"
          size={32}
          defaultIcon={<PluginColor fontSize={36} />}
        />

        <div className={styles.createNodeSidebarHeaderTitle}>
          Select {selectorNode ? 'Property Form' : 'Node'}
        </div>
      </div>

      {!selectorNode?.name ? (
        <Menu selectedKeys={[]} mode="vertical" items={nodeItems} />
      ) : (
        <Menu selectedKeys={[]} mode="vertical" items={propertyFormItems} />
      )}
    </div>
  )
}

export default memo(CreateLoopNodeSidebar)
