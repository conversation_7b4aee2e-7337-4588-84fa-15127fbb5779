import { nanoid } from 'nanoid'
import { Position } from '@xyflow/react'
import { merge } from 'lodash-es'
import { BaseNodeType, NodeTypeEnum } from '../../lib/base-node'
import WorkflowNode, { WorkflowNodeDataType } from '../../lib/workflow-node'
import NodeDefaultNode from '../node-default'
import { propertyForm, output } from './constants'

import StartSidebarView from './sidebar'

class StartNode extends WorkflowNode {
  static NodeView = NodeDefaultNode.NodeView
  static SidebarView = StartSidebarView

  static getDefaultNodeData = (
    id: string,
    cfg?: Partial<BaseNodeType<'data'>>
  ): WorkflowNodeDataType => {
    return merge(
      {
        handles: [
          {
            id: `${id}_source_${nanoid()}`,
            type: 'source',
            position: Position.Right,
            nodeId: id,
            x: 0,
            y: 0,
            width: 0,
            height: 0,
          },
        ],
        data: {
          id,
          name: 'Start',
          nodeType: 'start',
          description: 'Start Node',
          nodeData: {
            type: 'internal',
            propertyForms: [
              { name: 'ChatBot Start', structure: propertyForm, output },
              { name: 'SmartApi Start', structure: propertyForm, output },
            ],
            // TODO: According the workflow type choose one. (SmartApi or Chatbot)
            propertyForm: {
              name: 'ChatBot Start',
              structure: propertyForm,
              output,
            },
          },
          parents: [],
          children: [],
        },
        id,
        type: NodeTypeEnum.Workflow,
        edges: [],
        position: { x: 0, y: 0 },
      },
      cfg
    )
  }

  constructor(node: BaseNodeType<'data'>) {
    super(node, { Node: NodeDefaultNode.NodeView, Sidebar: StartSidebarView })
  }
}

export default StartNode
