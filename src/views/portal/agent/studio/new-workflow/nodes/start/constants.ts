import { PluginPluginOutputFormType } from 'api/data-contracts'
import { FormItemType } from 'components/new-dynamic-form/types'

export const propertyForm: FormItemType = {
  path: ['f7425b49-c74d-4ca0-ab4b-43ae2624d82c'],
  formItemId: 'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
  type: 'root',
  children: [
    {
      path: ['f7425b49-c74d-4ca0-ab4b-43ae2624d82c', 'yooiGrBd'],
      formItemId: 'yooiGrBd',
      type: 'section',
      children: [
        {
          path: [
            'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
            'yooiGrBd',
            'JcBW75-U',
          ],
          formItemId: 'JcBW75-U',
          type: 'switch',
          children: [],
          props: {
            formItemConfig: {
              name: 'chat_to_document',
              label: 'Chat to Document',
              layout: 'horizontal',
            },
            generalConfig: {
              dataType: 'Boolean',
            },
            switchConfig: {
              defaultValue: false,
            },
          },
          index: 0,
        },
        {
          path: [
            'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
            'yooiGrBd',
            'aFkPIZfY',
          ],
          formItemId: 'aFkPIZfY',
          type: 'inputNumber',
          children: [],
          props: {
            formItemConfig: {
              name: 'max_document_size',
              label: 'Max Document Size',
              layout: 'horizontal',
            },
            generalConfig: {
              dataType: 'Number',
              condition: [
                {
                  logicType: 'And',
                  groupName: ' Group 1',
                  items: [
                    {
                      target: { type: 'input', value: 'true' },
                      sourceKey: 'settings.chat_to_document',
                      operator: '==',
                    },
                  ],
                },
              ],
            },
            inputNumberConfig: {
              defaultValue: 0,
              min: 0,
            },
          },
          index: 1,
        },
        {
          path: [
            'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
            'yooiGrBd',
            '_dTtYzgE',
          ],
          formItemId: '_dTtYzgE',
          type: 'inputNumber',
          children: [],
          props: {
            formItemConfig: {
              name: 'max_length_characters',
              label: 'Max Length of Characters',
              layout: 'horizontal',
            },
            generalConfig: {
              dataType: 'Number',
              condition: [
                {
                  logicType: 'And',
                  groupName: ' Group 1',
                  items: [
                    {
                      target: { type: 'input', value: 'true' },
                      sourceKey: 'settings.chat_to_document',
                      operator: '==',
                    },
                  ],
                },
              ],
            },
            inputNumberConfig: {
              min: 0,
            },
          },
          index: 2,
        },
        {
          path: [
            'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
            'yooiGrBd',
            'a3r1uzX7',
          ],
          formItemId: 'a3r1uzX7',
          type: 'switch',
          children: [],
          props: {
            formItemConfig: {
              name: 'chat_to_image',
              label: 'Chat to Image',
              layout: 'horizontal',
            },
            generalConfig: {
              dataType: 'Boolean',
            },
            switchConfig: {
              defaultValue: false,
            },
          },
          index: 3,
        },
        {
          path: [
            'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
            'yooiGrBd',
            'R8LnV9-U',
          ],
          formItemId: 'R8LnV9-U',
          type: 'switch',
          children: [],
          props: {
            formItemConfig: {
              name: 'voice_conversation',
              label: 'Voice Conversation',
              layout: 'horizontal',
            },
            generalConfig: {
              dataType: 'Boolean',
            },
            switchConfig: {
              defaultValue: false,
            },
          },
          index: 4,
        },
      ],
      props: {
        formItemConfig: {
          name: 'settings',
          label: 'Settings',
          layout: 'horizontal',
        },
        generalConfig: {},
      },
      index: 0,
    },
    {
      path: ['f7425b49-c74d-4ca0-ab4b-43ae2624d82c', '5zBXz4HE'],
      formItemId: '5zBXz4HE',
      type: 'section',
      children: [
        {
          path: [
            'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
            '5zBXz4HE',
            'rPR5XTf5',
          ],
          formItemId: 'rPR5XTf5',
          type: 'list',
          children: [
            {
              path: [
                'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
                '5zBXz4HE',
                'rPR5XTf5',
                'us93FI7C',
              ],
              formItemId: 'us93FI7C',
              type: 'input',
              children: [],
              props: {
                formItemConfig: {
                  name: 'name',
                  label: 'Name',
                  wrapperCol: {
                    flex: 1,
                    xs: {
                      span: 24,
                    },
                  },
                  layout: 'horizontal',
                },
                generalConfig: {
                  dataType: 'String',
                },
                inputConfig: {
                  placeholder: 'Enter name',
                  type: 'text',
                },
              },
              extends: {
                listField: {
                  name: 0,
                  key: 0,
                  fieldKey: 0,
                },
                index: 4,
              },
            },
            {
              path: [
                'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
                '5zBXz4HE',
                'rPR5XTf5',
                'tpVAqEod',
              ],
              formItemId: 'tpVAqEod',
              type: 'select',
              children: [],
              props: {
                formItemConfig: {
                  name: 'type',
                  label: 'Type',
                  wrapperCol: {
                    flex: 1,
                    xs: {
                      span: 24,
                    },
                  },
                  layout: 'horizontal',
                },
                generalConfig: {
                  dataType: 'String',
                },
                selectConfig: {
                  options: [
                    [
                      {
                        label: 'Label',
                        value: '1',
                      },
                      {
                        label: 'Value',
                        value: '1',
                      },
                    ],
                    [
                      {
                        label: 'Label',
                        value: '2',
                      },
                      {
                        label: 'Value',
                        value: '2',
                      },
                    ],
                  ],
                },
              },
              extends: {
                listField: {
                  name: 0,
                  key: 0,

                  fieldKey: 0,
                },
              },

              index: 4,
            },
            {
              path: [
                'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
                '5zBXz4HE',
                'rPR5XTf5',
                'Qdk0iZn3',
              ],
              formItemId: 'Qdk0iZn3',
              type: 'input',
              children: [],
              props: {
                formItemConfig: {
                  name: 'description',
                  label: 'Description',
                  wrapperCol: {
                    flex: 1,
                    xs: {
                      span: 24,
                    },
                  },
                  layout: 'horizontal',
                },
                generalConfig: {
                  dataType: 'String',
                },
                inputConfig: {
                  placeholder: 'Description',
                  type: 'text',
                },
              },
              extends: {
                listField: {
                  name: 0,
                  key: 0,

                  fieldKey: 0,
                },
              },
              index: 4,
            },
          ],
          props: {
            formItemConfig: {
              name: 'list_QKgw',
              label: '',
              layout: 'horizontal',
            },
            generalConfig: {
              dataType: 'Array<Object>',
            },
            listConfig: { buttonText: 'Add' },
          },
          index: 0,
        },
      ],
      props: {
        formItemConfig: {
          name: 'custom_variables',
          label: 'Custom Variables',
          layout: 'horizontal',
        },
        generalConfig: {},
      },
      index: 1,
    },
    {
      path: ['f7425b49-c74d-4ca0-ab4b-43ae2624d82c', 'qcyK3n9O'],
      formItemId: 'qcyK3n9O',
      type: 'section',
      children: [
        {
          path: [
            'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
            'qcyK3n9O',
            'D8blJWXW',
          ],
          formItemId: 'D8blJWXW',
          type: 'switch',
          children: [],
          props: {
            formItemConfig: {
              name: 'enable_pii_filter',
              label: 'Enable PII Filter',
              layout: 'horizontal',
            },
            generalConfig: {
              dataType: 'Boolean',
            },
            switchConfig: {
              defaultValue: false,
            },
          },
          index: 0,
        },
        {
          path: [
            'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
            'qcyK3n9O',
            'E_mPDcPR',
          ],
          formItemId: 'E_mPDcPR',
          type: 'input',
          children: [],
          props: {
            formItemConfig: {
              name: 'placeholder',
              label: 'Placeholder',
              layout: 'horizontal',
            },
            generalConfig: {
              dataType: 'String',
              condition: [
                {
                  logicType: 'And',
                  groupName: ' Group 1',
                  items: [
                    {
                      target: {
                        type: 'input',
                        value: 'true',
                      },
                      sourceKey: 'pii_filter.enable_pii_filter',
                      operator: '==',
                    },
                  ],
                },
              ],
            },
            inputConfig: {
              type: 'text',
            },
          },
          index: 1,
        },
        {
          path: [
            'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
            'qcyK3n9O',
            '4aKs9cuL',
          ],
          formItemId: '4aKs9cuL',
          type: 'inputNumber',
          children: [],
          props: {
            formItemConfig: {
              name: 'threshold',
              label: 'Threshold',
              layout: 'horizontal',
            },
            generalConfig: {
              dataType: 'Number',
              condition: [
                {
                  logicType: 'And',
                  groupName: ' Group 1',
                  items: [
                    {
                      target: {
                        type: 'input',
                        value: 'true',
                      },
                      sourceKey: 'pii_filter.enable_pii_filter',
                      operator: '==',
                    },
                  ],
                },
              ],
            },
            inputNumberConfig: {
              defaultValue: 0,
              min: 0,
            },
          },
          index: 2,
        },
        {
          path: [
            'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
            'qcyK3n9O',
            'J5R5Nn2c',
          ],
          formItemId: 'J5R5Nn2c',
          type: 'list',
          children: [
            {
              path: [
                'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
                'qcyK3n9O',
                'J5R5Nn2c',
                'dC5Ow63_',
              ],
              formItemId: 'dC5Ow63_',
              type: 'input',
              children: [],
              props: {
                formItemConfig: {
                  name: 'item',
                  label: '',
                  wrapperCol: {
                    flex: 1,
                    xs: {
                      span: 24,
                    },
                  },
                  layout: 'horizontal',
                },
                generalConfig: {
                  dataType: 'String',
                },
                inputConfig: {
                  type: 'text',
                },
              },
              extends: {
                listField: {
                  name: 0,
                  key: 0,

                  fieldKey: 0,
                },
              },
              index: 4,
            },
          ],
          props: {
            formItemConfig: {
              name: 'labels',
              label: 'Labels',
              layout: 'horizontal',
            },
            generalConfig: {
              dataType: 'Array<Object>',
              condition: [
                {
                  logicType: 'And',
                  groupName: ' Group 1',
                  items: [
                    {
                      target: {
                        type: 'input',
                        value: 'true',
                      },
                      sourceKey: 'pii_filter.enable_pii_filter',
                      operator: '==',
                    },
                  ],
                },
              ],
            },
            listConfig: { buttonText: 'Add Label' },
          },
          index: 3,
        },
        {
          path: [
            'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
            'qcyK3n9O',
            'lOv6ZFC5',
          ],
          formItemId: 'lOv6ZFC5',
          type: 'list',
          children: [
            {
              path: [
                'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
                'qcyK3n9O',
                'lOv6ZFC5',
                'oGYYBX_3',
              ],
              formItemId: 'oGYYBX_3',
              type: 'input',
              children: [],
              props: {
                formItemConfig: {
                  name: 'item',
                  label: '',
                  wrapperCol: {
                    flex: 1,
                    xs: {
                      span: 24,
                    },
                  },
                  layout: 'horizontal',
                },
                generalConfig: {
                  dataType: 'String',
                },
                inputConfig: {
                  type: 'text',
                },
              },
              extends: {
                listField: {
                  name: 0,
                  key: 0,

                  fieldKey: 0,
                },
              },
              index: 4,
            },
          ],
          props: {
            formItemConfig: {
              name: 'whitelist',
              label: 'Whitelist',
              labelCol: {
                span: 24,
              },
              wrapperCol: {
                flex: 1,
                xs: {
                  span: 24,
                },
              },
              layout: 'horizontal',
            },
            generalConfig: {
              dataType: 'Array<String>',
              condition: [
                {
                  logicType: 'And',
                  groupName: ' Group 1',
                  items: [
                    {
                      target: {
                        type: 'input',
                        value: 'true',
                      },
                      sourceKey: 'pii_filter.enable_pii_filter',
                      operator: '==',
                    },
                  ],
                },
              ],
            },
            listConfig: { buttonText: 'Add Whitelist' },
          },
          extends: {
            listField: {
              name: 0,
              key: 0,

              fieldKey: 0,
            },
          },
          index: 4,
        },
      ],
      props: {
        formItemConfig: {
          name: 'pii_filter',
          label: 'PII Filter',
          layout: 'horizontal',
        },
        generalConfig: {},
      },
      index: 0,
    },
    {
      path: ['f7425b49-c74d-4ca0-ab4b-43ae2624d82c', 'ZfxgtIKU'],
      formItemId: 'ZfxgtIKU',
      type: 'section',
      children: [
        {
          path: [
            'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
            'ZfxgtIKU',
            'yyhyjRkV',
          ],
          formItemId: 'yyhyjRkV',
          type: 'switch',
          children: [],
          props: {
            formItemConfig: {
              name: 'enable_for_documents',
              label: 'Enable for Documents',
              layout: 'horizontal',
            },
            generalConfig: {
              dataType: 'Boolean',
            },
            switchConfig: {
              defaultValue: false,
            },
          },
          index: 0,
        },
        {
          path: [
            'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
            'ZfxgtIKU',
            'uCiPZq-T',
          ],
          formItemId: 'uCiPZq-T',
          type: 'list',
          children: [
            {
              path: [
                'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
                'ZfxgtIKU',
                'uCiPZq-T',
                'iTYZNeEX',
              ],
              formItemId: 'iTYZNeEX',
              type: 'input',
              children: [],
              props: {
                formItemConfig: {
                  name: 'regex',
                  label: 'Regex',
                  wrapperCol: {
                    flex: 1,
                    xs: {
                      span: 24,
                    },
                  },
                  layout: 'horizontal',
                },
                generalConfig: {
                  dataType: 'String',
                },
                inputConfig: {
                  type: 'text',
                },
              },
              extends: {
                listField: {
                  name: 0,
                  key: 0,

                  fieldKey: 0,
                },
              },
              index: 4,
            },
            {
              path: [
                'f7425b49-c74d-4ca0-ab4b-43ae2624d82c',
                'ZfxgtIKU',
                'uCiPZq-T',
                'VJx-FpTS',
              ],
              formItemId: 'VJx-FpTS',
              type: 'input',
              children: [],
              props: {
                formItemConfig: {
                  name: 'placeholder',
                  label: 'Placeholder',
                  wrapperCol: {
                    flex: 1,
                    xs: {
                      span: 24,
                    },
                  },
                  layout: 'horizontal',
                },
                generalConfig: {
                  dataType: 'String',
                },
                inputConfig: {
                  type: 'text',
                },
              },
              extends: {
                listField: {
                  name: 0,
                  key: 0,

                  fieldKey: 0,
                },
              },
              index: 4,
            },
          ],
          props: {
            formItemConfig: {
              name: 'regex_items',
              label: 'Regex Items',
              layout: 'horizontal',
            },
            generalConfig: {
              dataType: 'Array<Object>',
            },
            listConfig: { buttonText: 'Add Keyword' },
          },
          index: 1,
        },
      ],
      props: {
        formItemConfig: {
          name: 'regex_filter',
          label: 'Regex Filter',
          layout: 'horizontal',
        },
        generalConfig: {},
      },
      index: 3,
    },
  ],
  props: {
    formItemConfig: {
      name: 'root',
      required: true,
    },
  },
}

export const output = {
  type: PluginPluginOutputFormType.Fixed,
  structure: [
    {
      key: '6a2f6811-391a-4f58-9b05-626f6491d14c',
      name: 'Test1',
      type: 'String',
      required: true,
      deep: 1,
    },
    {
      key: 'bf9c3577-207b-4afe-9da9-d3b4e1cb1215',
      name: 'Test2',
      type: 'Object',
      required: false,
      deep: 1,
      children: [
        {
          key: '7e50f2c6-d23d-4db3-a787-1725f0cf2dcf',
          name: 'Test3',
          type: 'String',
          required: true,
          deep: 2,
        },
      ],
    },
    {
      key: '830af807-d3c7-4376-aad7-9ffaa4b62caf',
      name: 'Test4',
      type: 'Array<String>',
      required: false,
      deep: 1,
    },
  ],
}
