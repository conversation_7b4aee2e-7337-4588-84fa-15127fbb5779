import { nanoid } from 'nanoid'
import { merge } from 'lodash-es'

import PluginNode from '../lib/plugin-node'
import RootNode from './root'
import NodeDefaultNode from './node-default'
import StartNode from './start'
import EndNode from './end'

import { BaseNodeType, NodeTypeEnum } from '../lib/base-node'
import WorkflowNode from '../lib/workflow-node'
import { WorkflowNodeMap, WorkflowTypeNodes } from './constant'
import LogicConditionNode from './logic-condition'

export function getNodeClass(
  nodeType: string
): typeof PluginNode | typeof RootNode | typeof WorkflowNode | null
export function getNodeClass(
  nodeType: string,
  defaultNodeClass: typeof PluginNode
): typeof PluginNode | typeof RootNode | typeof WorkflowNode
export function getNodeClass(
  nodeType: string,
  defaultNodeClass?: typeof PluginNode
) {
  let NodeClass = Reflect.get(WorkflowNodeMap, nodeType)
  if (NodeClass) return NodeClass
  return defaultNodeClass ?? null
}

export const SidebarViewMap = {
  root: RootNode.SidebarView,
  nodeDefault: NodeDefaultNode.SidebarView,
  start: StartNode.SidebarView,
  end: EndNode.SidebarView,
  logicCondition: LogicConditionNode.SidebarView,
}

export const getDefaultNodeData = (type: NodeTypeEnum, nodeType: string) => {
  const id = nanoid(8)
  let defaultNodeData = undefined
  if (type === NodeTypeEnum.Workflow) {
    const WorkflowNodeClass = Reflect.get(WorkflowTypeNodes, nodeType)
    if (WorkflowNodeClass)
      defaultNodeData = WorkflowNodeClass.getDefaultNodeData(id)
  } else {
    defaultNodeData = PluginNode.getDefaultNodeData(id)
  }

  const nodeData: BaseNodeType<'data'> = merge(
    {
      id,
      type,
      data: {
        id,
        name: '',
        nodeType,
        nodeData: {},
        inputs: {},
        extends: {},
        parents: [],
        children: [],
      },
      position: { x: 0, y: 0 },
      edges: [],
      handles: [],
    },
    defaultNodeData
  )

  return nodeData
}
