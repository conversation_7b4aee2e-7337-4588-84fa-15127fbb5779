import EndNode from './end'
import LogicConditionNode from './logic-condition'
import LoopN<PERSON> from './loop'
import LoopStartNode from './loop/components/loop-start'
import NodeDefaultNode from './node-default'
import RootNode from './root'
import StartNode from './start'

export const WorkflowTypeNodes = {
  start: StartNode,
  end: EndNode,
  loop: LoopNode,
  loopStart: LoopStartNode,
  logicCondition: LogicConditionNode,
}

export const WorkflowNodeMap = {
  root: RootNode,
  nodeDefault: NodeDefaultNode,
  ...WorkflowTypeNodes,
}
