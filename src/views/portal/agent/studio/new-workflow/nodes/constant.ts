import EndNode from './end'
import LogicC<PERSON>itionNode from './logic-condition'
import LoopN<PERSON> from './loop'
import NodeDefaultNode from './node-default'
import RootNode from './root'
import StartNode from './start'

export const WorkflowTypeNodes = {
  start: StartNode,
  end: EndNode,
  loop: LoopNode,
  logicCondition: LogicConditionNode,
}

export const WorkflowNodeMap = {
  root: RootNode,
  nodeDefault: NodeDefaultNode,
  ...WorkflowTypeNodes,
}
