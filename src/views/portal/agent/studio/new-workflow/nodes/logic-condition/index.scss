.logic-condition-node {
  &-wrapper {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  &-branch {
    &-wrapper {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    &-item {
      position: relative;
      display: flex;
      height: 32px;
      padding: 0px 12px;
      align-items: center;
      border-radius: 4px;
      background: #f7f9fa;

      font-weight: 500;
      font-size: 14px;

      &-handler {
        position: absolute;
        right: -12px;
      }
    }
  }
}
