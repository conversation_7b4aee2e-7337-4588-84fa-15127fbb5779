import { memo, useEffect, useMemo, useState } from 'react'
import { Handle, NodeProps, Position } from '@xyflow/react'
import { WorkflowNodeDataType, WorkflowNodeType } from '../../lib/workflow-node'

import CustomHandle from '../../components/custom-handle'
import NodeWrapper from '../../components/node-wrapper'

import styles from './index.scss'
import { ConditionGroup } from 'components/new-dynamic-form/components/condition'
import { nanoid } from 'nanoid'
import { useWorkflowStore } from 'stores/new-workflow'

type LogicConditionNodeViewProps = NodeProps<WorkflowNodeDataType> & {
  node: WorkflowNodeType
}

type SourceHandle = Handle & { conditionName: string }

const LogicConditionNodeView = (props: LogicConditionNodeViewProps) => {
  const { id, data, node } = props

  const { allNodes, setNode } = useWorkflowStore()
  const [sourceHandles, setSourceHandles] = useState<SourceHandle[]>([])
  const elseHandle = useMemo<SourceHandle>(
    () => ({
      id: `${id}_source_${nanoid(8)}`,
      conditionName: `Else`,
      type: 'source',
      position: Position.Right,
      nodeId: id,
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    }),
    []
  )

  const targetHandles = node?.handles?.filter((h) => h.type === 'target') ?? []

  useEffect(() => {
    const branches = data.inputs?.propertyForm?.conditionBranches.branches
    if (!branches) return

    const newHandles = branches.map((branch) => conditionToHandle(branch, node))
    setSourceHandles([...newHandles, elseHandle])

    if (allNodes) {
      const node = Reflect.get(allNodes, id)
      node.node.handles = [...targetHandles, ...newHandles, elseHandle]
      setNode(node.node.id, node)
    }
  }, [node?.data.inputs?.propertyForm?.conditionBranches.branches])

  return (
    <div className={styles.logicConditionNodeWrapper}>
      {targetHandles.map((handle) => (
        <CustomHandle
          key={handle.id}
          curNode={node}
          handle={handle}
          type="target"
          position={Position.Left}
          isConnectable={props.isConnectable}
        />
      ))}

      <div className={styles.logicConditionNodeBranchWrapper}>
        {sourceHandles?.map?.((handle) => (
          <div
            key={handle.conditionName}
            className={styles.logicConditionNodeBranchItem}
          >
            <span>{handle.conditionName}</span>
            {/* <span>{handle.id}</span> */}
            <CustomHandle
              key={handle.id}
              curNode={node}
              handle={handle}
              type="source"
              position={Position.Right}
              isConnectable={props.isConnectable}
              customClassName={styles.logicConditionNodeBranchItemHandler}
            />
          </div>
        ))}
      </div>
    </div>
  )
}

const conditionToHandle = (
  condition: ConditionGroup[number],
  node: WorkflowNodeType
): SourceHandle => ({
  id: `${node.id}_source_${condition.id}`,
  conditionName: condition.groupName,
  type: 'source',
  position: Position.Right,
  nodeId: node.id,
  x: 0,
  y: 0,
  width: 0,
  height: 0,
})

export default NodeWrapper(memo(LogicConditionNodeView))
