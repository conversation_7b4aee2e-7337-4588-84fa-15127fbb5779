import { Position } from '@xyflow/react'
import { merge } from 'lodash-es'
import { nanoid } from 'nanoid'
import { BaseNodeType, NodeTypeEnum } from '../../lib/base-node'
import WorkflowNode, { WorkflowNodeDataType } from '../../lib/workflow-node'
import { propertyForm, output } from './constants'

import LogicConditionNodeView from './node'
import LogicConditionSidebarView from './sidebar'
import { LogicTypeEnum } from 'components/new-dynamic-form/components/condition/constants'

class LogicConditionNode extends WorkflowNode {
  readonly type = NodeTypeEnum.Workflow
  static NodeView = LogicConditionNodeView
  static SidebarView = LogicConditionSidebarView
  static getDefaultNodeData = (
    id: string,
    cfg?: Partial<BaseNodeType<'data'>>
  ): WorkflowNodeDataType => {
    return merge(
      {
        type: NodeTypeEnum.Workflow,
        handles: [
          {
            id: `${id}_target_${nanoid(8)}`,
            type: 'target',
            position: Position.Left,
            nodeId: id,
            x: 0,
            y: 0,
            width: 0,
            height: 0,
          },
        ],
        data: {
          name: 'Logic Condition',
          nodeType: 'logicCondition',
          description: 'Define the branches for differential processing',
          nodeData: {
            type: 'internal',
            propertyForms: [
              { name: 'Logic Condition', structure: propertyForm, output },
            ],
          },
          id,
          parents: [],
          children: [],
          inputs: {
            propertyForm: {
              conditionBranches: {
                branches: [
                  {
                    id: nanoid(8),
                    logicType: LogicTypeEnum.and,
                    groupName: ' Group 1',
                    items: [],
                  },
                ],
              },
            },
          },
        },
        id,
        edges: [],
        position: { x: 0, y: 0 },
      },
      cfg
    )
  }

  constructor(node: BaseNodeType<'data'>) {
    super(node, {
      Node: LogicConditionNodeView,
      Sidebar: LogicConditionSidebarView,
    })
  }
}

export default LogicConditionNode
