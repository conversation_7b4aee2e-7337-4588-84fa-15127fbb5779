import { Position } from '@xyflow/react'
import { merge } from 'lodash-es'
import { nanoid } from 'nanoid'
import { BaseNodeType, NodeTypeEnum } from '../../lib/base-node/type'
import WorkflowNode, {
  WorkflowNodeBaseDataType,
  WorkflowNodeInputsDataType,
} from '../../lib/workflow-node'
import { propertyForm, output } from './constants'

import LogicConditionNodeView, { LogicConditionNodeDataType } from './node'
import LogicConditionSidebarView from './sidebar'
import { LogicTypeEnum } from 'components/new-dynamic-form/components/condition/constants'
import { ConditionGroup } from 'components/new-dynamic-form/components/condition'

class LogicConditionNode extends WorkflowNode<
  BaseNodeType<
    'instance',
    WorkflowNodeBaseDataType,
    WorkflowNodeInputsDataType & {
      outputBranch: Record<string, Array<string>>
      propertyForm: Record<string, any> & {
        conditionBranches: { branches: ConditionGroup }
      }
    }
  >
> {
  static NodeView = LogicConditionNodeView
  static SidebarView = LogicConditionSidebarView
  static getDefaultNodeData = (
    id: string,
    cfg?: Partial<BaseNodeType<'data'>>
  ): LogicConditionNodeDataType => {
    return merge(
      {
        type: NodeTypeEnum.Workflow,
        handles: [
          {
            id: `${id}_target_${nanoid(4)}`,
            type: 'target',
            position: Position.Left,
            nodeId: id,
            x: 0,
            y: 0,
            width: 0,
            height: 0,
          },
        ],
        data: {
          name: 'Logic Condition',
          nodeType: 'logicCondition',
          description: 'Define the branches for differential processing',
          nodeData: {
            name: 'Logic Condition',
            iconId: '',
            type: 'internal',
            propertyForms: [
              {
                name: 'Logic Condition',
                structure: propertyForm,
                output,
                description: 'Define the branches for differential processing',
              },
            ],
          },
          id,
          parents: [],
          children: [],
          inputs: {
            propertyForm: {
              conditionBranches: {
                branches: [
                  {
                    id: nanoid(4),
                    logicType: LogicTypeEnum.and,
                    groupName: 'Condition 1',
                    items: [],
                  },
                ],
              },
            },
            outputBranch: {},
          },
        },
        id,
        edges: [],
        position: { x: 0, y: 0 },
      },
      cfg
    )
  }

  constructor(node: BaseNodeType<'data'>) {
    super(node, {
      Node: LogicConditionNodeView,
      Sidebar: LogicConditionSidebarView,
    })
  }
}

export default LogicConditionNode
