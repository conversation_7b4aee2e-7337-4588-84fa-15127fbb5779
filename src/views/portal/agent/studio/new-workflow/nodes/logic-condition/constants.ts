import { PluginPluginOutputFormType } from 'api/data-contracts'
import { FormItemType } from 'components/new-dynamic-form/types'

export const propertyForm: FormItemType = {
  path: ['disGlaod'],
  formItemId: 'disGlaod',
  type: 'root',
  children: [
    {
      path: ['disGlaod', 'yooiGrBd'],
      formItemId: 'yooiGrBd',
      type: 'section',
      children: [
        {
          path: ['disGlaod', 'yooiGrBd', 'pE56ULsK'],
          formItemId: 'pE56ULsK',
          type: 'condition',
          children: [],
          props: {
            formItemConfig: {
              name: 'branches',
              label: '',
              layout: 'horizontal',
            },
            generalConfig: {
              dataType: 'String',
            },
            conditionConfig: {
              hasElse: true,
              max: null,
              min: 1,
            },
          },
          index: 1,
        },
      ],
      props: {
        formItemConfig: {
          name: 'conditionBranches',
          label: 'Condition Branches',
          layout: 'horizontal',
        },
        generalConfig: {},
      },
      index: 0,
    },
  ],
  props: {
    formItemConfig: {
      name: 'root',
      required: true,
    },
  },
}

export const output = {
  type: PluginPluginOutputFormType.Fixed,
  structure: [
    {
      key: '6a2f6811-391a-4f58-9b05-626f6491d14c',
      name: 'Test1',
      type: 'String',
      required: true,
      deep: 1,
    },
    {
      key: 'bf9c3577-207b-4afe-9da9-d3b4e1cb1215',
      name: 'Test2',
      type: 'Object',
      required: false,
      deep: 1,
      children: [
        {
          key: '7e50f2c6-d23d-4db3-a787-1725f0cf2dcf',
          name: 'Test3',
          type: 'String',
          required: true,
          deep: 2,
        },
      ],
    },
    {
      key: '830af807-d3c7-4376-aad7-9ffaa4b62caf',
      name: 'Test4',
      type: 'Array<String>',
      required: false,
      deep: 1,
    },
  ],
}
