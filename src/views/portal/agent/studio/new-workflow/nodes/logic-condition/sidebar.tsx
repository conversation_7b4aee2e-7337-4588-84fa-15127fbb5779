import { memo } from 'react'
import WorkflowNode from '../../lib/workflow-node'
import DefaultNodeSidebar from '../../components/default-node-sidebar'

type LogicConditionSidebarViewProps = {
  node: WorkflowNode
}

const LogicConditionSidebarView = (props: LogicConditionSidebarViewProps) => {
  const { node } = props

  return <DefaultNodeSidebar node={node} />
}

export default memo(LogicConditionSidebarView)
