// Loop Container Styles
.loopContainer {
  min-width: 200px;
  min-height: 100px;
  border: 1px solid var(--ant-color-primary);
  border-radius: 8px;
  box-shadow: 0 2px 8px
    color-mix(in srgb, var(--ant-color-primary) 15%, transparent);
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;

  // ensure child elements (including edges) can receive pointer events
  pointer-events: auto;

  // allow child elements to be interactive
  * {
    pointer-events: auto;
  }

  &:hover {
    border-color: color-mix(in srgb, var(--ant-color-primary) 80%, white);
    box-shadow: 0 4px 12px
      color-mix(in srgb, var(--ant-color-primary) 25%, transparent);
  }

  &.selected {
    border-color: var(--ant-color-primary);
    box-shadow: 0 0 0 4px
      color-mix(in srgb, var(--ant-color-primary) 20%, transparent);
  }
  /** Resize handle styles **/
  :global(.react-flow__resize-control.handle) {
    border-radius: 0;
    width: 5px;
    height: 5px;
  }
}

// Loop Header Styles
.loopHeader {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  border-bottom: 1px solid #f0f0f0;
  background-color: var(--genie-color-bg-container);
  gap: 12px;
  position: relative;
}

.loopIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: linear-gradient(
    135deg,
    var(--ant-color-primary),
    color-mix(in srgb, var(--ant-color-primary) 80%, white)
  );
  border-radius: 6px;
  color: white;
  font-size: 16px;
  flex-shrink: 0;
}

.loopTitle {
  flex: 1;
  min-width: 0;
}

.loopName {
  font-size: 14px;
  font-weight: 600;
  color: var(--ant-color-text);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.loopType {
  font-size: 12px;
  color: var(--ant-color-text-tertiary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.addNodeButton {
  flex-shrink: 0;
  color: var(--ant-color-primary);
  border: 1px solid var(--ant-color-primary);
  border-radius: 4px;

  &:hover {
    background: color-mix(in srgb, var(--ant-color-primary) 10%, transparent);
    border-color: color-mix(in srgb, var(--ant-color-primary) 80%, white);
    color: color-mix(in srgb, var(--ant-color-primary) 80%, white);
  }

  &:focus {
    background: color-mix(in srgb, var(--ant-color-primary) 10%, transparent);
    border-color: var(--ant-color-primary);
    color: var(--ant-color-primary);
  }
}

// Subflow Content Area
.subflowContent {
  padding: 16px;
  position: relative;
  flex: 1;
  overflow: hidden;
}

.deleteButton {
  position: absolute;
  top: -12px;
  right: -12px;
  cursor: pointer;
  opacity: 0;
  width: 16px;
  height: 16px;

  &:hover {
    color: var(--ant-color-primary);
  }
}

.loopContainer:hover .deleteButton {
  opacity: 1;
}
