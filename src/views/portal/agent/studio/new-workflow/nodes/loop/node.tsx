import {
  CloseCircleOutlined,
  PlusOutlined,
  ReloadOutlined,
} from '@ant-design/icons'
import { NodeProps, NodeResizer, Position } from '@xyflow/react'
import { Button } from 'antd'
import classNames from 'classnames'
import { memo } from 'react'

import { useWorkflowStore } from 'stores/new-workflow'
import CustomHandle from '../../components/custom-handle'
import type {
  WorkflowNodeDataType,
  WorkflowNodeType,
} from '../../lib/workflow-node'

import { deleteNode } from '../../lib/utils'
import styles from './index.scss'

type LoopNodeViewProps = NodeProps<WorkflowNodeDataType> & {
  node: WorkflowNodeType
}

const LoopNodeView = (props: LoopNodeViewProps) => {
  const { data, selected, node } = props
  const {
    setSidebarConfig,
    setCurrentNodeId,
    allNodes,
    workflowTree,
    refreshWorkflowTree,
  } = useWorkflowStore()

  // Get handles for connections
  const targetHandles = node?.handles?.filter((h) => h.type === 'target') ?? []
  const sourceHandles = node?.handles?.filter((h) => h.type === 'source') ?? []

  const handleAddNode = () => {
    setSidebarConfig({
      open: true,
      type: 'createLoopNode',
      createNodeInfo: {
        sourceNodeId: data.id,
        sourceHandleId: undefined,
        targetNodeId: undefined,
        targetHandleId: undefined,
      },
    })
  }

  const handleNodeClick = () => {
    setCurrentNodeId(data.id)
    setSidebarConfig({ open: true, type: 'nodeDetail' })
  }

  const handleNodeDeleteClick: React.MouseEventHandler<HTMLDivElement> = (
    e
  ) => {
    if (!allNodes || !workflowTree) return
    e.stopPropagation()
    const currentNode = Reflect.get(allNodes, data.id)

    deleteNode(currentNode, workflowTree)
    setSidebarConfig({ open: false, type: '' })
    refreshWorkflowTree()
  }

  return (
    <div
      className={classNames(styles.loopContainer, {
        [styles.selected]: selected,
      })}
      onClick={handleNodeClick}
    >
      {/* Resizer */}
      <NodeResizer />
      {/* Close button */}
      <CloseCircleOutlined
        className={styles.deleteButton}
        onClick={handleNodeDeleteClick}
      />
      {/* Connection Handles */}
      {targetHandles.map((handle) => (
        <CustomHandle
          key={handle.id}
          curNode={node}
          handle={handle}
          type="target"
          position={Position.Left}
          isConnectable={props.isConnectable}
        />
      ))}
      {sourceHandles.map((handle) => (
        <CustomHandle
          key={handle.id}
          curNode={node}
          handle={handle}
          type="source"
          position={Position.Right}
          isConnectable={props.isConnectable}
        />
      ))}

      {/* Loop Header */}
      <div className={styles.loopHeader}>
        <div className={styles.loopIcon}>
          <ReloadOutlined />
        </div>
        <div className={styles.loopTitle}>
          <div className={styles.loopName}>{data.name}</div>
          <div className={styles.loopType}>Loop Container</div>
        </div>
        <Button
          type="dashed"
          style={{ fontSize: 12 }}
          size="small"
          icon={<PlusOutlined />}
          onClick={(e) => {
            e.stopPropagation()
            handleAddNode()
          }}
        >
          Add Node
        </Button>
      </div>
    </div>
  )
}

export default memo(LoopNodeView)
