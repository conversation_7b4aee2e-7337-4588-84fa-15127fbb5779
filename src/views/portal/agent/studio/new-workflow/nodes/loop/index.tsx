import { Position } from '@xyflow/react'
import { merge } from 'lodash-es'
import { nanoid } from 'nanoid'
import type BaseNode from '../../lib/base-node'
import { NodeTypeEnum, type BaseNodeType } from '../../lib/base-node/type'
import WorkflowNode, {
  type WorkflowNodeDataType,
} from '../../lib/workflow-node'
import NodeDefaultNode from '../node-default'
import LoopStartNode from './components/loop-start'
import LoopNodeView from './node'

export type LoopNodeType<StructureType extends 'instance' | 'data'> =
  BaseNodeType<StructureType> & {
    loopChildren: StructureType extends 'instance'
      ? BaseNode[]
      : BaseNodeType<'data'>[]
  }

class LoopNode extends WorkflowNode<LoopNodeType<'instance'>> {
  readonly type = NodeTypeEnum.Workflow
  static NodeView = LoopNodeView
  static SidebarView = NodeDefaultNode.SidebarView

  static getDefaultNodeData = (
    id: string,
    cfg?: Partial<BaseNodeType<'data'>>
  ): WorkflowNodeDataType => {
    return merge(
      {
        id,
        type: NodeTypeEnum.Workflow,
        position: { x: 0, y: 0 },
        style: {
          width: 400,
          height: 300,
          backgroundColor: 'rgba(240, 240, 240, 0.4)',
          border: '1px solid #d9d9d9',
          borderRadius: '8px',
        },
        handles: [
          {
            id: `${id}_target_${nanoid(8)}`,
            type: 'target',
            position: Position.Left,
            nodeId: id,
            x: 0,
            y: 0,
            width: 0,
            height: 0,
          },
          {
            id: `${id}_source_${nanoid(8)}`,
            type: 'source',
            position: Position.Right,
            nodeId: id,
            x: 0,
            y: 0,
            width: 0,
            height: 0,
          },
        ],
        edges: [],
        data: {
          id,
          name: 'Loop',
          nodeType: 'loop',
          description: 'Loop node',
          nodeData: {
            type: 'internal',
            // propertyForms: [propertyForm],
          },
          parents: [],
          children: [],
        },
        loopChildren: [],
      },
      cfg
    )
  }

  getDefaultTree() {
    const loopStartId = `${this.node.data.id}_loop_start_${nanoid(4)}`

    const loopStartNodeData = LoopStartNode.getDefaultNodeData(loopStartId, {
      position: { x: 0, y: 100 },
    })

    const loopStart = new LoopStartNode(loopStartNodeData)

    loopStart.node.parentId = this.node.data.id
    loopStart.node.extent = 'parent'

    return [loopStart]
  }

  constructor(node: BaseNodeType<'data'>) {
    super(node, {
      Node: LoopNodeView,
      Sidebar: NodeDefaultNode.SidebarView,
    })

    this.node.loopChildren = this.getDefaultTree()
  }
}

export default LoopNode
