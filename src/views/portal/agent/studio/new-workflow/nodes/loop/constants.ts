import { PluginPluginOutputFormType } from 'api/data-contracts'
import { nanoid } from 'nanoid'

export enum LoopTypeEnum {
  count = 'count',
  condition = 'condition',
  array = 'array',
}

export const output = {
  type: PluginPluginOutputFormType.Fixed,
  structure: [
    {
      key: nanoid(),
      name: 'loopResult',
      type: 'Array<Object>',
      required: true,
      deep: 1,
    },
    {
      key: nanoid(),
      name: 'iterationCount',
      type: 'Integer',
      required: true,
      deep: 1,
    },
    {
      key: nanoid(),
      name: 'lastIterationResult',
      type: 'Object',
      required: false,
      deep: 1,
    },
  ],
}
