import { nanoid } from 'nanoid'
import BaseNode, { BaseNodeType, NodeTypeEnum } from '../../lib/base-node'

import RootNodeView from './node'
import RootSidebarView from './sidebar'
import { Position } from '@xyflow/react'
import { merge } from 'lodash-es'

class RootNode extends BaseNode<BaseNodeType<'instance'>> {
  readonly type = NodeTypeEnum.Root
  static NodeView = RootNodeView
  static SidebarView = RootSidebarView
  freeNodes: BaseNode[] = []

  static getDefaultNodeData = (
    id: string,
    cfg?: Partial<BaseNodeType<'data'>>
  ): BaseNodeType<'data'> => {
    return merge(
      {
        id,
        data: {
          id,
          name: 'Root',
          nodeType: 'root',
          description: 'Root Node',
          parents: [],
          children: [],
        },
        type: NodeTypeEnum.Workflow,
        handles: [],
        edges: [],
        position: { x: 0, y: 0 },
      },
      cfg
    )
  }

  constructor(node: BaseNodeType<'data'>) {
    super(node, { Node: RootNodeView, Sidebar: RootSidebarView })
  }
}

export default RootNode
