const propertyForms = [
  {
    name: 'Get Job',
    structure: {
      formItemId: 'f44f097b-4249-4bec-8aaa-6ff9ab209ba2',
      path: ['f44f097b-4249-4bec-8aaa-6ff9ab209ba2'],
      type: 'root',
      props: {
        formItemConfig: {
          name: 'root',
          required: true,
        },
      },
      extends: {
        name: 'Get Job',
      },
      children: [
        {
          formItemId: '178d1e4e-34dd-4041-8690-4b5cd869b44e',
          path: [
            'f44f097b-4249-4bec-8aaa-6ff9ab209ba2',
            '178d1e4e-34dd-4041-8690-4b5cd869b44e',
          ],
          type: 'section',
          props: {
            formItemConfig: {
              label: 'Section',
              name: 'section_4OKU',
              required: false,
            },
          },
          children: [
            {
              formItemId: '3c76f545-6c73-48f9-b639-294e181b1093',
              path: [
                'f44f097b-4249-4bec-8aaa-6ff9ab209ba2',
                '178d1e4e-34dd-4041-8690-4b5cd869b44e',
                '3c76f545-6c73-48f9-b639-294e181b1093',
              ],
              type: 'input',
              props: {
                formItemConfig: {
                  label: 'Query',
                  name: 'query_2OMI',
                  required: false,
                },
                generalConfig: {
                  condition: [
                    [
                      {
                        operator: '===',
                        targetKey: '',
                        targetVal: '',
                      },
                    ],
                  ],
                  dataType: 'String',
                },
              },
              children: [],
            },
            {
              formItemId: '43b2d462-127e-4552-9e52-f28bb9809cdd',
              path: [
                'f44f097b-4249-4bec-8aaa-6ff9ab209ba2',
                '178d1e4e-34dd-4041-8690-4b5cd869b44e',
                '43b2d462-127e-4552-9e52-f28bb9809cdd',
              ],
              type: 'input',
              props: {
                formItemConfig: {
                  label: 'Location',
                  name: 'location__3AB',
                  required: false,
                },
                generalConfig: {
                  dataType: 'String',
                },
              },
              children: [],
            },
            {
              formItemId: '289ac841-f5db-4130-82c5-f2da044b622b',
              path: [
                'f44f097b-4249-4bec-8aaa-6ff9ab209ba2',
                '178d1e4e-34dd-4041-8690-4b5cd869b44e',
                '289ac841-f5db-4130-82c5-f2da044b622b',
              ],
              type: 'input',
              props: {
                formItemConfig: {
                  label: 'Salary Minimum',
                  name: 'input_ebM-',
                  required: false,
                },
                generalConfig: {
                  dataType: 'String',
                },
              },
              children: [],
            },
          ],
        },
      ],
    },
    endpoint: {
      url: {
        method: 'GET',
        path: 'https://api.careerfuture.com/v1/jobs',
      },
      query: [
        {
          id: '5DzQO5n5mF2zv3_xwCAq_',
          key: 'query',
          type: 'reference',
          dataType: 'String',
          value: 'section_4OKU.query_2OMI',
          reference: 'query',
          description: '',
          selected: true,
        },
        {
          id: 'ru8gCtEK48iy4k9o6kBxi',
          key: 'location',
          type: 'reference',
          dataType: 'String',
          value: 'section_4OKU.location__3AB',
          reference: 'location',
          description: '',
          selected: true,
        },
        {
          id: 'ZW4Qdnokw_rqz6i835q_E',
          key: 'salary_min',
          type: 'reference',
          dataType: 'String',
          value: 'section_4OKU.input_ebM-',
          reference: 'section_4OKU.input_ebM-',
          description: '',
          selected: true,
        },
      ],
      header: [
        {
          id: 'iyKjIM2CwKOJUPPfCBk4_',
          key: 'Content-Type',
          type: 'input',
          dataType: 'String',
          value: 'application/json',
          reference: '',
          description: '',
          selected: true,
        },
      ],
      body: {
        type: 'form-data',
        payload: [],
      },
      output: {
        type: 'fixed',
        structure: [],
      },
    },
  },
  {
    name: 'Search Job',
    structure: {
      formItemId: '74b04291-c602-42aa-a669-77bb660325bb',
      path: ['74b04291-c602-42aa-a669-77bb660325bb'],
      type: 'root',
      props: {
        formItemConfig: {
          name: 'root',
          required: true,
        },
      },
      extends: {
        name: 'Search Job',
      },
      children: [
        {
          formItemId: 'e326e732-8471-4d2e-928b-36cb0972bb36',
          path: [
            '74b04291-c602-42aa-a669-77bb660325bb',
            'e326e732-8471-4d2e-928b-36cb0972bb36',
          ],
          type: 'section',
          props: {
            formItemConfig: {
              label: 'Section',
              name: 'section_hnsW',
              required: false,
            },
          },
          children: [
            {
              formItemId: '815e01f4-ac6d-479e-94d1-8b8268b88194',
              path: [
                '74b04291-c602-42aa-a669-77bb660325bb',
                'e326e732-8471-4d2e-928b-36cb0972bb36',
                '815e01f4-ac6d-479e-94d1-8b8268b88194',
              ],
              type: 'input',
              props: {
                formItemConfig: {
                  label: 'Query',
                  name: 'query_HZve',
                  required: false,
                },
                generalConfig: {
                  dataType: 'String',
                },
              },
              children: [],
            },
            {
              formItemId: 'c2e952b7-b40a-4484-8dc8-5f97eff31d98',
              path: [
                '74b04291-c602-42aa-a669-77bb660325bb',
                'e326e732-8471-4d2e-928b-36cb0972bb36',
                'c2e952b7-b40a-4484-8dc8-5f97eff31d98',
              ],
              type: 'input',
              props: {
                formItemConfig: {
                  label: 'Salary Range',
                  name: 'salary_range_qWu9',
                  required: false,
                },
                generalConfig: {
                  dataType: 'String',
                },
              },
              children: [],
            },
          ],
        },
      ],
    },
    endpoint: {
      url: {
        method: 'POST',
        path: 'https://api.careerfuture.com/v1/jobs',
      },
      query: [
        {
          id: 'oJrueycKUAdqNW5hPuCIr',
          key: 'query',
          type: 'reference',
          dataType: 'String',
          value: 'section_hnsW.query_HZve',
          reference: 'section_hnsW.query_HZve',
          description: '',
          selected: true,
        },
        {
          id: 'Z3ptLgDglbvslfUtaLAQ4',
          key: 'salary_range',
          type: 'reference',
          dataType: 'String',
          value: 'section_hnsW.salary_range_qWu9',
          reference: 'section_hnsW.salary_range_qWu9',
          description: '',
          selected: true,
        },
      ],
      header: [],
      body: {
        type: 'form-data',
        payload: [],
      },
      output: {
        type: 'fixed',
        structure: [],
      },
    },
  },
]

export const fakeData = {
  name: 'My Career Future',
  uuid: '20e94672-6ac1-430a-961c-7c7b01b8b196',
  description: 'To get list of jobs in Singapore',
  iconID: null,
  type: 'external',
  categoryID: 1,
  category: {
    id: 1,
    uuid: 'b52565e9-6e1e-4905-8bf7-d9985bea791e',
    name: 'Data',
    description: 'data',
    iconID: null,
    userID: 1,
    organizationID: 1,
  },
  endpoint: '',
  userID: 1,
  organizationID: 1,
  id: 93,
  pluginID: 55,
  version: 'a89643c5-bb93-4632-9c91-************',
  status: 'draft',
  createdAt: '2025-04-22T09:18:11.461Z',
  updatedAt: '2025-04-22T09:18:11.461Z',
  configurationForm: {
    formItemId: '2a7decde-7a1d-41e9-a16d-462931f6528f',
    path: ['2a7decde-7a1d-41e9-a16d-462931f6528f'],
    type: 'root',
    props: {
      formItemConfig: {
        name: 'root',
        required: true,
      },
    },
    children: [
      {
        formItemId: '0b9cd2ab-7278-40af-8ea5-13d9791c0ec4',
        path: [
          '2a7decde-7a1d-41e9-a16d-462931f6528f',
          '0b9cd2ab-7278-40af-8ea5-13d9791c0ec4',
        ],
        type: 'input',
        props: {
          formItemConfig: {
            label: 'Description',
            name: 'input_4K3t',
            required: false,
          },
          generalConfig: {
            dataType: 'String',
          },
        },
        children: [],
      },
      {
        formItemId: 'd87d85d4-3e9f-4b11-83a5-3d67156f54df',
        path: [
          '2a7decde-7a1d-41e9-a16d-462931f6528f',
          'd87d85d4-3e9f-4b11-83a5-3d67156f54df',
        ],
        type: 'input',
        props: {
          formItemConfig: {
            label: 'API Key',
            name: 'input_MaH_',
            required: false,
          },
          generalConfig: {
            dataType: 'String',
          },
        },
        children: [],
      },
    ],
  },
  propertyAndEndpointForms:
    propertyForms[Math.floor(Math.random() * 10) % 2].structure,
}
