import { PluginPluginOutputFormType } from 'api/data-contracts'
import { FormItemType } from 'components/new-dynamic-form/types'

export const structure: FormItemType = {
  path: ['root'],
  formItemId: 'root',
  type: 'root',
  children: [],
  props: {
    formItemConfig: {
      name: 'root',
      required: true,
    },
  },
}

export const output = {
  type: PluginPluginOutputFormType.Customized,
  structure: [],
}
