import { nanoid } from 'nanoid'
import { BaseNodeType, NodeTypeEnum } from '../../lib/base-node'
import WorkflowNode, { WorkflowNodeDataType } from '../../lib/workflow-node'
import NodeDefaultNode from '../node-default'

import EndSidebarView from './sidebar'
import { Position } from '@xyflow/react'
import { merge } from 'lodash-es'

class EndNode extends WorkflowNode {
  readonly type = NodeTypeEnum.Workflow
  static NodeView = NodeDefaultNode.NodeView
  static SidebarView = EndSidebarView
  static getDefaultNodeData = (
    id: string,
    cfg?: Partial<BaseNodeType<'data'>>
  ): WorkflowNodeDataType => {
    return merge(
      {
        type: NodeTypeEnum.Workflow,
        handles: [
          {
            id: `${id}target${nanoid()}`,
            type: 'target',
            position: Position.Left,
            nodeId: id,
            x: 0,
            y: 0,
            width: 0,
            height: 0,
          },
        ],
        data: {
          name: 'End',
          nodeType: 'end',
          description: 'End node',
          nodeData: {
            type: 'internal',
            // propertyForms: [propertyForm],
          },
          id,
          parents: [],
          children: [],
        },
        id,
        edges: [],
        position: { x: 0, y: 0 },
      },
      cfg
    )
  }

  constructor(node: BaseNodeType<'data'>) {
    super(node, { Node: NodeDefaultNode.NodeView, Sidebar: EndSidebarView })
  }
}

export default EndNode
