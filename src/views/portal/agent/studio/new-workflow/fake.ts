import { Position } from '@xyflow/react'
import { nanoid } from 'nanoid'
import { BaseNodeType, NodeTypeEnum } from './lib/base-node'
import { fakeData as PluginFakeData } from './nodes/node-default/fake'

export const generateMockData = (): BaseNodeType<'data'> => {
  const generateNode = (
    id: string,
    type: NodeTypeEnum,
    nodeType: string,
    x: number,
    y: number
  ): BaseNodeType<'data'> => ({
    id,
    type,
    position: { x, y },
    handles:
      nodeType === NodeTypeEnum.Root
        ? []
        : nodeType === 'start'
          ? [
              {
                id: `${id}_source_1`,
                type: 'source',
                position: Position.Right,
                nodeId: id,
                x: 0,
                y: 0,
                width: 0,
                height: 0,
              },
            ]
          : nodeType === 'end'
            ? [
                {
                  id: `${id}_target_1`,
                  type: 'target',
                  position: Position.Left,
                  nodeId: id,
                  x: 0,
                  y: 0,
                  width: 0,
                  height: 0,
                },
              ]
            : [
                {
                  id: `${id}_target_1`,
                  type: 'target',
                  position: Position.Left,
                  nodeId: id,
                  x: 0,
                  y: 0,
                  width: 0,
                  height: 0,
                },
                {
                  id: `${id}_source_1`,
                  type: 'source',
                  position: Position.Right,
                  nodeId: id,
                  x: 0,
                  y: 0,
                  width: 0,
                  height: 0,
                },
              ],
    data: {
      id,
      name:
        nodeType === 'start' ? 'Start' : nodeType === 'end' ? 'End' : nanoid(),
      nodeType,
      nodeData:
        nodeType === 'start'
          ? { type: 'internal' }
          : nodeType === 'end'
            ? { type: 'internal' }
            : PluginFakeData,
      description:
        nodeType === 'start'
          ? 'Start node'
          : nodeType === 'end'
            ? 'End node'
            : nanoid(),
      inputs: {
        configurationForm: {},
        propertyAndEndpoint: {},
      },
      extends: {},
      parents: [],
      children: [],
    },

    edges: [],
  })

  const [
    rootId,
    startId,
    plugin1Id,
    plugin2Id,
    plugin3Id,
    plugin4Id,
    plugin5Id,
  ] = [nanoid(), nanoid(), nanoid(), nanoid(), nanoid(), nanoid(), nanoid()]

  const rootNode = generateNode(rootId, NodeTypeEnum.Root, 'root', -100, -100)
  const startNode = generateNode(
    startId,
    NodeTypeEnum.Workflow,
    'start',
    -400,
    0
  )
  const plugin1 = generateNode(plugin1Id, NodeTypeEnum.Plugin, 'test', 0, 0)
  const plugin2 = generateNode(plugin2Id, NodeTypeEnum.Plugin, 'llm', 400, 100)
  const plugin3 = generateNode(plugin3Id, NodeTypeEnum.Plugin, 'xxx', 400, -100)
  const plugin4 = generateNode(plugin4Id, NodeTypeEnum.Plugin, 'a', 800, -100)
  const plugin5 = generateNode(plugin5Id, NodeTypeEnum.Plugin, 'b', 1200, -100)

  rootNode.data.children = [startNode]
  startNode.data.children = [plugin1]
  plugin1.data.children = [plugin2, plugin3]
  plugin3.data.children = [plugin4]
  plugin4.data.children = [plugin5]

  startNode.edges = [
    {
      id: `${startNode.id}_${plugin1.id}`,
      source: startNode.id,
      target: plugin1.id,
      sourceHandle: `${startNode.id}_source_1`,
      targetHandle: `${plugin1.id}_target_1`,
      type: 'custom-edge',
    },
  ]
  plugin1.edges = [
    {
      id: `${plugin1.id}_${plugin2.id}`,
      source: plugin1.id,
      target: plugin2.id,
      sourceHandle: `${plugin1.id}_source_1`,
      targetHandle: `${plugin2.id}_target_1`,
      type: 'custom-edge',
    },
    {
      id: `${plugin1.id}_${plugin3.id}`,
      source: plugin1.id,
      target: plugin3.id,
      sourceHandle: `${plugin1.id}_source_1`,
      targetHandle: `${plugin3.id}_target_1`,
      type: 'custom-edge',
    },
  ]
  plugin3.edges = [
    {
      id: `${plugin3.id}_${plugin4.id}`,
      source: plugin3.id,
      target: plugin4.id,
      sourceHandle: `${plugin3.id}_source_1`,
      targetHandle: `${plugin4.id}_target_1`,
      type: 'custom-edge',
    },
  ]
  plugin4.edges = [
    {
      id: `${plugin4.id}_${plugin5.id}`,
      source: plugin4.id,
      target: plugin5.id,
      sourceHandle: `${plugin4.id}_source_1`,
      targetHandle: `${plugin5.id}_target_1`,
      type: 'custom-edge',
    },
  ]

  return rootNode
}

export const FakeData = generateMockData()
