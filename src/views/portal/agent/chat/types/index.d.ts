import {
  GenieCoreAppHandlersChatResponsesChatConversationResponse,
  GenieCoreAppHandlersChatResponsesChatSessionResponse,
  GenieCoreAppHandlersAgentResponsesPublishChannelResponse,
} from 'api/data-contracts'

export type ChatSession = Omit<
  GenieCoreAppHandlersChatResponsesChatSessionResponse,
  'channel' | 'channelID'
> & {
  channel?: GenieCoreAppHandlersChatResponsesChatSessionResponse['channel']
}

export type ChatConversation = Omit<
  GenieCoreAppHandlersChatResponsesChatConversationResponse,
  'session'
> & {
  session?: GenieCoreAppHandlersChatResponsesChatSessionResponseChatSession
  type: 'file' | 'text'
  startTime: string
  endTime: string
  hasError: boolean
  isLoading: boolean
}

export type ChatMainChannel = Omit<
  GenieCoreAppHandlersAgentResponsesPublishChannelResponse,
  'extraData'
> & {
  extraData: {
    openingQuestions?: string[]
    feedbackOperationLikeOptions?: string
    feedbackOperationUnlikeOptions?: string
  }
}

export type SendResponse = Pick<
  GenieCoreAppHandlersChatResponsesChatConversationResponse,
  'nodeInfos',
  'completionTokens'
> & {
  uuid: string
  title: string
  answer: string
  requestMessageID: string
  responseMessageID: string
  completionTokens: number
  nodeID: string
  nodeLabel: string
  nodeType: string
  startTime: string
  endTime: string
  status: string
  errorMessage: string
}
