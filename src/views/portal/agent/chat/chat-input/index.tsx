import {
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  PlusCircleOutlined,
  SendOutlined,
} from '@ant-design/icons'
import {
  App,
  Button,
  Flex,
  Input,
  Progress,
  Tooltip,
  Upload,
  UploadProps,
} from 'antd'
import { UploadRef } from 'antd/es/upload/Upload'
import cls from 'classnames'
import {
  forwardRef,
  ForwardRefRenderFunction,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react'
import { shortFileName } from 'utils/common'
import styles from './index.scss'

const { Dragger } = Upload
const { TextArea } = Input

export interface ChatInputProps {
  content?: string
  placeholder?: string
  canUpload?: boolean
  maxFilesCount?: number
  shouldFileWithMessage?: boolean
  supportFileTypes?: string[]
  fileSizeLimit?: number
  onAttachFile?: UploadProps['customRequest']
  onSendMessage?: (
    content: string,
    fileList: UploadProps['fileList']
  ) => Promise<void>
}

export interface ChatInputRef {
  clearFiles: () => void
}

const ChatInput: ForwardRefRenderFunction<ChatInputRef, ChatInputProps> = (
  props,
  ref
) => {
  const { message } = App.useApp()
  const [content, setContent] = useState(props.content ?? '')
  const [sendMessageLoading, setSendMessageLoading] = useState(false)
  const uploadRef = useRef<UploadRef>(null)
  const [fileList, setFileList] = useState<UploadProps['fileList']>([])
  const [isUploading, setIsUploading] = useState(false)
  const disabledSendButton = useMemo(
    () =>
      isUploading ||
      (props.shouldFileWithMessage &&
        fileList &&
        fileList.length > 0 &&
        content.length === 0),
    [isUploading, props.shouldFileWithMessage, fileList, content]
  )

  useEffect(() => {
    setContent(props.content ?? '')
  }, [props.content])

  useImperativeHandle(
    ref,
    () => ({
      clearFiles: () => {
        fileList?.forEach((file) => (file as any).action?.remove())
      },
    }),
    [fileList]
  )

  const handleSendMessage = async (
    content: string,
    fileList: UploadProps['fileList']
  ) => {
    if (content.trim() === '' || sendMessageLoading) {
      return
    }

    try {
      setSendMessageLoading(true)
      setContent('')
      await props.onSendMessage?.(content, fileList)
    } catch (error) {
      message.error(
        'Oops! There was a problem delivering your message. Please try sending it again.'
      )
    } finally {
      setSendMessageLoading(false)
    }
  }

  const handleAttachFiles = (e: React.MouseEvent<HTMLElement>) => {
    const upload: any = uploadRef.current?.upload
    upload?.uploader?.onClick(e)
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage(content, fileList)
    }
  }

  const renderFileItem: UploadProps['itemRender'] = (
    originNode,
    file,
    fileList,
    action
  ) => {
    const isUploading = file.status === 'uploading'
    const isErrored = file.status === 'error'
    const isDone = file.status === 'done'
    // To remove file from fileList when click the clear files
    ;(file as any).action = action
    setFileList(fileList)
    return (
      <Flex
        className={cls({
          [styles.uploadItemWrapper]: true,
          'upload-item-uploading': isUploading,
          'upload-item-error': isErrored,
          'upload-item-done': isDone,
        })}
        key={file.uid}
        align="center"
        justify="space-between"
        gap={4}
      >
        {isUploading && (
          <Progress
            type="circle"
            percent={file.percent}
            strokeColor={{ '0%': '#e9eef4', '100%': '#1a79ff' }}
            trailColor="transparent"
            size={20}
            status="active"
          />
        )}
        {isErrored && (
          <ExclamationCircleOutlined style={{ color: '#d94032' }} size={20} />
        )}
        <Tooltip title={file.name} placement="top">
          {shortFileName(file.name)}
        </Tooltip>
        <Tooltip title="Remove file" placement="top">
          <Button
            className="close-btn"
            type="link"
            icon={
              <CloseCircleOutlined style={{ backgroundColor: 'transparent' }} />
            }
            onClick={() => action.remove()}
          />
        </Tooltip>
      </Flex>
    )
  }

  const uploadProps: UploadProps = {
    accept: props.supportFileTypes?.join(','),
    multiple: props.maxFilesCount != 1,
    openFileDialogOnClick: false,
    itemRender: renderFileItem,
    customRequest: props.onAttachFile,
    beforeUpload: (file) => {
      const sizeLimit =
        props.fileSizeLimit === undefined
          ? 10 * 1024 * 1024
          : props.fileSizeLimit
      const isLtLimit = file.size < sizeLimit
      if (!isLtLimit) {
        message.error(
          `File must smaller than ${(sizeLimit / 1024 / 1024).toFixed(1)}MB!`
        )
      }
      return isLtLimit ? true : Upload.LIST_IGNORE
    },
    onChange: (info) => {
      switch (info.file.status) {
        case 'uploading':
          setIsUploading(true)
          break
        case 'done':
          setIsUploading(false)
          break
        case 'error':
          setIsUploading(false)
          message.error(`${info.file.name} file upload failed.`)
          break
        case 'removed':
          setIsUploading(false)
          break
      }
    },
    onRemove: (file) => {
      if (file.status === 'uploading') {
        setIsUploading(false)
      }
    },
  }

  return (
    <Dragger
      ref={uploadRef}
      className={styles.chatInput}
      disabled={!props.canUpload}
      {...uploadProps}
    >
      <TextArea
        value={content}
        onChange={(e) => setContent(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={props.placeholder}
        autoSize={{ minRows: 4, maxRows: 4 }}
        style={{ border: 'none', wordBreak: 'break-all' }}
      />
      <Flex className={styles.chatInputInnerFunctions} gap={4}>
        {props.canUpload && (
          <Button
            icon={<PlusCircleOutlined />}
            type="text"
            onClick={(e) => handleAttachFiles(e)}
            disabled={
              isUploading ||
              sendMessageLoading ||
              (fileList?.length ?? 0) >= (props.maxFilesCount ?? 99)
            }
          />
        )}
        <Button
          icon={<SendOutlined />}
          type="text"
          onClick={() => {
            handleSendMessage(content, fileList)
          }}
          loading={sendMessageLoading}
          style={
            disabledSendButton
              ? { backgroundColor: '#ccc', color: '#fff' }
              : { backgroundColor: '#3278d9', color: '#fff' }
          }
          disabled={disabledSendButton}
        />
      </Flex>
    </Dragger>
  )
}

export default forwardRef(ChatInput)
