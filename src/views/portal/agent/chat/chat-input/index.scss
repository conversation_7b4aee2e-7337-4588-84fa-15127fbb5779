.chat-input {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100%;
  background-color: var(--ant-color-bg-elevated);
  border: 1px solid var(--ant-color-border);
  border-radius: 8px;
  padding: 8px;

  :global {
    .ant-upload:where(.ant-upload-drag, .ant-upload-btn) {
      padding: 0 !important;
      border: none;
      height: unset;
    }

    .ant-upload-drag-container {
      display: flex !important;
      flex-direction: column;
      padding: 0;
    }

    .ant-upload-list {
      &:before,
      &:after {
        display: none;
      }

      display: flex;
      gap: 12px;
      order: -1;
      overflow-x: auto;

      .ant-upload-list-item-container {
        display: flex;
      }
    }

    .ant-input-outlined:focus {
      box-shadow: none;
    }
  }

  &-inner-functions {
    background: var(--ant-color-bg-elevated);
    justify-content: flex-end;
  }
}

.upload-item-wrapper {
  position: relative;
  margin: 8px 0;
  padding: 8px;
  border-radius: 4px;
  text-wrap: nowrap;

  &:global:where(.upload-item-uploading) {
    border: 1px solid #dedede2e;
    background-color: #dedede2e;
  }

  &:global:where(.upload-item-error) {
    border: 1px solid #ffc3b5;
    background-color: #ff6c362e;

    button.close-btn,
    button.close-btn:hover {
      color: #d94032 !important;
    }
  }

  &:global:where(.upload-item-done) {
    border: 1px solid #dedede2e;
    background-color: #dedede2e;
  }

  :global {
    button.close-btn,
    button.close-btn:hover {
      color: #2a2c2f;
      position: absolute;
      top: -16px;
      right: -16px;
      background: radial-gradient(
        ellipse at center,
        #fff 0%,
        #fff 35%,
        #ffffff00 35%
      ) !important;
    }

    button.ant-btn-variant-link.close-btn:hover {
      color: #7a7c7f;
    }
  }
}
