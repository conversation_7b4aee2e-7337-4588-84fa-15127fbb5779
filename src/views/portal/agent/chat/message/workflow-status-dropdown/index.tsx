import {
  CheckCircleOutlined,
  Clock<PERSON>ircleOutlined,
  DoubleRightOutlined,
  Exclamation<PERSON>ircleOutlined,
  LoadingOutlined,
} from '@ant-design/icons'
import { Button, Dropdown, MenuProps } from 'antd'
import { NodeStatusNodeStatus } from 'api/data-contracts'
import cls from 'classnames'
import { useMemo, useState } from 'react'
import {
  WorkflowExecutionNode,
  WorkflowNodeInfo,
} from 'views/components/workflow-exeuction-node'
import { useChatNodeInfos } from 'views/hooks/use-chat-node-infos'
import NodeDetailsModal from '../../../../../components/workflow-exeuction-node/components/node-details-modal/node-details-modal'
import styles from './index.scss'

const getNodeStatusIcon = (status: NodeStatusNodeStatus) => {
  let icon = <></>
  switch (status) {
    case NodeStatusNodeStatus.Success:
      icon = <CheckCircleOutlined style={{ color: '#199459' }} />
      break
    case NodeStatusNodeStatus.Failed:
      icon = <ExclamationCircleOutlined style={{ color: '#D94032' }} />
      break
    case NodeStatusNodeStatus.Processing:
      icon = <LoadingOutlined style={{ color: '#3278D9' }} />
      break
    case NodeStatusNodeStatus.Initiating:
      icon = <ClockCircleOutlined />
      break
  }
  return icon
}

const renderCurrentStatus = (status: NodeStatusNodeStatus) => {
  const icon = getNodeStatusIcon(status)
  switch (status) {
    case NodeStatusNodeStatus.Success:
      return <>{icon} Workflow Execution Successes</>
    case NodeStatusNodeStatus.Failed:
      return <>{icon} Workflow Execution Failed</>
    case NodeStatusNodeStatus.Processing:
      return <>{icon} Workflow Execution Processing</>
    case NodeStatusNodeStatus.Initiating:
      return <>{icon} Workflow Execution Initiating</>
  }
}

export interface WorkflowStatusDropdownProps {
  isLoading: boolean
  msgId: string
  data: WorkflowNodeInfo[]
}

const WorkflowStatusDropdown: React.FC<WorkflowStatusDropdownProps> = (
  props
) => {
  const [initialized, setInitialized] = useState(false)
  const [logsModalVisible, setLogsModalVisible] = useState(false)
  const [detailsNodeIndex, setDetailsNodeIndex] = useState(1)
  const [dropdownOpen, setDropdownOpen] = useState(false)
  const { loading, nodeInfos } = useChatNodeInfos(props.msgId)

  const items: MenuProps['items'] = useMemo(
    () =>
      props.data.map((data, index) => ({
        key: `${index}_${data.nodeId}`,
        label: (
          <WorkflowExecutionNode
            data={data}
            index={index}
            onLogsClick={() => {
              setDetailsNodeIndex(index)
              setLogsModalVisible(true)
              setDropdownOpen(false)
            }}
          />
        ),
      })),
    [props.data]
  )

  let lastNode: WorkflowNodeInfo = props.data[props.data.length - 1]

  if (!lastNode) {
    if (!props.isLoading) {
      return <></>
    } else {
      lastNode = {
        nodeId: 'start',
        nodeName: 'Start',
        nodeType: 'start',
        startTime: '',
        endTime: '',
        status: NodeStatusNodeStatus.Processing,
        logs: '',
        details: '[]',
        durationInMilliseconds: 0,
      }
    }
  }

  return (
    <>
      <Dropdown
        menu={{ items }}
        placement="bottomLeft"
        open={dropdownOpen}
        className={cls({
          [styles.workflowStatusWrapper]: true,
          [styles.workflowStatusWrapperSuccess]: lastNode.status === 'success',
          [styles.workflowStatusWrapperFailed]: lastNode.status === 'failed',
          [styles.workflowStatusWrapperInitiating]:
            lastNode.status === 'initiating',
          [styles.workflowStatusWrapperProcessing]:
            lastNode.status === 'processing',
        })}
        overlayClassName={styles.workflowStatusOverlay}
        onOpenChange={(open) => {
          setDropdownOpen(open)
          if (open) {
            setInitialized(true)
          }
        }}
      >
        <Button type="text">
          {renderCurrentStatus(lastNode.status)}
          <DoubleRightOutlined />
        </Button>
      </Dropdown>
      {initialized && (
        <NodeDetailsModal
          open={logsModalVisible}
          msgId={props.msgId}
          nodeIndex={detailsNodeIndex}
          onCancel={() => setLogsModalVisible(false)}
          loading={loading}
          nodeInfoDetails={nodeInfos ?? []}
        />
      )}
    </>
  )
}

export default WorkflowStatusDropdown
