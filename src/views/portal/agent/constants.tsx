import { ApiOutlined } from '@ant-design/icons'
import Icon from '@ant-design/icons/lib/components/Icon'
import { AgentTypeAgentType } from 'api/data-contracts'
import Robot from 'assets/images/robot.svg'

// Agent type display names
export const AgentTypes = {
  [AgentTypeAgentType.Chatbot]: 'Chatbot',
  [AgentTypeAgentType.SmartAPI]: 'Smart API',
} as const

export const AgentCardNavLinkUrl = {
  [AgentTypeAgentType.Chatbot]: '/portal/agent/chat',
  [AgentTypeAgentType.SmartAPI]: '/portal/agent/smart-api',
} as const

// Helper function to convert string agentType to AgentTypeAgentType enum
export const getAgentType = (type?: string): AgentTypeAgentType => {
  if (type === 'chatbot') return AgentTypeAgentType.Chatbot
  if (type === 'smart_api') return AgentTypeAgentType.SmartAPI
  return AgentTypeAgentType.Chatbot // Default fallback
}

// Agent type icons
export const AgentTypeIcons = {
  [AgentTypeAgentType.Chatbot]: <Icon component={Robot} />,
  [AgentTypeAgentType.SmartAPI]: <ApiOutlined />,
} as const
