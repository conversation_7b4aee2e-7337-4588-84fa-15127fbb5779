import { Skeleton } from 'antd'
import classNames from 'classnames'
import { useState } from 'react'
import {
  WorkflowExecutionNode,
  type WorkflowNodeInfo,
} from 'views/components/workflow-exeuction-node'
import NodeDetailsModal from 'views/components/workflow-exeuction-node/components/node-details-modal/node-details-modal'
import { useSmartApiNodeInfos } from 'views/hooks/use-smart-api-node-infos'
import EmptySnippets from '../empty-snippets'
import styles from './index.module.scss'

interface WorkflowNodesProps {
  data: WorkflowNodeInfo[]
  requestMessageId: string
  isLoading: boolean
  className?: string
}

export function WorkflowNodes({
  data,
  requestMessageId,
  className,
  isLoading,
}: WorkflowNodesProps) {
  const { loading: isLoadingNodeInfos, nodeInfos } =
    useSmartApiNodeInfos(requestMessageId)
  const [logsModalVisible, setLogsModalVisible] = useState(false)
  const [detailsNodeIndex, setDetailsNodeIndex] = useState(0)

  if (isLoadingNodeInfos || isLoading) {
    return <Skeleton active />
  }

  if (!data?.length) {
    return <EmptySnippets />
  }

  return (
    <>
      <div className={classNames(styles.container, className)}>
        {data.map((nodeInfo, index) => (
          <div key={nodeInfo.nodeId} className={styles.nodeWrapper}>
            <WorkflowExecutionNode
              data={nodeInfo}
              index={index}
              onLogsClick={() => {
                setDetailsNodeIndex(index)
                setLogsModalVisible(true)
              }}
            />
          </div>
        ))}
      </div>
      <NodeDetailsModal
        open={logsModalVisible}
        msgId={requestMessageId}
        nodeIndex={detailsNodeIndex}
        onCancel={() => setLogsModalVisible(false)}
        loading={isLoadingNodeInfos || isLoading}
        nodeInfoDetails={nodeInfos ?? []}
      />
    </>
  )
}
