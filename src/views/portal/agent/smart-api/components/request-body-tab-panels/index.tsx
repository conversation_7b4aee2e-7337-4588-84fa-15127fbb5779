import type { CollapseProps } from 'antd'
import { Collapse, Form, Table } from 'antd'
import { BodyPayloadTypeBodyPayloadType } from 'api/data-contracts'
import classNames from 'classnames'
import type {
  FormValueEntry,
  FormValues,
  RequestBodyType,
  TableType,
} from '../../types'
import styles from './index.module.scss'
import { tableColumns } from './table-columns'

interface RequestBodyTabPanelsProps {
  tableType: TableType
  selectedTableRowKeys: Record<string, React.Key[]>
  onTableRowSelectionChanged: (
    sectionKeys: SectionKeys,
    selectedRowKeys: React.Key[],
    selectedRows: FormValueEntry[]
  ) => void
}

export const SECTION_KEYS = {
  QUERY_PARAMS: 'queryParams',
  BODY_PAYLOAD: 'bodyPayload',
} as const

export type SectionKeys = (typeof SECTION_KEYS)[keyof typeof SECTION_KEYS]

function RequestBodyTabPanels({
  onTableRowSelectionChanged,
  selectedTableRowKeys,
}: Readonly<RequestBodyTabPanelsProps>) {
  const form = Form.useFormInstance<FormValues>()

  const handleRowSelectionChange = (
    sectionKey: SectionKeys,
    selectedRowKeys: React.Key[],
    selectedRows: FormValueEntry[]
  ) => {
    // Find unselected rows by comparing with previous selection
    const previousSelectedKeys = selectedTableRowKeys[sectionKey] || []
    const unselectedKeys = previousSelectedKeys.filter(
      (key) => !selectedRowKeys.includes(key)
    )

    // Clear validation errors for unselected rows
    const values = form.getFieldValue(sectionKey) || []
    const fieldsToReset = values
      .map((value: FormValueEntry, index: number) => ({
        value,
        index,
      }))
      .filter((item: { value: FormValueEntry; index: number }) =>
        unselectedKeys.includes(item.value.key)
      )
      .map((item: { value: FormValueEntry; index: number }) => ({
        name: [sectionKey, item.index, 'value'],
        errors: [],
      }))

    if (fieldsToReset.length > 0) {
      form.setFields(fieldsToReset)
    }

    // Call the original handler
    onTableRowSelectionChanged(sectionKey, selectedRowKeys, selectedRows)
  }

  const getPanelContent = (sectionKey: SectionKeys) => {
    const values = form.getFieldValue(sectionKey) || []

    if (values.length === 0) return null

    return (
      <Form.List name={sectionKey}>
        {() => {
          return (
            <Table<FormValueEntry>
              rowSelection={{
                selectedRowKeys: selectedTableRowKeys[sectionKey] || [],
                onChange: (selectedRowKeys, selectedRows) =>
                  handleRowSelectionChange(
                    sectionKey,
                    selectedRowKeys,
                    selectedRows
                  ),
                getCheckboxProps: (record) => ({
                  disabled: record.required,
                }),
              }}
              columns={tableColumns(
                sectionKey,
                selectedTableRowKeys[sectionKey] || []
              )}
              dataSource={values}
              pagination={false}
              size="small"
            />
          )
        }}
      </Form.List>
    )
  }

  const requestBodyType: RequestBodyType = form.getFieldValue('requestBodyType')
  const bodyPayload = form.getFieldValue(SECTION_KEYS.BODY_PAYLOAD)

  const items: CollapseProps['items'] = [
    {
      key: SECTION_KEYS.QUERY_PARAMS,
      label: 'Query Params',
      children: getPanelContent(SECTION_KEYS.QUERY_PARAMS),
    },
    ...(requestBodyType === BodyPayloadTypeBodyPayloadType.JSON &&
    bodyPayload.length > 0
      ? [
          {
            key: SECTION_KEYS.BODY_PAYLOAD,
            label: 'JSON Request Body',
            children: getPanelContent(SECTION_KEYS.BODY_PAYLOAD),
          },
        ]
      : []),
    ...(requestBodyType === BodyPayloadTypeBodyPayloadType.FormData &&
    bodyPayload.length > 0
      ? [
          {
            key: SECTION_KEYS.BODY_PAYLOAD,
            label: 'Form Data',
            children: getPanelContent(SECTION_KEYS.BODY_PAYLOAD),
          },
        ]
      : []),
  ].filter((item) => item.children !== null)

  return (
    <Collapse
      ghost
      className={classNames(styles.collapsePanel, styles.pL0)}
      items={items}
      defaultActiveKey={items
        .map((item) => item.key)
        .filter(Boolean)
        .map(String)}
    />
  )
}

export default RequestBodyTabPanels
