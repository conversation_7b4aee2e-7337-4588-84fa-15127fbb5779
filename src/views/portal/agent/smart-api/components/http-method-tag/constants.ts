import { HttpMethodSmartAPIURLMethod } from 'api/data-contracts'

// map of HTTP method names to their display tag names
export const HTTP_METHOD_TAG_NAMES: Record<
  HttpMethodSmartAPIURLMethod,
  string
> = {
  [HttpMethodSmartAPIURLMethod.SmartAPIURLMethodGET]: 'GET',
  [HttpMethodSmartAPIURLMethod.SmartAPIURLMethodPOST]: 'POST',
  [HttpMethodSmartAPIURLMethod.SmartAPIURLMethodPUT]: 'PUT',
  [HttpMethodSmartAPIURLMethod.SmartAPIURLMethodDELETE]: 'DEL',
  [HttpMethodSmartAPIURLMethod.SmartAPIURLMethodPATCH]: 'PATCH',
} as const
export type HttpMethodTagNames =
  (typeof HTTP_METHOD_TAG_NAMES)[keyof typeof HTTP_METHOD_TAG_NAMES]

export const HTTP_METHOD_TAG_COLORS = {
  [HTTP_METHOD_TAG_NAMES.GET]: '#2ABB71',
  [HTTP_METHOD_TAG_NAMES.POST]: '#E3B759',
  [HTTP_METHOD_TAG_NAMES.PUT]: '#3278D9',
  [HTTP_METHOD_TAG_NAMES.DELETE]: '#D94032',
} as const
export type HttpMethodTagColors =
  (typeof HTTP_METHOD_TAG_COLORS)[keyof typeof HTTP_METHOD_TAG_COLORS]
