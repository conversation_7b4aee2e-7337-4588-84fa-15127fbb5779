import { A<PERSON>, <PERSON><PERSON>, <PERSON>, Splitter, Tabs, Typography } from 'antd'
import classNames from 'classnames'
import PageMainLayout from 'layouts/portal/page-main-layout'
import { useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import ResponseBody from '../../../components/response-body'
import AgentAvatar from '../components/agent-avatar'
import { getAgentType } from '../constants'
import HttpMethodTag from './components/http-method-tag'
import RequestBodyTabPanels, {
  type SectionKeys,
} from './components/request-body-tab-panels'
import styles from './index.module.scss'

import { type ModelsSmartAPIResponse } from 'api/data-contracts'
import { getMessageFromError } from 'utils/common'
import { UrlBar } from './components/url-bar'
import { WorkflowNodes } from './components/workflow-nodes'
import { useRedirectIfError } from './hooks/useRedirectIfError'
import type { FormValues, SmartApiRouteState } from './types'
import {
  buildQueryParams,
  buildRequestBodyPayload,
  getAllRowKeys,
  getInitialFormValues,
  handleStreamingResponse,
  mapNodeInfos,
} from './utils'

function SmartApi() {
  const navigate = useNavigate()
  const [form] = Form.useForm<FormValues>()
  const { state: routeState }: { state: SmartApiRouteState } = useLocation()
  const { agent } = routeState.data
  const [response, setResponse] = useState<ModelsSmartAPIResponse | null>(null)
  const [isSendingRequest, setIsSendingRequest] = useState(false)
  const { message } = App.useApp()

  const requiredRowKeys = getAllRowKeys(agent.smartAPI.smartAPIDefinition)

  const [selectedTableRowKeys, setSelectedTableRowKeys] =
    useState<Record<SectionKeys, string[]>>(requiredRowKeys)

  /** Prevent runtime error on initial render if smart API definition is not available. */
  useRedirectIfError(routeState)
  if (!agent?.smartAPI?.smartAPIDefinition) return null

  const handleSubmit = async (values: FormValues) => {
    setResponse(null)
    setIsSendingRequest(true)

    try {
      const url = `${values.url}${buildQueryParams(values.queryParams, selectedTableRowKeys['queryParams'])}`
      const { data, headers } = buildRequestBodyPayload(
        values.method,
        values.requestBodyType,
        selectedTableRowKeys['bodyPayload'],
        values.bodyPayload
      )

      // Check if data is empty based on its type
      if (data instanceof FormData) {
        if (data.entries().next().done) {
          message.error('No form data selected')
          return
        }
      } else if (Object.keys(data || {}).length === 0) {
        message.error('No request body data selected')
        return
      }

      await handleStreamingResponse(
        url,
        values.method,
        data,
        setResponse,
        (error) => {
          setResponse(error)
          message.error(getMessageFromError(error))
        },
        headers
      )
    } catch (error) {
      // runtime error, if this happens it means something is wrong with frontend code
      console.error(error)
      message.error('Unexpected error')
    } finally {
      setIsSendingRequest(false)
    }
  }

  const handleReload = async () => {
    const values = form.getFieldsValue()
    await handleSubmit(values)
  }

  return (
    <PageMainLayout
      onClickBack={() => {
        navigate('/portal/agent')
      }}
      title={routeState?.title ?? '-'}
      icon={
        <AgentAvatar
          agentType={getAgentType(routeState?.data?.agent.agentType)}
          iconUuid={routeState?.data?.agent.agentIconUUID}
          name={routeState?.data?.agent.agentName}
        />
      }
    >
      <Form
        className={styles.smartApiContainer}
        layout="vertical"
        form={form}
        initialValues={getInitialFormValues(agent)}
        onFinish={handleSubmit}
        disabled={isSendingRequest}
      >
        <Splitter layout="horizontal">
          {/* Left Panel */}
          <Splitter.Panel className={styles.wrapper}>
            <header className={styles.sectionHeader}>
              <div className={styles.urlContainer}>
                <Form.Item name="method" noStyle>
                  <HttpMethodTag />
                </Form.Item>
                <Form.Item name="url" noStyle>
                  <UrlBar />
                </Form.Item>
                <Form.Item hidden name="requestBodyType" noStyle />
                <Form.Item hidden name="isStreamingEnabled" noStyle />
              </div>
              <Button
                type="primary"
                htmlType="submit"
                loading={isSendingRequest}
              >
                Send
              </Button>
            </header>
            <Splitter layout="vertical">
              {/* top left panel */}
              <Splitter.Panel>
                <Tabs
                  className={styles.tabs}
                  defaultActiveKey="params"
                  items={[
                    {
                      key: 'params',
                      label: 'Params',
                      children: (
                        <RequestBodyTabPanels
                          tableType="params"
                          selectedTableRowKeys={selectedTableRowKeys}
                          onTableRowSelectionChanged={(
                            sectionKey,
                            selectedRowKeys
                          ) => {
                            setSelectedTableRowKeys((prev) => ({
                              ...prev,
                              [sectionKey]: [
                                ...selectedRowKeys,
                                ...requiredRowKeys[sectionKey],
                              ],
                            }))
                          }}
                        />
                      ),
                    },
                    // { // header tab is not required, remove it for now
                    //   key: 'headers',
                    //   label: 'Headers',
                    //   children: (
                    //     <RequestBodyTabPanels tableType="headers" />
                    //   ),
                    // },
                  ]}
                />
              </Splitter.Panel>

              {/* bottom left panel */}
              <Splitter.Panel
                className={classNames(styles.fullHeight, styles.overflowHidden)}
              >
                <Typography.Title
                  level={3}
                  className={classNames(styles.sectionTitle, styles.gutterYSm)}
                >
                  Response Body
                </Typography.Title>
                <ResponseBody
                  response={response?.output}
                  onReload={handleReload}
                  wrapperClassName={styles.responseBodyWrapper}
                  syntaxHighlighterProps={{
                    wrapLines: true,
                    wrapLongLines: true,
                  }}
                />
              </Splitter.Panel>
            </Splitter>
          </Splitter.Panel>

          {/* Right Panel */}
          <Splitter.Panel
            defaultSize={350}
            className={classNames(styles.wrapper, styles.panelBorder)}
          >
            <Typography.Title
              level={2}
              className={classNames(
                styles.sectionTitle,
                styles.upperCase,
                styles.pbSm
              )}
            >
              Workflow Logs
            </Typography.Title>
            <WorkflowNodes
              data={mapNodeInfos(response?.nodeInfos ?? [])}
              requestMessageId={response?.requestMessageID ?? ''}
              isLoading={isSendingRequest}
            />
          </Splitter.Panel>
        </Splitter>
      </Form>
    </PageMainLayout>
  )
}

export default SmartApi
