import type {
  HttpMethodSmartAPIURLMethod,
  ResponsesAgentResponse,
} from 'api/data-contracts'
import { BodyPayloadTypeBodyPayloadType } from 'api/data-contracts'
import { ALL_DATA_TYPE } from 'constants/common'

export type TableType = 'params' | 'header'

export type SectionKeys = 'queryParams' | 'bodyPayload'

export interface SmartApiRouteState {
  data: {
    agent: ResponsesAgentResponse
  }
  title: string
}

/**
 * All possible value types from the Smart API
 */
export const SMART_API_VALUE_TYPE = [
  'String',
  'Number',
  'Integer',
  'Boolean',
  'Date',
  'Datetime',
  'Document',
  'Image',
  'Object',
  'Array<String>',
  'Array<Number>',
  'Array<Integer>',
  'Array<Date>',
  'Array<Datetime>',
  'Array<Document>',
  'Array<Image>',
  'Array<Boolean>',
  'Array<Object>',
] as const

export type SmartApiValueType = (typeof ALL_DATA_TYPE)[number]

/**
 * Smart API request body type
 */
export type RequestBodyType = `${BodyPayloadTypeBodyPayloadType}`

/**
 * Smart API debug page request form values
 */
export interface FormValues {
  method: `${HttpMethodSmartAPIURLMethod}`
  isStreamingEnabled: boolean
  url: string
  requestBodyType: RequestBodyType
  bodyPayload?: FormValueEntry[]
  queryParams?: FormValueEntry[]
}

/**
 * Smart API debug page request form entry format
 */
export interface FormValueEntry {
  key: string
  value: any
  description: string
  required: boolean
  type: (typeof ALL_DATA_TYPE)[number]
}
