import type {
  HttpMethodSmartAPIURLMethod,
  ModelsLightNodeInfo,
  ModelsSmartAPIDefinition,
  ResponsesAgentResponse,
} from 'api/data-contracts'
import { WorkflowRunningStatusWorkflowRunningStatus } from 'api/data-contracts'
import { ContentType } from 'api/http-client'
import { ISO_8601_DATE_FORMAT } from 'constants/common'
import { createStreamingApiRequest } from 'services/request'
import { baseRequest } from 'utils/axios'
import { getMessageFromError } from 'utils/common'
import { dayjsUTC } from 'utils/filter'
import type { WorkflowNodeInfo } from 'views/components/workflow-exeuction-node'
import { getInitialValueForType } from '../../../components/value-input'
import { mockInitialValues } from './mock'
import type {
  FormValueEntry,
  FormValues,
  RequestBodyType,
  SmartApiValueType,
} from './types'

const URL_PREFIX = '/api/v1/internal-smart-api'

/**
 * Get initial form values
 * @param useMock - use mock data, useful for debugging table row selection
 */
export function getInitialFormValues(
  agent: ResponsesAgentResponse,
  useMock: boolean = false
): FormValues {
  if (useMock) {
    return mockInitialValues
  }

  if (!agent?.smartAPI?.smartAPIDefinition) {
    console.error('No smartAPI definition found for agent', agent)
    return {
      method: 'GET',
      url: '',
      requestBodyType: 'json',
      bodyPayload: [],
      queryParams: [],
      isStreamingEnabled: false,
    }
  }

  return {
    method: agent.smartAPI.smartAPIDefinition.method,
    url: `${URL_PREFIX}/${agent.smartAPI.url}`,
    requestBodyType: agent.smartAPI.smartAPIDefinition.bodyPayloadType,
    isStreamingEnabled: agent.smartAPI.smartAPIDefinition.isStreamingEnabled,
    bodyPayload: agent.smartAPI.smartAPIDefinition.bodyPayload.map((item) => ({
      key: item.name,
      value: getInitialValueForType(item.type as SmartApiValueType),
      description: item.description,
      required: item.required,
      type: item.type as SmartApiValueType,
    })),
    queryParams: agent.smartAPI.smartAPIDefinition.queryParams.map((item) => ({
      key: item.name,
      value: getInitialValueForType(item.type as SmartApiValueType),
      description: item.description,
      required: item.required,
      type: item.type as SmartApiValueType,
      isStreamingEnabled: agent.smartAPI.smartAPIDefinition.isStreamingEnabled,
    })),
  }
}

const toName = ({ name }: { name: string }): string => name

const byRequired = (item: { required: boolean }): boolean => item.required

/**
 * Get all row keys for the smart API definition.
 * The row keys are actually the keys of the query params and body payload.
 */
export function getAllRowKeys(smartApiDefinition: ModelsSmartAPIDefinition) {
  const queryParams = smartApiDefinition?.queryParams?.map(toName) ?? []
  const bodyPayload = smartApiDefinition?.bodyPayload?.map(toName) ?? []
  return {
    queryParams,
    bodyPayload,
  }
}

/**
 * Retrieve the keys of required fields from the smart API definition.
 * Smart API definition has a required attribute to indicate if a field is required.
 */
export function getRequiredRowKeys(
  smartApiDefinition: ModelsSmartAPIDefinition
) {
  const queryParams =
    smartApiDefinition?.queryParams?.filter(byRequired).map(toName) ?? []
  const bodyPayload =
    smartApiDefinition?.bodyPayload?.filter(byRequired).map(toName) ?? []
  return {
    queryParams,
    bodyPayload,
  }
}

// TODO @kenan: dedup this, this was copied from chat node info
export function mapNodeInfos(
  nodeInfos: ModelsLightNodeInfo[]
): WorkflowNodeInfo[] {
  return nodeInfos.map((nodeInfo) => ({
    ...nodeInfo,
    logs: nodeInfo.logs
      .map((log) => {
        const startTime = dayjsUTC(log.startTime).format('YYMMDDHHmmss.SSS')
        const endTime = log.endTime
          ? dayjsUTC(log.endTime).format('YYMMDDHHmmss.SSS')
          : 'NA'
        const error = log.error ? 'Error: ' + log.error : ''
        return `[${startTime}][${endTime}][${log.status}] ${log.message} ${error}`
      })
      .join('\n'),
    details: JSON.stringify(nodeInfo, null, 4),
    durationInMilliseconds: nodeInfo.durationInMicroSeconds / 1000,
  }))
}

/**
 * Build query params string from query params and selected rows in the table.
 */
export const buildQueryParams = (
  queryParams: FormValueEntry[] | undefined,
  selectedKeys: string[]
): string => {
  if (!queryParams?.length) return ''

  const params = new URLSearchParams()
  queryParams
    .filter((param) => selectedKeys.includes(param.key))
    .forEach((param) => {
      params.append(param.key, param.value)
    })

  const queryString = params.toString()
  return queryString ? `?${queryString}` : ''
}

/**
 * Format the value of a payload based on its type.
 * Date and Date<Array> uses AntD datepicker, its value is Dayjs object
 * @see https://ant.design/components/form#form-demo-time-related-controls
 */
function formatPayloadValue(type: SmartApiValueType, value: any) {
  if (type === 'Date') {
    return dayjsUTC(value).format(ISO_8601_DATE_FORMAT)
  }
  if (type === 'Array<Date>' && Array.isArray(value)) {
    return value.map((date) => dayjsUTC(date).format(ISO_8601_DATE_FORMAT))
  }
  return value
}

const isMethodHasBody = (method: `${HttpMethodSmartAPIURLMethod}`): boolean =>
  ['POST', 'PUT', 'PATCH'].includes(method)

const getContentTypeHeader = (
  requestBodyType: RequestBodyType
): Record<string, string> => {
  if (requestBodyType === 'form-data') {
    return {
      Accept: 'text/event-stream',
    }
  }
  return {
    'Content-Type': ContentType.Json,
    Accept: 'text/event-stream',
  }
}

const buildJsonPayload = (entries: FormValueEntry[]): Record<string, any> =>
  Object.fromEntries(
    entries.map((param) => [
      param.key,
      formatPayloadValue(param.type, param.value),
    ])
  )

function buildFormDataPayload(entries: FormValueEntry[]): FormData {
  const formData = new FormData()
  entries.forEach((param) => {
    const value = formatPayloadValue(param.type, param.value)
    if (param.type === 'Document' || param.type === 'Image') {
      formData.append(param.key, value)
    } else {
      formData.append(param.key, JSON.stringify(value))
    }
  })
  return formData
}

/**
 * Build request body payload from body payload and selected rows in the table.
 * Request payload can be in JSON or form-data format.
 */
export const buildRequestBodyPayload = (
  method: `${HttpMethodSmartAPIURLMethod}`,
  requestBodyType: RequestBodyType,
  selectedKeys: string[],
  bodyPayload?: FormValueEntry[]
): { data: any; headers: Record<string, string> } => {
  if (!isMethodHasBody(method)) {
    throw new Error(`Method: [${method}] should have body payload`)
  }

  if (!bodyPayload?.length) {
    throw new Error('No body payload')
  }

  // Only include data that is selected in the table
  const selectedEntries = bodyPayload.filter(({ key }) =>
    selectedKeys.includes(key)
  )

  if (!selectedEntries.length) {
    return { data: undefined, headers: {} }
  }

  const headers = getContentTypeHeader(requestBodyType)
  const data =
    requestBodyType === 'json'
      ? buildJsonPayload(selectedEntries)
      : buildFormDataPayload(selectedEntries)

  return { data, headers }
}

export const handleStreamingResponse = async (
  url: string,
  method: string,
  data: any,
  onResponse: (response: any) => void,
  onError: (error: any) => void,
  headers?: Record<string, string>
): Promise<void> => {
  await createStreamingApiRequest(
    {
      url,
      method: method.toLowerCase() as Lowercase<HttpMethodSmartAPIURLMethod>,
    },
    {
      body: data,
      headers,
      onMessage: onResponse,
      onError: (error) => {
        onError(error)
      },
    }
  )
}

export const handleRegularResponse = async (
  url: string,
  method: string,
  data: any,
  headers: Record<string, string>
): Promise<any> => {
  const response = await baseRequest.request({
    url,
    method,
    data,
    headers,
  })
  return response.data
}

export const createErrorResponse = (error: unknown) => ({
  codeStatus: 500,
  completionTokens: 0,
  endTime: new Date().toISOString(),
  errorMessage:
    error instanceof Error ? error.message : getMessageFromError(error),
  intents: {},
  nodeInfos: [],
  promptTokens: 0,
  requestMessageID: '',
  responseMessageID: '',
  startTime: new Date().toISOString(),
  status: WorkflowRunningStatusWorkflowRunningStatus.Failed,
  userTokens: 0,
  uuid: '',
})
