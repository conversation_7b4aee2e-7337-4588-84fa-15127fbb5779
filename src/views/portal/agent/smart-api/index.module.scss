.smartApiContainer {
  $gutter: 18px;
  $gutter-sm: 12px;
  height: 100%;

  .wrapper {
    display: flex;
    flex-direction: column;
    padding-inline: $gutter;
    padding-block: $gutter;
    height: calc(100vh - 85px);
    overflow: unset; // fix scrollbar appears when resize vertical spliter from bottom to top
    overflow-x: auto; // fix response body overflows when it is too
  }

  .responseBodyWrapper {
    $titleHeight: 48px;
    // fixes response body last line got cut off
    height: calc(100% - $titleHeight);
  }

  .sectionTitle {
    margin: 0;
    padding: 0;
    font-size: 18px;
    font-weight: 700;
    text-wrap: nowrap;
  }

  .panelBorder {
    border-left: 1px solid var(--ant-color-border);
  }

  .sectionHeader {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0;
    margin-bottom: 16px;
    gap: 12px;

    .urlContainer {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 8px 24px 8px 8px;
      gap: 8px;
      flex: 1;
      min-width: 0;
      height: 38px;
      background: var(--genie-color-bg-container);
      border-radius: 8px;
    }
  }

  .tabs {
    :global {
      .ant-tabs-content-holder {
        padding-inline: 21px;
        padding-block-end: 28px;
      }

      .ant-tabs-tab-active {
        font-weight: 700;
      }
    }
  }

  /**
   * utils
   */
  .upperCase {
    text-transform: uppercase;
  }

  .gutterY {
    padding-block: $gutter;
  }

  .gutterYSm {
    padding-block: $gutter-sm;
  }

  .fullHeight {
    height: 100%;
  }

  .overflowUnset {
    overflow: unset;
  }

  .overflowHidden {
    overflow: hidden;
  }

  .pbSm {
    padding-bottom: $gutter-sm;
  }
}
