import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import invariant from 'utils/invariant'
import type { SmartApiRouteState } from '../types'

export function useRedirectIfError(state: SmartApiRouteState) {
  const navigate = useNavigate()

  useEffect(() => {
    try {
      invariant(
        state?.data?.agent,
        'Agent not found, redirecting to agent list'
      )
      invariant(
        state.data.agent.smartAPI?.smartAPIDefinition,
        'Smart API definition not found, redirecting to agent list'
      )
    } catch (error) {
      console.error(error)
      navigate('/portal/agent')
    }
  }, [state, navigate])
}
