import {
  BodyPayloadTypeBodyPayloadType,
  HttpMethodSmartAPIURLMethod,
  PluginPluginDataType,
} from 'api/data-contracts'
import type { FormValues } from './types'

export const mockInitialValues: FormValues = {
  method: HttpMethodSmartAPIURLMethod.SmartAPIURLMethodGET,
  url: 'users',
  requestBodyType: BodyPayloadTypeBodyPayloadType.FormData,
  isStreamingEnabled: false,
  queryParams: [
    {
      key: 'page',
      value: '1',
      description: 'Page number for pagination',
      required: false,
      type: PluginPluginDataType.Number,
    },
    {
      key: 'limit',
      value: '10',
      description: 'Number of items per page',
      required: false,
      type: PluginPluginDataType.Number,
    },
    {
      key: 'search',
      value: '',
      description: 'Search term to filter results',
      required: false,
      type: PluginPluginDataType.String,
    },
  ],
  bodyPayload: [
    {
      key: 'name',
      value: '',
      description: 'User full name',
      required: true,
      type: PluginPluginDataType.String,
    },
    {
      key: 'email',
      value: '',
      description: 'User email address',
      required: true,
      type: PluginPluginDataType.String,
    },
    {
      key: 'role',
      value: 'user',
      description: 'User role in the system',
      required: false,
      type: PluginPluginDataType.String,
    },
  ],
}
