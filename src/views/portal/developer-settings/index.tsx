import { Checkbox, CheckboxProps, Flex } from 'antd'
import { observer } from 'mobx-react'
import { useEffect } from 'react'
import { useGlobalStore } from 'stores/global'

const DeveloperSettings = () => {
  const { isDeveloperMode, toggleDeveloperMode } = useGlobalStore()

  return (
    <div className="main-layout-main">
      <div className="navigation">
        <div className="title">
          <h1>Developer Settings</h1>
          <p>You can manage your developer settings here.</p>
        </div>
      </div>
      <Flex>
        <Checkbox
          checked={isDeveloperMode}
          onChange={(e) => toggleDeveloperMode(e.target.checked)}
        >
          Enable developer mode to see extra features
        </Checkbox>
      </Flex>
    </div>
  )
}

export default observer(DeveloperSettings)
