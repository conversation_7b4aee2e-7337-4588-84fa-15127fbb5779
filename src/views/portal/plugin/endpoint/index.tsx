import { Form, Menu, MenuProps, Splitter } from 'antd'
import { FormInstance, useForm } from 'antd/es/form/Form'
import { capitalize, last } from 'lodash-es'
import cls from 'classnames'
import {
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react'
import Api from 'views/portal/plugin/components/api'
import Output, { getDefaultOutputValue, OutputType } from '../components/output'

import {
  PluginPluginBodyPayloadType,
  PluginPluginEndpointURLMethod,
  PluginPluginOutputFormType,
} from 'api/data-contracts'

import { FormItemType } from 'components/new-dynamic-form/types'
import { BodyDataType } from '../components/api/body'
import { DataType, getDefaultRecord } from '../components/api/table-section'
import OperationBar from '../components/operation-bar'
import styles from './index.scss'

type EndpointProps = {
  configurationForm?: FormItemType | undefined
  forms?: FormItemDetail[]
  wrapperClassName?: string
}

export type FormItemDetail = {
  name: string
  structure: FormItemType
  endpoint: {
    url: { method: PluginPluginEndpointURLMethod; path: string }
    query?: DataType[]
    body?: BodyDataType
    header?: DataType[]
    output: OutputType
  }
}

export const getDefaultEndpointValue = (): FormItemDetail['endpoint'] => ({
  url: {
    method: PluginPluginEndpointURLMethod.PluginEndpointURLMethodGET,
    path: '',
  },
  query: [],
  body: {
    type: PluginPluginBodyPayloadType.PluginBodyPayloadTypeFormData,
    payload: [],
  },
  header: [],
  output: { type: PluginPluginOutputFormType.Fixed, structure: [] },
})

const Endpoint = forwardRef<unknown, EndpointProps>(
  (props: EndpointProps, ref) => {
    const { configurationForm, forms, wrapperClassName } = props
    const [mainForm] = useForm<FormItemDetail['endpoint']>()
    const [curFormId, setCurFormId] = useState<string>(
      forms?.[0]?.structure?.formItemId ?? ''
    )

    useImperativeHandle(ref, () => ({ form: mainForm }))

    const handleMenuItemClick: MenuProps['onClick'] = ({ key }) => {
      setCurFormId(key)
    }

    useEffect(() => {
      // await Api
      if (!forms || !forms.length) return

      setCurFormId(forms[0]?.structure?.formItemId)
      mainForm.setFieldsValue(
        forms.reduce((pre, cur) => {
          const curEndpoint = cur.endpoint ?? getDefaultEndpointValue()

          // Add a default record if last data is not empty in header, body and query
          Object.keys(curEndpoint).forEach((key) => {
            if (key === 'output') {
              const outputList = Reflect.get(curEndpoint, key)?.structure ?? [
                getDefaultOutputValue(),
              ]
              const lastData = last(outputList)
              if (lastData?.name) {
                const defaultData = getDefaultOutputValue({ required: false })
                outputList?.push(defaultData)
              }
              Reflect.set(pre, cur.structure?.formItemId, curEndpoint)
              return pre
            }

            let v: DataType[] | undefined
            if (key === 'body') {
              v = Reflect.get(curEndpoint, key)?.payload
            } else if (key === 'header' || key === 'query') {
              v = Reflect.get(curEndpoint, key)
            }

            const lastData = last(v)
            if (
              lastData?.key ||
              lastData?.value ||
              lastData?.dataType ||
              lastData?.type
            ) {
              const defaultData = getDefaultRecord({ selected: false })
              v?.push(defaultData)
            }
          })

          Reflect.set(pre, cur.structure?.formItemId, curEndpoint)
          return pre
        }, {})
      )
    }, [forms])

    return (
      <Form
        form={mainForm}
        className={cls(styles.endpointWrapper, wrapperClassName)}
        preserve
      >
        <Splitter>
          <Splitter.Panel defaultSize="290" min="200" collapsible>
            {!!forms?.length && (
              <div className={styles.endpointSiderWrapper}>
                <Menu
                  selectedKeys={[curFormId]}
                  items={forms.map((form) => {
                    const { name, structure, endpoint } = form
                    return {
                      key: structure.formItemId,
                      label: (
                        <div className={styles.endpointSiderLabelWrapper}>
                          <Form.Item noStyle shouldUpdate>
                            {(
                              form: FormInstance<
                                Record<string, FormItemDetail['endpoint']>
                              >
                            ) => {
                              const values = form.getFieldsValue(true)
                              const curMethod =
                                values?.[structure.formItemId]?.url?.method ??
                                endpoint?.url.method
                              return (
                                <span
                                  className={cls({
                                    [styles.endpointSiderLabelTag]: true,
                                    [styles[`tag${capitalize(curMethod)}`]]:
                                      true,
                                  })}
                                >
                                  {curMethod}
                                </span>
                              )
                            }}
                          </Form.Item>
                          <span className={styles.endpointSiderLabelName}>
                            {name}
                          </span>
                        </div>
                      ),
                    }
                  })}
                  onClick={handleMenuItemClick}
                />
              </div>
            )}
          </Splitter.Panel>
          <Splitter.Panel>
            {curFormId && forms && (
              <Form.Item noStyle name={curFormId}>
                <Splitter layout="vertical">
                  <Splitter.Panel>
                    <Api
                      form={mainForm}
                      curFormId={curFormId}
                      formBasename={curFormId}
                      forms={forms.map((f) => f.structure)}
                      configurationForm={configurationForm}
                    />
                  </Splitter.Panel>
                  <Splitter.Panel className={styles.endpointOutputWrapper}>
                    <Output
                      form={mainForm}
                      key={curFormId}
                      formBasename={curFormId}
                    />

                    <OperationBar
                      wrapperClassName={styles.endpointOperationWrapper}
                    />
                  </Splitter.Panel>
                </Splitter>
              </Form.Item>
            )}
          </Splitter.Panel>
        </Splitter>
      </Form>
    )
  }
)

export default memo(Endpoint)
