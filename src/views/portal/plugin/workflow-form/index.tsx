import { forwardRef, memo, useEffect, useMemo, useRef, useState } from 'react'
import { cloneDeep, set } from 'lodash-es'
import cls from 'classnames'

import { WithDraggingProps } from 'components/new-dynamic-form/components/lib/component-wrapper'
import { FormItemType } from 'components/new-dynamic-form/types'
import {
  getItemByPath,
  updateItemByPath,
} from 'components/new-dynamic-form/util'
import { useDropComponents } from 'components/new-dynamic-form/hook'
import { FormContext, FormsContext } from 'components/new-dynamic-form/context'
import { Avatar, Input, InputProps, Splitter, Tabs, TabsProps } from 'antd'
import FormItemSelector from 'components/new-dynamic-form/form-item-selector'
import FormItemRender from 'components/new-dynamic-form/form-item-render'
import FormItemConfig from 'components/new-dynamic-form/form-item-config'

import styles from './index.scss'
import { getRootDefaultValue } from 'components/new-dynamic-form/constants'
import OperationBar from '../components/operation-bar'

type workflowFormProps = {
  treeRoots: FormItemType[]
  setTreeRoots: (node: FormItemType[]) => void
  wrapperClassName?: string
}

type TargetKey = React.MouseEvent | React.KeyboardEvent | string

const WorkflowForm = forwardRef((props: workflowFormProps, ref) => {
  const { wrapperClassName, treeRoots, setTreeRoots } = props
  const rootRef = useRef<HTMLDivElement>(null)

  const [curActiveTabKey, setCurActiveTabKey] = useState(
    treeRoots[0]?.formItemId
  )
  const [currentItem, setCurrentItem] = useState<FormItemType | null>(null)
  const curRoot = useMemo(
    () =>
      treeRoots.find((r) => r.formItemId === curActiveTabKey) ?? treeRoots[0],
    [curActiveTabKey, treeRoots]
  )

  const handleSetCurRoot = (newCurRoot: FormItemType) => {
    const curRootIdx = treeRoots.findIndex(
      (r) => r.formItemId === newCurRoot.formItemId
    )

    const newTreeRoots = [...treeRoots]
    newTreeRoots.splice(curRootIdx, 1, newCurRoot)
    setTreeRoots([...newTreeRoots])
  }

  const handleItemClick = (item: WithDraggingProps<FormItemType> | null) => {
    if (!item) return
    setCurrentItem(item)
  }

  const handleItemUpdate = (item: WithDraggingProps<FormItemType>) => {
    const { path } = item
    const newRoot = cloneDeep(curRoot)
    const newItem = updateItemByPath(newRoot, path, item)
    const newTreeRoots = [...treeRoots]
    newTreeRoots.splice(
      treeRoots.findIndex((r) => r.formItemId === newItem.formItemId),
      1,
      newItem
    )
    setTreeRoots([...newTreeRoots])
  }

  const handleTabAddClick = () => {
    const newTabItemVal = getRootDefaultValue({
      extends: { name: `Feature ${treeRoots.length + 1}` },
    })
    const newTreeRoots = [...treeRoots, newTabItemVal]
    setTreeRoots(newTreeRoots)
    setCurActiveTabKey(newTabItemVal.formItemId)
  }

  const handleTabRemoveClick = (targetKey: TargetKey) => {
    const newTreeRoots = [...treeRoots]
    const targetIdx = newTreeRoots.findIndex((r) => r.formItemId === targetKey)
    newTreeRoots.splice(targetIdx, 1)

    if (newTreeRoots.length && targetKey === curActiveTabKey) {
      const newActiveKey =
        newTreeRoots[
          targetIdx === newTreeRoots.length ? targetIdx - 1 : targetIdx
        ].formItemId

      setCurActiveTabKey(newActiveKey)
    }

    setTreeRoots(newTreeRoots)
  }

  const handleTabEditClick = (
    targetKey: TargetKey,
    action: 'add' | 'remove'
  ) => {
    action === 'add' ? handleTabAddClick() : handleTabRemoveClick(targetKey)
  }

  const handleFeatureNameChange: InputProps['onChange'] = (e) => {
    const newItem = cloneDeep(curRoot)
    set(newItem, 'extends.name', e.target.value)
    handleSetCurRoot(newItem)
  }

  useDropComponents(rootRef, curRoot, handleSetCurRoot)
  useEffect(() => {
    if (!treeRoots) return
    if (!currentItem) {
      setCurrentItem(treeRoots[0].children[0])
      return
    }
    const newCurItem = getItemByPath(curRoot, currentItem?.path)
    setCurrentItem(newCurItem)
  }, [curRoot])

  // render
  const getFormRootRender = (root: FormItemType, tabLabel?: string) => {
    return (
      <FormItemRender
        key={root.formItemId}
        data={root.children}
        header={
          <div className={styles.workflowFormRenderHeaderWrapper}>
            <div className={styles.workflowFormRenderHeaderTitle}>
              <Avatar size={30} shape="square" />
              <Input
                placeholder="Feature Name"
                variant="borderless"
                defaultValue={root.extends?.name ?? tabLabel}
                onChange={handleFeatureNameChange}
              />
            </div>
          </div>
        }
        canEdit
      />
    )
  }
  const tabItems = useMemo<TabsProps['items']>(() => {
    return treeRoots.map((r, idx) => ({
      key: r.formItemId,
      label: r.extends?.name ?? `Feature ${idx}`,
      children: getFormRootRender(r, r.extends?.name ?? `Feature ${idx}`),
    }))
  }, [treeRoots])

  return (
    <FormsContext.Provider
      value={{
        roots: treeRoots,
        setRoots: setTreeRoots,
      }}
    >
      <FormContext.Provider
        value={{
          root: curRoot,
          setRoot: handleSetCurRoot,
          onItemClick: handleItemClick,
        }}
      >
        <Splitter className={cls(styles.workflowFormWrapper, wrapperClassName)}>
          <Splitter.Panel defaultSize={300} min={`15%`} max={432}>
            <FormItemSelector />
          </Splitter.Panel>

          <Splitter.Panel>
            <div className={styles.workflowFormRenderWrapper} ref={rootRef}>
              <Tabs
                className={styles.workflowFormRenderTab}
                type="editable-card"
                size={'small'}
                activeKey={curActiveTabKey}
                onChange={setCurActiveTabKey}
                onEdit={handleTabEditClick}
                items={tabItems}
                destroyInactiveTabPane
              />
              <OperationBar
                wrapperClassName={styles.workflowFormOperationBar}
              />
            </div>
          </Splitter.Panel>

          <Splitter.Panel collapsible defaultSize={400} min={`15%`} max={600}>
            <FormItemConfig
              key={currentItem?.formItemId}
              data={currentItem}
              onUpdate={handleItemUpdate}
            />
          </Splitter.Panel>
        </Splitter>
      </FormContext.Provider>
    </FormsContext.Provider>
  )
})

export default memo(WorkflowForm)
