import BranchSvg from 'assets/images/branch.svg'
import FileSvg from 'assets/images/file.svg'
import FormSvg from 'assets/images/form.svg'

import type { TabsProps } from 'antd'

import styles from './index.scss'

export enum TabItems {
  basicInfo = 'Basic Info',
  configurationForm = 'Configuration Form',
  workflowForm = 'Property Form',
  endpointMapping = 'Endpoint & Mapping',
  TestPublish = 'Test & Publish',
}

export const PluginTabItems: NonNullable<TabsProps['items']> = [
  {
    key: 'basicInfo',
    label: TabItems.basicInfo,
    icon: <FormSvg className={styles.pluginDetailTabItemIcon} />,
  },
  {
    key: 'configurationForm',
    label: TabItems.configurationForm,
    icon: <FileSvg className={styles.pluginDetailTabItemIcon} />,
  },
  {
    key: 'workflowForm',
    label: TabItems.workflowForm,
    icon: <BranchSvg className={styles.pluginDetailTabItemIcon} />,
  },
  {
    key: 'endpointMapping',
    label: TabItems.endpointMapping,
    icon: <BranchSvg className={styles.pluginDetailTabItemIcon} />,
  },
  {
    key: 'testPublish',
    label: TabItems.TestPublish,
    icon: <BranchSvg className={styles.pluginDetailTabItemIcon} />,
  },
]
