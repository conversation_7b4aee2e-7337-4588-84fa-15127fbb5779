import { forwardRef, memo, useImperativeHandle } from 'react'
import cls from 'classnames'
import { Anchor, Col, Row, Form } from 'antd'
import PluginInformation, { FormInformationType } from './information'

import styles from './index.scss'
const AnchorItems = [
  { key: 'pluginInfo', href: '#pluginInfo', title: 'Pugin Info' },
]

export type PluginInformationFormType = {
  pluginInformation: FormInformationType
}

type PluginBasicInfoProps = {}

const PluginBasicInfo = forwardRef<unknown, PluginBasicInfoProps>(
  (props, ref) => {
    const [form] = Form.useForm()

    useImperativeHandle(ref, () => ({ form }))

    return (
      <div className={styles.basicInfoWrapper}>
        <Row>
          <Col span={21}>
            <div
              className={cls(
                styles.basicInfoItemWrapper,
                styles.basicInfoItemBaseInfo
              )}
            >
              <div className={styles.basicInfoItemTitle}>Plugin Info</div>
              <PluginInformation form={form} />
            </div>
          </Col>

          <Col span={3} className={styles.basicInfoAnchorWrapper}>
            <Anchor items={AnchorItems} replace />
          </Col>
        </Row>
      </div>
    )
  }
)

export default memo(PluginBasicInfo)
