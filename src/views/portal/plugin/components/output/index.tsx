import { Form, FormInstance, Radio, RadioGroupProps } from 'antd'
import {
  PluginPluginDataType,
  PluginPluginOutputFormType,
} from 'api/data-contracts'
import { memo, useEffect, useMemo, useState } from 'react'
import { v4 as uuidV4 } from 'uuid'
import { OutputTable } from '../output-table'
import styles from './index.scss'

export type OutputDataType = {
  key: string
  name: string
  required: Boolean
  type: string
  deep: number
  children?: OutputDataType[]
}
export type OutputType = {
  type: PluginPluginOutputFormType
  structure: OutputDataType[]
}

type OutputProps = {
  form: FormInstance
  formBasename?: string
}

export const getDefaultOutputValue = (
  defaultProps?: Partial<OutputDataType>
): OutputDataType => ({
  key: uuidV4(),
  name: '',
  required: false,
  type: PluginPluginDataType.String,
  deep: 1,
  ...defaultProps,
})

const OutputTypeOptions: RadioGroupProps['options'] = [
  { label: 'Fixed', value: PluginPluginOutputFormType.Fixed },
  { label: 'Customized', value: PluginPluginOutputFormType.Customized },
]

const Output = (props: OutputProps) => {
  const { form, formBasename = '' } = props
  const baseNamePath = useMemo(() => [formBasename, 'output'], [formBasename])
  const outputData = Form.useWatch<OutputType>(baseNamePath)
  const [structure, setStructure] = useState<OutputDataType[]>([])

  const handleStructureChange = (newStructure: OutputDataType[]) => {
    setStructure(newStructure)
    form.setFieldValue([...baseNamePath, 'structure'], newStructure)
  }

  useEffect(() => {
    if (outputData && !outputData.structure?.length) {
      form.setFieldValue(
        [...baseNamePath, 'structure'],
        [getDefaultOutputValue()]
      )
      setStructure([getDefaultOutputValue()])
    } else if (outputData?.structure) {
      setStructure(outputData.structure)
    }
  }, [outputData])

  return (
    <div className={styles.outputWrapper}>
      <Form.Item
        label={'Output'}
        layout="vertical"
        className={styles.outputTitle}
      >
        <Form.Item
          name={[...baseNamePath, 'type']}
          label={null}
          initialValue={PluginPluginOutputFormType.Fixed}
          wrapperCol={{ flex: 1 }}
        >
          <Radio.Group
            options={OutputTypeOptions}
            className={styles.outputType}
          />
        </Form.Item>

        <Form.Item noStyle dependencies={[...baseNamePath, 'type']}>
          {(form) => {
            const type = form.getFieldValue([...baseNamePath, 'type'])
            return type === PluginPluginOutputFormType.Fixed ? (
              <OutputTable data={structure} onChange={handleStructureChange} />
            ) : null
          }}
        </Form.Item>
      </Form.Item>
    </div>
  )
}

export default memo(Output)
