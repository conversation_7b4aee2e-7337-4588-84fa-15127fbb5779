import { memo, useState } from 'react'
import styles from './index.scss'
import { Button, Dropdown, Space, Modal, Switch, Radio } from 'antd'
import { first, last } from 'lodash-es'
import cls from 'classnames'
import { PluginDetailPageParameters } from 'views/portal/plugin/detail'
import SaveSvg from 'assets/images/save.svg'
import { getQuery } from 'utils/common'
import { useOperationContext } from 'views/portal/plugin/detail/context'
import { PluginTabItems } from '../../detail/constants'

type OperationBarProps = {
  wrapperClassName?: string
}

const OperationBar = (props: OperationBarProps) => {
  const { wrapperClassName } = props
  const { curTab } = getQuery<PluginDetailPageParameters>(location.search)
  const { next, previous, save, publish } = useOperationContext() ?? {}

  // const [publishModalOpen, setPublishModalOpen] = useState(false)
  // const [publishType, setPublishType] = useState<'public' | 'private'>('public')

  // const handlePublish = () => {
  //   setPublishModalOpen(false)
  //   publish?.()
  // }

  return (
    <div className={cls(styles.operationBarWrapper, wrapperClassName)}>
      <Space className={styles.pluginDetailHeaderRight}>
        {curTab === last(PluginTabItems)?.key && (
          <Dropdown.Button
            type="primary"
            menu={{
              items: [{ label: 'Publish', key: 'publish' }],
              onClick: (info) => {
                if (info.key === 'publish') {
                  publish?.()
                }
              },
            }}
            onClick={save}
          >
            Save
          </Dropdown.Button>
        )}

        {curTab !== last(PluginTabItems)?.key && (
          <Button
            className={styles.pluginDetailHeaderRightBtn}
            onClick={next}
            type="primary"
          >
            Next
          </Button>
        )}

        {curTab !== first(PluginTabItems)?.key && (
          <Button
            className={styles.pluginDetailHeaderRightBtn}
            onClick={previous}
          >
            Previous
          </Button>
        )}

        {curTab !== last(PluginTabItems)?.key && (
          <Button
            className={styles.pluginDetailHeaderRightBtn}
            icon={<SaveSvg className={styles.pluginDetailHeaderRightIcon} />}
            onClick={save}
          >
            Save
          </Button>
        )}

        {/* <Modal
          title="Where do you want to publish the plugin?"
          open={publishModalOpen}
          onOk={handlePublish}
          onCancel={() => setPublishModalOpen(false)}
        >
          <Radio.Group
            options={[
              { label: 'Public', value: 'public' },
              { label: 'Private', value: 'private' },
            ]}
            defaultValue={'public'}
            value={publishType}
            onChange={(e) => setPublishType(e.target.value)}
          />
        </Modal> */}
      </Space>
    </div>
  )
}

export default memo(OperationBar)
