import { memo, useEffect, useMemo, useState } from 'react'
import { merge } from 'lodash-es'
import { Form, FormInstance, Radio, TreeSelectProps } from 'antd'
import { TableRowSelection } from 'antd/es/table/interface'

import TableSection, { DataType, getDefaultRecord } from '../table-section'
import { useApiFormsContext } from 'views/portal/plugin/components/api/context'
import {
  PluginPluginBodyPayloadType,
  PluginPluginDataType,
  PluginPluginInputPayloadType,
} from 'api/data-contracts'
import { convertFormItemsToTreeData } from 'components/new-dynamic-form/util'

type BodyProps = {
  form: FormInstance
  formBasename?: string
}

export type BodyDataType = {
  type: PluginPluginBodyPayloadType
  payload: DataType[]
}

const Body = (props: BodyProps) => {
  const { form, formBasename = '' } = props
  const { configurationForm, forms, mode } = useApiFormsContext() ?? {}

  const referenceTreeData = useMemo<TreeSelectProps['treeData']>(() => {
    const curForm = forms?.find((f) => f.formItemId === formBasename)
    if (!curForm) return
    const configurationData = convertFormItemsToTreeData(
      configurationForm?.children ?? [],
      { pathPrefix: 'configuration' }
    )
    const propertyData = convertFormItemsToTreeData(curForm.children, {
      pathPrefix: 'property',
    })

    return [
      {
        title: 'Configuration Form',
        value: 'configuration_form',
        children: configurationData,
        disabled: true,
      },
      {
        title: 'Property Form',
        value: 'property_form',
        children: propertyData,
        disabled: true,
      },
    ]
  }, [forms])
  const baseNamePath = useMemo(() => [formBasename, 'body'], [formBasename])
  const bodyDataTableData = Form.useWatch<BodyDataType>(baseNamePath, {
    preserve: true,
  })

  const selectedRowKeys = useMemo(
    () =>
      bodyDataTableData?.payload?.filter((i) => i.selected).map((i) => i.id),
    [bodyDataTableData]
  )

  const onRecordChange = (
    namePath: string[],
    key: string,
    value: any,
    record: DataType,
    index: number
  ) => {
    const newData = [...bodyDataTableData?.payload]
    const newRecord: DataType = { ...record }

    if (index === newData?.length - 1) {
      merge(newRecord, {
        selected: true,
        dataType: PluginPluginDataType.String,
        type: PluginPluginInputPayloadType.PluginInputPayloadTypeInput,
      })

      newData.push(getDefaultRecord({ selected: false }))
    }

    if (['dataType', 'type'].includes(key)) {
      Reflect.set(newRecord, 'value', '')
      // Reflect.set(newRecord, 'reference', '')
    }

    Reflect.set(newRecord, key, value)

    // if (
    //   record.type ===
    //   PluginPluginInputPayloadType.PluginInputPayloadTypeReference
    // ) {
    //   Reflect.set(newRecord, 'reference', value)
    // } else {
    //   Reflect.set(newRecord, 'reference', '')
    // }

    newData.splice(index, 1, newRecord)
    form.setFieldValue([...baseNamePath, 'payload'], newData)
  }

  const handleDelete = (record: DataType) => {
    const newData = bodyDataTableData?.payload.filter((v) => v.id !== record.id)
    form.setFieldValue([...baseNamePath, 'payload'], newData)
  }

  const handleRowSelectChange: TableRowSelection<DataType>['onChange'] = (
    selectedRowKeys
  ) => {
    const newData = [...bodyDataTableData?.payload].map((i) => ({
      ...i,
      selected: selectedRowKeys.includes(i.id),
    }))

    form.setFieldValue([...baseNamePath, 'payload'], newData)
  }

  useEffect(() => {
    if (bodyDataTableData && !bodyDataTableData.payload?.length) {
      form.setFieldValue(
        [...baseNamePath, 'payload'],
        [getDefaultRecord({ selected: false })]
      )
    }
  }, [bodyDataTableData])

  return (
    <div>
      <Form.Item name={[...baseNamePath, 'type']} initialValue={'form-data'}>
        <Radio.Group
          options={[
            {
              label: 'form-data',
              value: PluginPluginBodyPayloadType.PluginBodyPayloadTypeFormData,
            },
            {
              label: 'JSON',
              value: PluginPluginBodyPayloadType.PluginBodyPayloadTypeJSON,
            },
          ]}
        />
      </Form.Item>

      <Form.Item noStyle dependencies={[...baseNamePath, 'type']}>
        {(form) => {
          const bodyType: BodyDataType['type'] = form.getFieldValue([
            ...baseNamePath,
            'type',
          ])

          return (
            <TableSection<DataType>
              namePath={[...baseNamePath, 'payload']}
              onCellChange={onRecordChange}
              dataSource={bodyDataTableData?.payload}
              onDelete={handleDelete}
              tableProps={{
                rowSelection: {
                  selectedRowKeys,
                  onChange: handleRowSelectChange,
                },
              }}
              referenceTreeData={referenceTreeData}
              mode={mode}
            />
          )
        }}
      </Form.Item>
    </div>
  )
}

export default memo(Body)
