import {
  Button,
  Form,
  Input,
  Select,
  Table,
  TableProps,
  TreeSelect,
  TreeSelectProps,
} from 'antd'
import { DeleteOutlined } from '@ant-design/icons'
import { nanoid } from 'nanoid'
import {
  PluginPluginDataType,
  PluginPluginInputPayloadType,
} from 'api/data-contracts'
import { useMemo } from 'react'
import { ColumnsType } from 'antd/es/table'
import { capitalizeFirstLetter } from 'utils/format'
import { cloneDeep, last } from 'lodash-es'
import { ValueInput } from 'views/components/value-input'
import { ALL_DATA_TYPE } from 'constants/common'

export type TableSectionProps<D = DataType> = {
  namePath: string[]
  dataSource: D[]
  onCellChange: (
    namePath: string[],
    key: string,
    value: any,
    record: D,
    index: number
  ) => void
  onDelete?: (record: D) => void
  referenceTreeData?: TreeSelectProps['treeData']
  mode?: 'default' | 'test'

  disabledColumns?: string[]
  noHaveColumns?: string[]
  isRowSelection?: boolean
  tableProps?: TableProps<D>
}

export type DataType = {
  id: string
  key: string
  dataType?: (typeof ALL_DATA_TYPE)[number]
  type?: PluginPluginInputPayloadType
  value: string
  description: string
  // reference: string
  selected?: boolean
}

export const getDefaultRecord = (
  defaultProps?: Partial<DataType>
): DataType => ({
  id: nanoid(),
  key: '',
  dataType: undefined,
  type: undefined,
  value: '',
  // reference: '',
  description: '',
  selected: true,
  ...defaultProps,
})

const TableSection = <D extends DataType>(props: TableSectionProps<D>) => {
  const {
    namePath,
    dataSource,
    onCellChange,
    onDelete,
    isRowSelection = true,
    noHaveColumns = [],
    referenceTreeData = [],
    disabledColumns = [],
    tableProps,
    mode,
  } = props

  const isTest = mode === 'test'

  const handleFilterTypeTreeData = (
    tree: typeof referenceTreeData,
    type?: (typeof ALL_DATA_TYPE)[number]
  ) => {
    if (!type) return tree

    const newTree = cloneDeep(tree)
    const processNode = (node: (typeof referenceTreeData)[number]) => {
      if ('dataType' in node) {
        Reflect.set(node, 'disabled', node?.dataType !== type)
      }

      if (node.children) {
        node.children.forEach(processNode)
      }
    }

    newTree.forEach((tree) => processNode(tree))
    return newTree
  }

  const validateUnique = async (record: D, colKey: string, index: number) => {
    if (!colKey) return
    const duplicates = dataSource.filter((item, i) => {
      const val = Reflect.get(record, colKey)
      return i !== index && item.key === val
    })
    if (duplicates.length > 0) {
      return Promise.reject()
    }
  }

  // Render
  const columns = useMemo(() => {
    const allColumns: ColumnsType<D> = [
      {
        title: 'Key',
        dataIndex: 'key',
        key: 'key',
        render: (_, record, index) => {
          return (
            <Form.Item
              name={[...namePath, index, 'key']}
              noStyle
              rules={[
                { validator: () => validateUnique(record, 'key', index) },
              ]}
            >
              <Input
                placeholder={'Key'}
                variant={'borderless'}
                onChange={(e) =>
                  onCellChange(namePath, 'key', e.target.value, record, index)
                }
                disabled={isTest || disabledColumns.includes('key')}
              />
            </Form.Item>
          )
        },
      },
      {
        title: 'DataType',
        dataIndex: 'dataType',
        key: 'dataType',
        width: 140,
        render: (val, record, index) => (
          <Form.Item
            name={[...namePath, index, 'dataType']}
            noStyle
            initialValue={val}
          >
            <Select
              placeholder={PluginPluginDataType.String}
              options={ALL_DATA_TYPE.map((i) => ({
                label: i,
                value: i,
              }))}
              variant="borderless"
              onChange={(val) => {
                onCellChange(namePath, 'dataType', val, record, index)
              }}
              popupMatchSelectWidth={false}
              disabled={isTest || disabledColumns.includes('dataType')}
            />
          </Form.Item>
        ),
      },
      {
        title: 'Type',
        dataIndex: 'type',
        key: 'type',
        width: 140,
        render: (val, record, index) => (
          <Form.Item
            name={[...namePath, index, 'type']}
            noStyle
            initialValue={val}
          >
            <Select
              placeholder={'Input'}
              options={Object.values(PluginPluginInputPayloadType).map((i) => ({
                label: capitalizeFirstLetter(i),
                value: i,
              }))}
              variant="borderless"
              onChange={(val) =>
                onCellChange(namePath, 'type', val, record, index)
              }
              popupMatchSelectWidth={false}
              disabled={isTest || disabledColumns.includes('Type')}
            />
          </Form.Item>
        ),
      },
      {
        title: 'Value',
        dataIndex: 'value',
        key: 'value',
        width: 300,
        render: (_, record, index) => (
          <Form.Item
            name={[...namePath, index, 'value']}
            noStyle
            shouldUpdate={() => true}
            required
          >
            {record.type ===
              PluginPluginInputPayloadType.PluginInputPayloadTypeReference &&
            !isTest ? (
              <TreeSelect
                placeholder="Please Select Value"
                treeData={handleFilterTypeTreeData(
                  referenceTreeData,
                  record.dataType
                )}
                popupMatchSelectWidth={false}
                variant="borderless"
                showSearch
                allowClear
                treeDefaultExpandAll
                onChange={(val) =>
                  onCellChange(namePath, 'value', val, record, index)
                }
              />
            ) : (
              <ValueInput
                variant={'borderless'}
                type={record.dataType}
                required
                placeholder={'Value'}
                onChange={(e) => {
                  const v = e?.target?.value ?? e
                  onCellChange(namePath, 'value', v, record, index)
                }}
                disabled={isTest && dataSource.length === index + 1}
              />
            )}
          </Form.Item>
        ),
      },
      {
        title: 'Description',
        dataIndex: 'description',
        key: 'description',
        render: (_, record, index) => (
          <Form.Item name={[...namePath, index, 'description']} noStyle>
            <Input
              placeholder={'Description'}
              variant={'borderless'}
              onChange={(e) =>
                onCellChange(
                  namePath,
                  'description',
                  e.target.value,
                  record,
                  index
                )
              }
              disabled={isTest || disabledColumns.includes('description')}
            />
          </Form.Item>
        ),
      },
      {
        title: 'Action',
        dataIndex: 'action',
        key: 'action',
        render: (_, record, index) => (
          <>
            {index !== dataSource?.length - 1 && (
              <Button
                danger
                onClick={() => onDelete?.(record)}
                icon={<DeleteOutlined />}
              />
            )}
          </>
        ),
      },
    ]

    let finalCol = [...allColumns]
    if (isTest) {
      finalCol = finalCol.filter((col) => col.key !== 'action')
    }

    noHaveColumns.forEach((colKey: string) => {
      finalCol = finalCol.filter((col) => col.key !== colKey)
    })

    return finalCol
  }, [noHaveColumns, dataSource, referenceTreeData])

  return (
    <Form.Item layout="vertical" wrapperCol={{ flex: 1 }}>
      {!!dataSource?.length && (
        <Table<D>
          columns={columns}
          dataSource={dataSource}
          pagination={false}
          size="small"
          bordered
          {...tableProps}
          rowSelection={
            isRowSelection
              ? {
                  defaultSelectedRowKeys: dataSource
                    ?.filter((i) => i.selected)
                    .map((i) => i.id),
                  getCheckboxProps: (record) => ({
                    disabled: last(dataSource)?.id === record.id,
                  }),
                  ...tableProps?.rowSelection,
                }
              : undefined
          }
          rowKey={'id'}
        />
      )}
    </Form.Item>
  )
}

export default TableSection
