import { memo } from 'react'
import { Tabs, Input, Select, Form, Button, Col } from 'antd'
import StickyBox from 'react-sticky-box'

import styles from './index.scss'
import { Method } from 'axios'
import Parameters from './parameters'
import Header from './header'

import type { FormInstance, TabsProps } from 'antd'
import Body from './body'
import { ApiFormsContext } from './context'
import { FormItemType } from 'components/new-dynamic-form/types'
import { TableSectionProps } from './table-section'
import { isLocal } from 'utils/env'

const options: Array<{ label: Method; value: Method }> = [
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'DELETE', value: 'DELETE' },
  { label: 'PATCH', value: 'PATCH' },
  { label: 'PUT', value: 'PUT' },
]

type ApiProps = {
  configurationForm?: FormItemType
  form: FormInstance
  forms: FormItemType[]
  curFormId: string
  formBasename?: string
  mode?: TableSectionProps['mode']
  onSendClick?: () => void
}

const Api = (props: ApiProps) => {
  const {
    configurationForm,
    form,
    forms,
    formBasename = '',
    mode = 'default',
    onSendClick,
  } = props

  const handleTestClick = () => {
    console.error(form.getFieldsValue())
  }

  const defaultTabItems: TabsProps['items'] = [
    {
      key: 'Parameters',
      label: 'Parameters',
      children: (
        <Parameters
          form={form}
          formBasename={formBasename}
          key={formBasename}
        />
      ),
    },
    {
      key: 'Header',
      label: 'Header',
      children: (
        <Header form={form} formBasename={formBasename} key={formBasename} />
      ),
    },
    {
      key: 'Body',
      label: 'Body',
      children: (
        <Body form={form} formBasename={formBasename} key={formBasename} />
      ),
    },
  ]

  return (
    <ApiFormsContext.Provider value={{ configurationForm, forms, mode }}>
      <div className={styles.apiWrapper}>
        <div className={styles.apiHeaderWrapper}>
          <Col flex={1}>
            <Form.Item
              name={[formBasename, 'url', 'path']}
              className={styles.apiHeaderInput}
            >
              <Input
                addonBefore={
                  <Form.Item
                    name={[formBasename, 'url', 'method']}
                    noStyle
                    initialValue="GET"
                  >
                    <Select options={options} popupMatchSelectWidth={false} />
                  </Form.Item>
                }
                disabled={mode === 'test'}
              />
            </Form.Item>
          </Col>
          {mode === 'test' && onSendClick && (
            <Button type="primary" onClick={onSendClick} disabled={false}>
              Send
            </Button>
          )}
          {isLocal && <Button onClick={handleTestClick}>Local Test</Button>}
        </div>

        <Tabs
          items={defaultTabItems}
          defaultActiveKey={defaultTabItems[0].key}
          renderTabBar={(props, DefaultTabBar) => (
            <StickyBox offsetTop={0} className={styles.apiContentTabBarWrapper}>
              <DefaultTabBar {...props} />
            </StickyBox>
          )}
        />
      </div>
    </ApiFormsContext.Provider>
  )
}

export default memo(Api)
