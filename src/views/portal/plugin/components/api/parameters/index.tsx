import { memo, useEffect, useMemo } from 'react'
import { merge } from 'lodash-es'
import {
  Alert,
  Form,
  FormInstance,
  Input,
  Tag,
  TreeSelectProps,
  Typography,
} from 'antd'

import TableSection, { DataType, getDefaultRecord } from '../table-section'
import styles from './index.scss'
import {
  PluginPluginDataType,
  PluginPluginInputPayloadType,
} from 'api/data-contracts'
import { useApiFormsContext } from 'views/portal/plugin/components/api/context'
import { convertFormItemsToTreeData } from 'components/new-dynamic-form/util'

type ParametersProps = {
  form: FormInstance
  formBasename?: string
}

const Parameters = (props: ParametersProps) => {
  const { form, formBasename = '' } = props
  const { configurationForm, forms, mode } = useApiFormsContext() ?? {}

  const baseNamePath = useMemo(() => [formBasename, 'query'], [formBasename])
  const queryTableData = Form.useWatch<DataType[]>(baseNamePath, {
    preserve: true,
  })
  const referenceTreeData = useMemo<TreeSelectProps['treeData']>(() => {
    const curForm = forms?.find((f) => f.formItemId === formBasename)
    if (!curForm) return
    const configurationData = convertFormItemsToTreeData(
      configurationForm?.children ?? [],
      { pathPrefix: 'configuration_form' }
    )
    const propertyData = convertFormItemsToTreeData(curForm.children, {
      pathPrefix: 'property_form',
    })

    return [
      {
        title: 'Configuration Form',
        value: 'configuration_form',
        children: configurationData,
        disabled: true,
      },
      {
        title: 'Property Form',
        value: 'property_form',
        children: propertyData,
        disabled: true,
      },
    ]
  }, [forms])

  const onRecordChange = (
    namePath: string[],
    key: string,
    value: any,
    record: DataType,
    index: number
  ) => {
    const newData = [...queryTableData]
    const newRecord: DataType = { ...record }

    if (index === newData?.length - 1) {
      merge(newRecord, {
        selected: true,
        dataType: PluginPluginDataType.String,
        type: PluginPluginInputPayloadType.PluginInputPayloadTypeInput,
      })

      newData.push(getDefaultRecord({ selected: false }))
    }

    if (['dataType', 'type'].includes(key)) {
      Reflect.set(newRecord, 'value', '')
      // Reflect.set(newRecord, 'reference', '')
    }

    Reflect.set(newRecord, key, value)

    // if (
    //   record.type ===
    //   PluginPluginInputPayloadType.PluginInputPayloadTypeReference
    // ) {
    //   Reflect.set(newRecord, 'reference', value)
    // } else {
    //   Reflect.set(newRecord, 'reference', '')
    // }

    newData.splice(index, 1, newRecord)
    form.setFieldValue(baseNamePath, newData)
  }

  const handleDelete = (record: DataType) => {
    const newData = queryTableData?.filter?.((v) => v.id !== record.id)
    form.setFieldValue(baseNamePath, newData)
  }

  useEffect(() => {
    if (queryTableData && !queryTableData.length) {
      form.setFieldValue(baseNamePath, [getDefaultRecord({ selected: false })])
    }
  }, [queryTableData])

  return (
    <div className={styles.parametersWrapper}>
      <Alert
        className={styles.parametersDescriptionWrapper}
        message={'Adding Path Parameters'}
        description={
          <>
            To include path parameters in your URL, use curly braces like{' '}
            {'{parameter}'}. Example:
            <Input
              disabled
              defaultValue={`https://www.google.com/search?q={keyword}`}
            />
          </>
        }
      />
      <TableSection<DataType>
        namePath={baseNamePath}
        onCellChange={onRecordChange}
        dataSource={queryTableData}
        onDelete={handleDelete}
        isRowSelection={false}
        referenceTreeData={referenceTreeData}
        mode={mode}
      />
    </div>
  )
}

export default memo(Parameters)
