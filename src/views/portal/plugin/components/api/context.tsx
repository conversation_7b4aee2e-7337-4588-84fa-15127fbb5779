import { FormItemType } from 'components/new-dynamic-form/types'
import { createContext, useContext } from 'react'
import { TableSectionProps } from './table-section'

// Forms
export type FormsContextProps = {
  configurationForm?: FormItemType
  forms?: FormItemType[]
  mode?: TableSectionProps['mode']
}

export const ApiFormsContext = createContext<FormsContextProps | null>(null)

export const useApiFormsContext = () => {
  const value = useContext(ApiFormsContext)
  return value
}
