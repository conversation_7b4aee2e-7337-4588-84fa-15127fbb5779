.test-plugin {
  &-wrapper {
    height: -webkit-fill-available;
  }

  &-sider {
    &-wrapper {
      :global {
        .ant-menu {
          border: none !important;
        }

        .ant-menu-item-selected {
          background-color: var(--genie-color-bg-container);
        }
      }

      padding: 16px;
    }
  }

  &-label {
    &-wrapper {
      display: flex;
      flex-flow: row nowrap;
      column-gap: 4px;
    }

    &-tag {
      text-transform: uppercase;
      flex: 0 0 64px;

      font-weight: 700;
      font-size: 14px;
      line-height: 20px;
    }

    &-name {
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
      color: var(--ant-color-text-secondary);
    }
  }

  &-response {
    &-wrapper {
      position: relative;
    }
    &-body {
      height: calc(100% - 80px) !important;
    }
  }

  &-operation {
    &-wrapper {
      position: absolute;
      left: 18px;
      bottom: 18px;
      margin-bottom: 0;
      padding: 0;
    }
  }
}
