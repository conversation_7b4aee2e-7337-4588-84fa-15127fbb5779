import { memo, ReactNode } from 'react'
import cls from 'classnames'
import styles from './index.scss'

type CardWrapperProps = {
  tip?: string
  children?: ReactNode
  wrapperClassName?: string
  contentClassName?: string
}

const CardWrapper = (props: CardWrapperProps) => {
  const { tip, children, wrapperClassName, contentClassName } = props
  return (
    <div className={cls(wrapperClassName, styles.cardWrapper)}>
      <div className={cls(contentClassName, styles.cardWrapperBox)}>
        {children}
      </div>
      <div className={styles.cardWrapperTip}>{tip}</div>
    </div>
  )
}

export default memo(CardWrapper)
