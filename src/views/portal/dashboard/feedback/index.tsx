import React, { memo, useEffect, useMemo, useState } from 'react'
import dayjs from 'dayjs'
import cls from 'classnames'
import ReactECharts from 'echarts-for-react'
import { ResponsesConversationStatsWithDetailResponse } from 'api/data-contracts'
import CardWrapper from '../card-wrapper'

import styles from './index.scss'

type FeedbackProps = {
  data: ResponsesConversationStatsWithDetailResponse[]
}

const todayStart = dayjs().startOf('day')
const todayEnd = dayjs().endOf('day')

const Feedback = (props: FeedbackProps) => {
  const { data } = props
  const [weeklyChartOption, setWeeklyChartOption] = useState<any>(null)

  const todayList = useMemo(
    () =>
      data.filter(
        (item) =>
          dayjs(item.startAt).isBefore(todayEnd) &&
          dayjs(item.endAt).isAfter(todayStart)
      ),
    [data]
  )

  useEffect(() => {
    if (!data.length) return
    const weeklyData = Array.from({ length: 7 }, (_, i) =>
      dayjs()
        .subtract(i + 1, 'day')
        .format('MM-DD')
    )
      .reverse()
      .reduce<Record<string, [number, number]>>((pre, cur) => {
        Reflect.set(pre, cur, [0, 0])
        return pre
      }, {})

    data.forEach((date) => {
      const { startAt, likes, dislikes } = date
      Reflect.set(weeklyData, dayjs(startAt).format('MM-DD'), [
        likes.length,
        dislikes.length,
      ])
    })

    setWeeklyChartOption({
      tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
      xAxis: { type: 'category', data: Object.keys(weeklyData) },
      yAxis: { minInterval: 1, type: 'value' },
      series: [{ type: 'bar' }, { type: 'bar' }],
      dataset: {
        dimensions: ['data', 'Up', 'Down'],
        source: Object.entries(weeklyData).map((d) => {
          const [k, v] = d
          return [k, ...v]
        }),
      },
    })
  }, [data])

  return (
    <div className={styles.feedbackWrapper}>
      <div className={styles.feedbackFirstLine}>
        <CardWrapper
          tip={'Thumbs Up'}
          wrapperClassName={styles.feedbackSquareCard}
          contentClassName={cls(
            styles.feedbackSquareCardContent,
            styles.feedbackUp
          )}
        >
          {todayList.reduce((pre, cur) => pre + cur.likes.length, 0)}
        </CardWrapper>

        <CardWrapper
          tip={'Thumbs Down'}
          wrapperClassName={styles.feedbackSquareCard}
          contentClassName={cls(
            styles.feedbackSquareCardContent,
            styles.feedbackDown
          )}
        >
          {todayList.reduce((pre, cur) => pre + cur.dislikes.length, 0)}
        </CardWrapper>
      </div>

      <CardWrapper
        tip={'No of Thumbs Up/Down (Weekly)'}
        contentClassName={styles.feedbackWeeklyContent}
      >
        {weeklyChartOption && <ReactECharts option={weeklyChartOption} />}
      </CardWrapper>
    </div>
  )
}

export default memo(Feedback)
