import React, { memo, ReactNode } from 'react'
import styles from './index.scss'

type BoardWrapperProps = {
  title?: string
  children?: ReactNode
}

const BoardWrapper = (props: BoardWrapperProps) => {
  const { title, children } = props
  return (
    <div className={styles.boardWrapper}>
      <div className={styles.boardWrapperTitle}>{title}</div>
      {children}
    </div>
  )
}

export default memo(BoardWrapper)
