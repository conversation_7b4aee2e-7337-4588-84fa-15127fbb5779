import { ResponsesConversationStatsWithDetailResponse } from 'api/data-contracts'
import cls from 'classnames'
import dayjs from 'dayjs'
import ReactECharts from 'echarts-for-react'
import { memo, useEffect, useMemo, useState } from 'react'
import CardWrapper from '../card-wrapper'
import styles from './index.scss'

type UsageProps = {
  data: ResponsesConversationStatsWithDetailResponse[]
}

const todayStart = dayjs().startOf('day')
const todayEnd = dayjs().endOf('day')

const Usage = (props: UsageProps) => {
  const { data } = props

  const [weeklyChartOption, setWeeklyChartOption] = useState<any>(null)

  const todayList = useMemo(
    () =>
      data.filter(
        (item) =>
          dayjs(item.startAt).isBefore(todayEnd) &&
          dayjs(item.endAt).isAfter(todayStart)
      ),
    [data]
  )

  useEffect(() => {
    if (!data.length) return
    const weeklyData = Array.from({ length: 7 }, (_, i) =>
      dayjs()
        .subtract(i + 1, 'day')
        .format('MM-DD')
    )
      .reverse()
      .reduce((pre, cur) => {
        Reflect.set(pre, cur, 0)
        return pre
      }, {})

    data.forEach((date) => {
      const { startAt, totalConversations } = date
      Reflect.set(
        weeklyData,
        dayjs(startAt).format('MM-DD'),
        totalConversations
      )
    })

    setWeeklyChartOption({
      tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
      xAxis: { type: 'category', data: Object.keys(weeklyData) },
      yAxis: { type: 'value' },
      series: [{ data: Object.values(weeklyData), type: 'bar' }],
    })
  }, [data])

  return (
    <div className={styles.usageWrapper}>
      <CardWrapper
        tip={'No of Conversation (Daily)'}
        wrapperClassName={styles.usageSquareCard}
        contentClassName={cls(
          styles.usageSquareCardContent,
          styles.usageConversationDaily
        )}
      >
        {todayList.length}
      </CardWrapper>

      <CardWrapper
        tip={'No of Conversation (Weekly)'}
        wrapperClassName={styles.usageConversationWeeklyWrapper}
        contentClassName={styles.usageConversationWeeklyContent}
      >
        {weeklyChartOption && <ReactECharts option={weeklyChartOption} />}
      </CardWrapper>

      <CardWrapper
        tip={'No of Interactions'}
        wrapperClassName={styles.usageSquareCard}
        contentClassName={cls(
          styles.usageSquareCardContent,
          styles.usageInteraction
        )}
      >
        {todayList.reduce((pre, cur) => pre + cur.totalConversations, 0)}
      </CardWrapper>

      <CardWrapper
        tip={'Response Time'}
        wrapperClassName={styles.usageSquareCard}
        contentClassName={cls(
          styles.usageSquareCardContent,
          styles.usageResponseTime
        )}
      >
        {todayList.length > 0 && (
          <div>
            {Math.floor(
              data.reduce((pre, cur) => pre + cur.averageInteractions, 0) /
              data.length
            )}
            <span className={styles.usageResponseTimeUnit}>s</span>
          </div>
        )}
      </CardWrapper>
    </div>
  )
}

export default memo(Usage)
