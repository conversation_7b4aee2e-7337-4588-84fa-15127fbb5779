import {
  useState,
  forwardRef,
  useImperativeHandle,
  ForwardRefRenderFunction,
  useEffect,
} from 'react'
import { <PERSON><PERSON>, <PERSON>confirm, A<PERSON>, Flex } from 'antd'
import EditSvg from 'assets/images/edit.svg'
import DeleteSvg from 'assets/images/delete.svg'
import { formatDate } from 'utils/filter'
import { getMessageFromError } from 'utils/common'
import { v1ApiKeyListList, v1ApiKeyDelete } from 'api/Api'
import { ResponsesAPIKeyResponse } from 'api/data-contracts'
import { useIsAdmin } from 'hooks/use-is-admin'
import CustomTable from 'components/custom-table'
import styles from './api-key-table.scss'

export enum ActionType {
  Edit = 'edit',
  Delete = 'delete',
}

interface ApiKeyTableProps {
  onAction?: (type: ActionType, apiKey: ResponsesAPIKeyResponse) => void
}

export interface ApiKeyTableRef {
  refresh: () => void
}

const ApiKeyTable: ForwardRefRenderFunction<
  ApiKeyTableRef,
  ApiKeyTableProps
> = (props, ref) => {
  const { message } = App.useApp()
  const [data, setData] = useState<ResponsesAPIKeyResponse[]>([])
  const [loading, setLoading] = useState(false)
  const [deletingId, setDeletingId] = useState(0)
  const isAdmin = useIsAdmin()

  useImperativeHandle(ref, () => ({
    refresh: () => fetchData(),
  }))

  const fetchData = async () => {
    setLoading(true)
    try {
      const response = await v1ApiKeyListList()
      setData(response.data.keys)
    } catch (error) {
      message.error(getMessageFromError(error))
    }
    setLoading(false)
  }

  const deleteApiKey = async (key: number) => {
    setDeletingId(key)
    try {
      await v1ApiKeyDelete({ id: key })
    } catch (error) {
      message.error(getMessageFromError(error))
    }
    setDeletingId(0)
    fetchData()
  }

  useEffect(() => {
    fetchData()
  }, [])

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      editable: false,
      width: 180,
      render: (text: string, record: ResponsesAPIKeyResponse) => {
        return <div key={'name_' + record.id}>{text}</div>
      },
    },
    {
      title: 'Client ID',
      dataIndex: 'clientID',
      editable: false,
      minWidth: 100,
      render: (text: string, record: ResponsesAPIKeyResponse) => {
        return <div key={'client_id_' + record.id}>{text}</div>
      },
    },
    {
      title: 'Secret Key',
      dataIndex: 'secret',
      editable: false,
      minWidth: 150,
      render: (text: string, record: ResponsesAPIKeyResponse) => {
        return <div key={'secret_key_' + record.id}>{text}</div>
      },
    },
    {
      title: 'Last Used At',
      dataIndex: 'latestUsageTime',
      width: 180,
      render: (text: string, record: ResponsesAPIKeyResponse) => (
        <div key={'last_used_at_' + record.id}>{formatDate(text)}</div>
      ),
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      width: 180,
      render: (text: string, record: ResponsesAPIKeyResponse) => (
        <div key={'created_at_' + record.id}>{formatDate(text)}</div>
      ),
    },
    {
      title: 'Updated At',
      dataIndex: 'updatedAt',
      width: 180,
      render: (text: string, record: ResponsesAPIKeyResponse) => (
        <div key={'updated_at_' + record.id}>{formatDate(text)}</div>
      ),
    },
    {
      title: 'Actions',
      width: 80,
      render: (_: any, record: ResponsesAPIKeyResponse) => {
        return (
          <Flex key={'actions_' + record.id} gap="6px">
            <Button
              key={'edit_' + record.id}
              type="text"
              icon={<EditSvg className={styles.tableIcon} />}
              disabled={false}
              onClick={() => {
                props.onAction && props.onAction(ActionType.Edit, record)
              }}
            />
            <Popconfirm
              key={'popconfirm_' + record.id}
              title="Are you sure to delete the API key?"
              onConfirm={() => deleteApiKey(record.id)}
            >
              <Button
                key={'delete_' + record.id}
                type="text"
                icon={<DeleteSvg className={styles.tableIcon} />}
                disabled={!isAdmin}
                loading={deletingId === record.id}
              />
            </Popconfirm>
          </Flex>
        )
      },
    },
  ]

  return (
    <CustomTable<ResponsesAPIKeyResponse>
      wrapperClassName={styles.apiKeyTable}
      columns={columns}
      data={data}
      loading={loading}
      scroll={{
        scrollToFirstRowOnChange: true,
        x: true,
        // page margin + title + title margin + tab content + tab margin + pagination content + pagination margin + table title
        y: 'calc(100vh - 2*24px - 38px - 24px - 46px - 16px - 32px - 64px)',
      }}
      noPagination
    />
  )
}

export default forwardRef(ApiKeyTable)
