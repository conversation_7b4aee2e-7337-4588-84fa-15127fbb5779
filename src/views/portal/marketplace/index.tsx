import { Tabs, type TabsProps } from 'antd'
import { useEffect } from 'react'
import { useNavigate, useOutletContext, useParams } from 'react-router-dom'
import { setQuery } from 'utils/common'
import { InstalledTab } from './components/installed-tab'
import MarketplaceFilterBar from './components/market-place-filter-bar'
import MarketplaceTab from './components/marketplace-tab'
import PluginsTab from './components/plugins-tab'
import { MarketplaceOutletContext } from './hooks/use-marketplace-outlet-context-value'
import styles from './index.module.scss'
import { useMainLayoutContext } from 'layouts/portal/page-main-layout/context'

const tabItems = [
  {
    key: 'market',
    label: 'Market',
    children: <MarketplaceTab />,
  },
  {
    key: 'my-plugins',
    label: 'My Plugins',
    children: <PluginsTab />,
  },
  {
    key: 'installed',
    label: 'Installed',
    children: <InstalledTab />,
  },
] as const satisfies TabsProps['items']

type MarketplaceTabKeys = (typeof tabItems)[number]['key']

export function Marketplace() {
  const { tab } = useParams<{ tab: MarketplaceTabKeys }>()
  const navigate = useNavigate()
  const { setActions, setRegisterEvents } = useMainLayoutContext() ?? {}
  // const { setHeaderActionItems } =
  //   useOutletContext<MarketplaceOutletContext>() ?? {}

  const handleTabChange = (activeKey: string) => {
    navigate({
      pathname: `/portal/marketplace/${activeKey}`,
      search: location.search,
    })
  }

  const handleCreatePlugin = () => {
    const search = setQuery({ opType: 'create' })
    navigate(`/portal/marketplace/my-plugins/details?${search}`)
  }

  useEffect(() => {
    if (tab !== 'my-plugins') setRegisterEvents?.([() => setActions?.([])])

    setRegisterEvents?.([
      () =>
        setActions?.([
          {
            text: 'Create Plugin',
            isPrimary: true,
            onClick: handleCreatePlugin,
          },
        ]),
    ])
  }, [tab])

  return (
    <Tabs
      items={tabItems}
      activeKey={tab}
      onChange={handleTabChange}
      className={styles.tabs}
      destroyInactiveTabPane
      tabBarExtraContent={{
        right: <MarketplaceFilterBar />,
      }}
    />
  )
}

export default Marketplace
