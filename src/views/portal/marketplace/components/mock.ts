import type { FormItemType } from 'components/new-dynamic-form/types'
import { sleep } from 'utils/common'

const res: FormItemType = {
  path: ['root'],
  formItemId: '123',
  type: 'section',
  children: [
    {
      formItemId: 'e04610f0-ee57-4c72-8a5f-d9b2c6d2175e',
      path: [
        'd3c67d87-19bd-45a6-8c32-f8f1e0fde540',
        'e04610f0-ee57-4c72-8a5f-d9b2c6d2175e',
      ],
      type: 'section',
      props: {
        formItemConfig: {
          label: 'Groq',
          name: 'section_ntO1',
          required: false,
        },
      },
      children: [
        {
          formItemId: '0b5c025c-6d09-4b56-b843-8edb4dfc059f',
          path: [
            'd3c67d87-19bd-45a6-8c32-f8f1e0fde540',
            'e04610f0-ee57-4c72-8a5f-d9b2c6d2175e',
            '0b5c025c-6d09-4b56-b843-8edb4dfc059f',
          ],
          type: 'switch',
          props: {
            formItemConfig: {
              label: 'Enabled',
              name: 'switch_iso-',
              required: true,
            },
            generalConfig: {
              dataType: 'Boolean',
            },
          },
          children: [],
        },
        {
          formItemId: '09594a98-84e8-4e83-8a00-e3ee809c5729',
          path: [
            'd3c67d87-19bd-45a6-8c32-f8f1e0fde540',
            'e04610f0-ee57-4c72-8a5f-d9b2c6d2175e',
            '09594a98-84e8-4e83-8a00-e3ee809c5729',
          ],
          type: 'input',
          props: {
            formItemConfig: {
              label: 'Secret Key',
              layout: 'vertical',
              name: 'secret_key',
              required: true,
            },
            generalConfig: {
              condition: [
                [
                  {
                    operator: '===',
                    targetKey: 'section_ntO1.switch_iso-',
                    targetVal: 'true',
                  },
                ],
              ],
              dataType: 'String',
            },
            inputConfig: {
              allowClear: false,
            },
          },
          children: [],
        },
        {
          path: [
            'd3c67d87-19bd-45a6-8c32-f8f1e0fde540',
            'e04610f0-ee57-4c72-8a5f-d9b2c6d2175e',
            'c9679fa3-a4ad-462f-992c-67cacbfa9119',
          ],
          formItemId: 'c9679fa3-a4ad-462f-992c-67cacbfa9119',
          type: 'input',
          children: [],
          props: {
            formItemConfig: {
              name: 'input_5ypp',
              label: 'Input',
            },
            generalConfig: {
              dataType: 'String',
            },
          },
        },
        {
          path: [
            'd3c67d87-19bd-45a6-8c32-f8f1e0fde540',
            'e04610f0-ee57-4c72-8a5f-d9b2c6d2175e',
            'd597f113-ba34-466d-8140-dc88cdb855e8',
          ],
          formItemId: 'd597f113-ba34-466d-8140-dc88cdb855e8',
          type: 'checkbox',
          children: [],
          props: {
            formItemConfig: {
              name: 'checkbox_f0Q7',
              label: 'Checkbox',
            },
            generalConfig: {
              dataType: 'Array<String>',
            },
          },
        },
      ],
    },
    {
      formItemId: '94f49721-7050-4600-b1c9-74c746ef669f',
      path: [
        'd3c67d87-19bd-45a6-8c32-f8f1e0fde540',
        '94f49721-7050-4600-b1c9-74c746ef669f',
      ],
      type: 'section',
      props: {
        formItemConfig: {
          label: 'Samba Nova',
          name: 'section_Kjg2',
          required: false,
        },
      },
      children: [
        {
          path: [
            'd3c67d87-19bd-45a6-8c32-f8f1e0fde540',
            '94f49721-7050-4600-b1c9-74c746ef669f',
            '62502648-2f01-480f-bdea-6979191eca05',
          ],
          formItemId: '62502648-2f01-480f-bdea-6979191eca05',
          type: 'checkbox',
          children: [],
          props: {
            formItemConfig: {
              name: 'checkbox_JcAW',
              label: 'Checkbox',
            },
            generalConfig: {
              dataType: 'Array<String>',
            },
          },
        },
        {
          formItemId: 'd45dacaf-ed7c-4461-ae0c-a7485f8cd5a9',
          path: [
            'd3c67d87-19bd-45a6-8c32-f8f1e0fde540',
            '94f49721-7050-4600-b1c9-74c746ef669f',
            'd45dacaf-ed7c-4461-ae0c-a7485f8cd5a9',
          ],
          type: 'switch',
          props: {
            formItemConfig: {
              label: 'Enabled',
              name: 'switch_mulx',
              required: true,
            },
            generalConfig: {
              dataType: 'Boolean',
            },
          },
          children: [],
        },
        {
          formItemId: 'e9f794a5-2332-48e2-bfb2-c5ec08c893bb',
          path: [
            'd3c67d87-19bd-45a6-8c32-f8f1e0fde540',
            '94f49721-7050-4600-b1c9-74c746ef669f',
            'e9f794a5-2332-48e2-bfb2-c5ec08c893bb',
          ],
          type: 'input',
          props: {
            formItemConfig: {
              label: 'API Key',
              name: 'input_OQuD',
              required: true,
            },
            generalConfig: {
              condition: [
                [
                  {
                    operator: '===',
                    targetKey: 'section_Kjg2.switch_mulx',
                    targetVal: 'true',
                  },
                ],
              ],
              dataType: 'String',
            },
          },
          children: [],
        },
        {
          path: [
            'd3c67d87-19bd-45a6-8c32-f8f1e0fde540',
            '94f49721-7050-4600-b1c9-74c746ef669f',
            '59997856-ddce-4616-a417-23eeb6c7cc69',
          ],
          formItemId: '59997856-ddce-4616-a417-23eeb6c7cc69',
          type: 'select',
          children: [],
          props: {
            formItemConfig: {
              name: 'select_0-xq',
              label: 'Select',
            },
            generalConfig: {
              dataType: 'String',
            },
          },
        },
      ],
    },
    {
      formItemId: '4ce27b91-27c9-4288-8532-0c2371f70e66',
      path: [
        'd3c67d87-19bd-45a6-8c32-f8f1e0fde540',
        '4ce27b91-27c9-4288-8532-0c2371f70e66',
      ],
      type: 'section',
      props: {
        formItemConfig: {
          label: 'Ali Cloud',
          name: 'section_is_S',
          required: false,
        },
      },
      children: [
        {
          formItemId: 'ea85ed29-f80c-4520-a8ee-00b2acbd00c2',
          path: [
            'd3c67d87-19bd-45a6-8c32-f8f1e0fde540',
            '4ce27b91-27c9-4288-8532-0c2371f70e66',
            'ea85ed29-f80c-4520-a8ee-00b2acbd00c2',
          ],
          type: 'switch',
          props: {
            formItemConfig: {
              label: 'Enabled',
              name: 'switch_HpFl',
              required: true,
            },
            generalConfig: {
              dataType: 'Boolean',
            },
          },
          children: [],
        },
        {
          formItemId: 'b0337adc-530e-471b-b803-f726ea22d7fd',
          path: [
            'd3c67d87-19bd-45a6-8c32-f8f1e0fde540',
            '4ce27b91-27c9-4288-8532-0c2371f70e66',
            'b0337adc-530e-471b-b803-f726ea22d7fd',
          ],
          type: 'input',
          props: {
            formItemConfig: {
              label: 'API Key',
              name: 'input_AyWe',
              required: true,
            },
            generalConfig: {
              condition: [
                [
                  {
                    operator: '===',
                    targetKey: 'section_is_S.switch_HpFl',
                    targetVal: 'true',
                  },
                ],
              ],
              dataType: 'String',
            },
          },
          children: [],
        },
      ],
    },
  ],
  props: {
    generalConfig: {
      name: 'root',
      label: 'Root Section',
    },
  },
}

export const getFormData = async (pluginId: number) => {
  await sleep(1000)
  return {
    data: {
      section_ntO1: {},
      section_Kjg2: {
        switch_mulx: true,
      },
      section_is_S: {
        switch_HpFl: true,
      },
    },
    structure: res,
  }
}
