import { DeleteOutlined, FormOutlined } from '@ant-design/icons'
import { App, Avatar, Dropdown, Space, Tag, type MenuProps } from 'antd'
import {
  v1PluginFormsConfigurationFormDetail,
  v1PluginInstalledAddCreate,
  v1PluginInstalledConfigurationFormPartialUpdate,
  v1PluginInstalledRemoveCreate,
  v1PluginsDelete,
} from 'api/Api'
import { type ResponsesPluginWithLatestFormsOfAllStatusesResponse } from 'api/data-contracts'
import classNames from 'classnames'
import CustomCard from 'components/custom-card'
import type { FormItemType } from 'components/new-dynamic-form/types'
import { memo, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { getMessageFromError, setQuery } from 'utils/common'
import { formatDate } from 'utils/filter'
import { capitalizeFirstLetter } from 'utils/format'

import { ConfigurePluginModal } from '../../components/configure-plugin-modal'
import styles from './index.scss'
import CustomAvatar from 'components/custom-avatar'
import PluginColor from 'assets/images/plugin-color.svg'

type PluginCardProps = {
  data: ResponsesPluginWithLatestFormsOfAllStatusesResponse
  onUpdate?: (...args: any[]) => void
}

const getActionMenuItems = (isInstalled: boolean): MenuProps['items'] => {
  const baseItems = [
    { key: 'edit', label: 'Edit Info', icon: <FormOutlined /> },
    { key: 'delete', label: 'Delete', icon: <DeleteOutlined /> },
  ]

  return baseItems
}

const PluginCard = (props: PluginCardProps) => {
  const { data, onUpdate } = props
  const { iconUUID, name, description, id, latestUpdatedAt } = data
  const navigate = useNavigate()
  const { message, modal } = App.useApp()
  const [isLoading, setIsLoading] = useState(false)
  const [isInstallModalOpen, setIsInstallModalOpen] = useState(false)
  const [formStructure, setFormStructure] = useState<FormItemType | null>(null)

  const isInstalled = !!data.pluginInstalled

  const handleCardClick = () => {
    const search = setQuery({ opType: 'view' })
    navigate(`/portal/marketplace/my-plugins/details/${id}?${search}`)
  }

  const handleMenuItemClick: MenuProps['onClick'] = (info) => {
    const { key } = info
    if (key === 'edit') {
      const search = setQuery({ opType: 'edit' })
      navigate(`/portal/marketplace/my-plugins/details/${id}?${search}`)
    } else if (key === 'delete') {
      modal.confirm({
        title: 'Delete Plugin',
        content:
          'Are you sure you want to delete this plugin? This action cannot be undone.',
        okText: 'Delete',
        okType: 'danger',
        cancelText: 'Cancel',
        onOk: async () => {
          try {
            setIsLoading(true)
            await v1PluginsDelete(id)
            message.success('Plugin deleted successfully')
            onUpdate?.({})
          } catch (error) {
            message.error(
              `Failed to delete plugin: ${getMessageFromError(error)}`
            )
          } finally {
            setIsLoading(false)
          }
        },
      })
    }
  }

  const handleInstall = async () => {
    setIsLoading(true)
    try {
      const res = await v1PluginFormsConfigurationFormDetail(
        data.latestDraft.id
      )
      setFormStructure(res.data.configurationForm as unknown as FormItemType)
      setIsInstallModalOpen(true)
    } catch (error) {
      message.error(
        `Failed to load plugin configuration: ${getMessageFromError(error)}`
      )
    } finally {
      setIsLoading(false)
    }
  }

  const handleFormSubmit = async (values: any) => {
    try {
      setIsLoading(true)
      if (isInstalled) {
        await v1PluginInstalledConfigurationFormPartialUpdate(id, {
          inputs: values,
        })
        message.success('Configuration saved successfully')
      } else {
        // Install plugin with configuration
        await v1PluginInstalledAddCreate(
          id,
          { is_marketplace: false },
          { inputs: values }
        )
        message.success('Plugin installed successfully')
      }
      setIsInstallModalOpen(false)
      onUpdate?.({})
    } catch (error) {
      const action = isInstalled ? 'save configuration' : 'install plugin'
      message.error(`Failed to ${action}: ${getMessageFromError(error)}`)
    } finally {
      setIsLoading(false)
    }
  }

  const handleUninstall = async () => {
    modal.confirm({
      title: 'Uninstall Plugin',
      content: 'Are you sure you want to uninstall this plugin?',
      okText: 'Uninstall',
      cancelText: 'Cancel',
      onCancel: () => {
        setIsLoading(false)
      },
      onOk: async () => {
        try {
          await v1PluginInstalledRemoveCreate(id)
          message.success('Plugin uninstalled successfully')
          onUpdate?.({})
        } catch (error) {
          message.error(
            `Failed to uninstall plugin: ${getMessageFromError(error)}`
          )
        } finally {
          setIsLoading(false)
        }
      },
    })
  }

  return (
    <>
      <CustomCard onClick={handleCardClick}>
        <CustomCard.Header
          title={name}
          subTitle={`Updated at ${formatDate(latestUpdatedAt)}`}
          icon={
            <CustomAvatar
              iconUUID={iconUUID}
              shape="square"
              size={54}
              defaultIcon={<PluginColor fontSize={36} />}
            />
          }
        />
        <CustomCard.Content>{description}</CustomCard.Content>
        <CustomCard.Footer
          tags={
            <Space.Compact size="small">
              <Tag color="blue">
                {capitalizeFirstLetter(
                  data.latestPublished.status ?? data.latestDraft.status
                )}
              </Tag>
              <Tag>{data.categoryName}</Tag>
            </Space.Compact>
          }
        >
          <div>
            <Dropdown.Button
              trigger={['click']}
              size="small"
              className={classNames(
                {
                  [styles.colorPrimary]: !isInstalled,
                  [styles.colorDanger]: isInstalled,
                },
                styles.button
              )}
              menu={{
                items: getActionMenuItems(isInstalled),
                onClick: handleMenuItemClick,
              }}
              onClick={(e) => {
                setIsLoading(true)
                e.stopPropagation()
                isInstalled ? handleUninstall() : handleInstall()
              }}
              loading={isLoading}
            >
              {isInstalled ? 'Uninstall' : 'Install'}
            </Dropdown.Button>
          </div>
        </CustomCard.Footer>
      </CustomCard>

      <ConfigurePluginModal
        open={isInstallModalOpen}
        onClose={() => setIsInstallModalOpen(false)}
        formStructure={formStructure}
        onSubmit={handleFormSubmit}
        isLoading={isLoading}
      />
    </>
  )
}

export default memo(PluginCard)
