import { App, Empty, Skeleton } from 'antd'
import {
  v1PluginInstalledAddCreate,
  v1PluginInstalledRemoveCreate,
} from 'api/Api'
import CustomPagination from 'components/custom-pagination'
import type { FormItemType } from 'components/new-dynamic-form/types'
import { DEFAULT_PAGE, DEFAULT_PAGE_SIZE } from 'constants/pagination'
import useDebounceValue from 'hooks/use-debounce-value'
import { useCallback, useState } from 'react'
import { getMessageFromError } from 'utils/common'
import invariant from 'utils/invariant'
import CardListLayout from 'views/layouts/card-list'
import { useFilterUrlState } from '../../hooks/use-filter-state'
import { usePluginMarketplaceConfigFormDetail } from '../../hooks/use-plugin-marketplace-config-form-detail'
import usePluginMarketPlaceList from '../../hooks/use-plugin-marketplace-list'
import { ConfigurePluginModal } from '../configure-plugin-modal'
import { MarketplacePluginCard } from '../marketplace-plugin-card'

export function MarketplaceTab() {
  const { message, modal } = App.useApp()

  const [currentPage, setCurrentPage] = useState(DEFAULT_PAGE)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)

  const [isInstallModalOpen, setIsInstallModalOpen] = useState(false)
  const [openedPluginId, setOpenedPluginId] = useState<number | null>(null)
  const [{ search, sort, filter }] = useFilterUrlState()

  const debouncedSearch = useDebounceValue(search, 500)

  function handleInstallModalOpen(pluginId: number) {
    setOpenedPluginId(pluginId)
    setIsInstallModalOpen(true)
  }

  const { fetchData: fetchPluginFormDetail } =
    usePluginMarketplaceConfigFormDetail()

  const {
    result: pluginListResult,
    isLoading,
    fetchPlugins,
  } = usePluginMarketPlaceList({
    params: {
      limit: pageSize,
      page: currentPage,
      search_plugin_name: debouncedSearch,
      sort: JSON.stringify(sort),
      category_ids: filter.category_ids
        ? filter.category_ids.join(',')
        : undefined,
    },
  })

  const [formStructure, setFormStructure] = useState<FormItemType | null>(null)

  async function handleInstallSubmit(values: any) {
    invariant(openedPluginId, 'Plugin ID is required')
    try {
      await v1PluginInstalledAddCreate(
        openedPluginId,
        {
          is_marketplace: true,
        },
        { inputs: values }
      )
      message.success(`Plugin installed successfully`)
      fetchPlugins()
      setIsInstallModalOpen(false)
    } catch (error) {
      message.error(`Failed to install plugin: ${getMessageFromError(error)}`)
    }
  }

  function handleInstallClick(pluginId: number) {
    fetchPluginFormDetail({
      params: pluginId,
      onSuccess: (formData) => {
        // Cast to unknown first to avoid type errors
        setFormStructure(formData.configurationForm as unknown as FormItemType)
        handleInstallModalOpen(pluginId)
      },
      onError: (error) => {
        message.error(`Failed to load plugin configuration: ${error.message}`)
      },
    })
  }

  function handleUninstallClick(pluginId: number) {
    modal.confirm({
      title: 'Uninstall Plugin',
      okText: 'Uninstall',
      cancelText: 'Cancel',
      content: 'Are you sure you want to uninstall this plugin?',
      onOk: async () => {
        try {
          await v1PluginInstalledRemoveCreate(pluginId)
          message.success('Plugin uninstalled successfully')
          fetchPlugins()
        } catch (error) {
          message.error('Failed to uninstall plugin')
        }
      },
    })
  }

  const handlePageChange = useCallback(
    (page: number, size: number) => {
      // Save the previous page and size
      const prevPage = currentPage
      const prevSize = pageSize

      // Update the local state immediately for UI feedback
      setCurrentPage(page)
      setPageSize(size)

      fetchPlugins({
        onError: () => {
          message.error(`Failed to load page ${page}`)
          // Revert to the previous page and size on error, setting the state would automatically trigger the refetch
          setCurrentPage(prevPage)
          setPageSize(prevSize)
        },
      })
    },
    [fetchPlugins, message, currentPage, pageSize]
  )

  if (isLoading) {
    return (
      <CardListLayout>
        <Skeleton active />
      </CardListLayout>
    )
  }

  if (!pluginListResult.success) {
    return (
      <CardListLayout>
        <Empty
          description={getMessageFromError(pluginListResult.error)}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </CardListLayout>
    )
  }

  if (!pluginListResult.data.data.length) {
    return (
      <CardListLayout>
        <Empty description="No plugins found" style={{ width: '100%' }} />
      </CardListLayout>
    )
  }

  const { data: plugins, pagination } = pluginListResult.data

  return (
    <>
      <CardListLayout
        style={{
          height: 'calc(100vh - 230px)',
        }}
      >
        {plugins.length === 0 ? (
          <Empty description="No plugins found" style={{ width: '100%' }} />
        ) : (
          plugins.map((plugin) => (
            <MarketplacePluginCard
              key={plugin.id}
              plugin={plugin}
              isLoading={isInstallModalOpen}
              onInstall={handleInstallClick}
              onUninstall={handleUninstallClick}
            />
          ))
        )}
      </CardListLayout>
      <div className="pagination">
        <CustomPagination
          total={pagination.totalItems}
          current={currentPage}
          pageSize={pageSize}
          align="center"
          onChange={handlePageChange}
          showSizeChanger
          showTotal={(total) => `Total ${total} items`}
          disabled={isLoading}
        />
      </div>

      <ConfigurePluginModal
        open={isInstallModalOpen}
        onClose={() => setIsInstallModalOpen(false)}
        formStructure={formStructure}
        onSubmit={handleInstallSubmit}
      />
    </>
  )
}

export default MarketplaceTab
