import type { SortOption } from 'views/components/filter-bar/types'

export const sortOptions = [
  {
    key: 'name',
    ascLabel: 'Name A-Z',
    descLabel: 'Name Z-A',
  },
  {
    key: 'category',
    ascLabel: 'Category A-Z',
    descLabel: 'Category Z-A',
  },
  {
    key: 'published_at',
    ascLabel: 'Published Date closest to now',
    descLabel: 'Published Date farthest from now',
  },
  {
    key: 'installed_at',
    ascLabel: 'Installed Date closest to now',
    descLabel: 'Installed Date farthest from now',
  },
] satisfies SortOption[]
