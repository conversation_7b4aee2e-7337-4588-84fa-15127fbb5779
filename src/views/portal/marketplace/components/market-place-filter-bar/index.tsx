import FilterBar from 'views/components/filter-bar'
import { useFilterUrlState } from '../../hooks/use-filter-state'
import { sortOptions } from './config'
import { useFilterOptions } from './use-filter-options'

export function MarketplaceFilterBar() {
  const [filterState, setFilterState] = useFilterUrlState()
  const filterOptions = useFilterOptions()

  return (
    <FilterBar
      onChange={setFilterState}
      value={filterState}
      sortOptions={sortOptions}
      filterOptions={filterOptions}
    />
  )
}

export default MarketplaceFilterBar
