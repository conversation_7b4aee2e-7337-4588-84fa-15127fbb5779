import { Modal, type FormInstance } from 'antd'
import { FormItemRender } from 'components/new-dynamic-form'
import { FormContext } from 'components/new-dynamic-form/context'
import type { FormItemType } from 'components/new-dynamic-form/types'
import { useEffect, useMemo, useRef } from 'react'

interface ConfigurePluginModal {
  open: boolean
  onClose: () => void
  formStructure: FormItemType | null
  formValue?: any
  isLoading?: boolean
  onSubmit: (values: any) => void
}

export function ConfigurePluginModal({
  open,
  onClose,
  formStructure,
  formValue,
  onSubmit,
  isLoading,
}: ConfigurePluginModal) {
  const formRef = useRef<{ form: FormInstance }>()

  useEffect(() => {
    if (formValue && open && formStructure) {
      formRef.current?.form.setFieldsValue(formValue)
    }
  }, [formValue, open, formStructure])

  const handleModalCancel = () => {
    formRef.current?.form.resetFields()
    if (formValue && formStructure) {
      formRef.current?.form.setFieldsValue(formValue)
    }
    onClose?.()
  }

  const handleSubmit = async () => {
    try {
      // const values = formRef.current?.form.validateFields() or formRef.current?.form.getFieldsValue() returns empty for slider
      await formRef.current?.form.validateFields()
      const values = formRef.current?.form.getFieldsValue(true)
      onSubmit(values)
    } catch (e) {
      console.error('Validation failed:', e)
    }
  }

  const contextValue = useMemo(() => {
    // Create a default empty structure if formStructure is null
    const safeRoot = formStructure || {
      path: [],
      formItemId: 'default-root',
      type: 'root',
      children: [],
      props: { formItemConfig: { name: 'root', required: true } },
    }

    return {
      root: safeRoot,
      setRoot: () => {},
      onItemClick: () => {},
    }
  }, [formStructure])

  return open ? (
    <Modal
      title="Configuration"
      open={open}
      confirmLoading={isLoading}
      onCancel={handleModalCancel}
      cancelButtonProps={{ style: { display: 'none' } }}
      okText="Submit"
      onOk={handleSubmit}
    >
      <FormContext.Provider value={contextValue}>
        <FormItemRender
          data={formStructure?.children ?? []}
          ref={formRef}
          canEdit={false}
        />
      </FormContext.Provider>
    </Modal>
  ) : null
}
