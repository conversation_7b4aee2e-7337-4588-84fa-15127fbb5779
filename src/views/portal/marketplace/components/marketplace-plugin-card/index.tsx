import { CloudDownloadOutlined, DeleteOutlined } from '@ant-design/icons'
import { Avatar, Button } from 'antd'
import type { ResponsesGetPluginMarketplaceWithLatestPluginFormResponse } from 'api/data-contracts'
import PluginColor from 'assets/images/plugin-color.svg'
import CustomCard from 'components/custom-card'
import { formatDate } from 'utils/filter'

interface MarketplacePluginCardProps {
  plugin: ResponsesGetPluginMarketplaceWithLatestPluginFormResponse
  onInstall: (pluginId: number) => void
  onUninstall?: (pluginId: number) => void
  isLoading?: boolean
}

export const MarketplacePluginCard = ({
  plugin,
  onInstall,
  onUninstall,
  isLoading = false,
}: MarketplacePluginCardProps) => {
  function handleActionButtonClick() {
    if (plugin.pluginInstalled) {
      onUninstall?.(plugin.id)
    } else {
      onInstall(plugin.id)
    }
  }
  return (
    <CustomCard key={plugin.id}>
      <CustomCard.Header
        title={plugin.name}
        subTitle={`Published at ${formatDate(plugin.latestPublishedAt)}`}
        icon={<Avatar icon={<PluginColor fontSize={36} />} size={54} />}
      />
      <CustomCard.Content>{plugin.description}</CustomCard.Content>
      <CustomCard.Footer>
        <Button
          type={plugin.pluginInstalled ? 'default' : 'primary'}
          style={{ width: '100%' }}
          icon={
            plugin.pluginInstalled ? (
              <DeleteOutlined />
            ) : (
              <CloudDownloadOutlined />
            )
          }
          loading={isLoading}
          onClick={() => handleActionButtonClick()}
        >
          {plugin.pluginInstalled ? 'Uninstall' : 'Install'}
        </Button>
      </CustomCard.Footer>
    </CustomCard>
  )
}

export default MarketplacePluginCard
