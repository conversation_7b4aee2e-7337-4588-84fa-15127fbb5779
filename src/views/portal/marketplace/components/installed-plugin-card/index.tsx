import { App, Avatar, Tag } from 'antd'
import {
  v1PluginInstalledRemoveCreate,
  v1PluginInstalledUpdateCreate,
} from 'api/Api'
import type { ResponsesGetPluginMarketplaceWithLatestPluginFormResponse } from 'api/data-contracts'
import CustomCard from 'components/custom-card'
import { formatDate } from 'utils/filter'
import CustomAvatar from 'components/custom-avatar'
import PluginColor from 'assets/images/plugin-color.svg'

interface InstalledPluginCardProps {
  plugin: ResponsesGetPluginMarketplaceWithLatestPluginFormResponse
  onUninstall: () => void
  onConfigure: (pluginId: number) => void
  onUpdate: () => void
}

export const InstalledPluginCard = ({
  plugin,
  onUninstall,
  onConfigure,
  onUpdate,
}: InstalledPluginCardProps) => {
  const { message, modal } = App.useApp()

  const handleMenuClick = async ({ key }: { key: string }) => {
    if (key === 'configure') {
      onConfigure(plugin.id)
    }
    if (key === 'update') {
      modal.confirm({
        title: 'Update Plugin',
        okText: 'Update',
        cancelText: 'Cancel',
        content: 'Are you sure you want to update this plugin?',
        onOk: async () => {
          try {
            await v1PluginInstalledUpdateCreate(plugin.id)
            onUpdate()
            message.success('Plugin updated successfully')
          } catch (error) {
            message.error('Failed to update plugin')
          }
        },
      })
    }
    if (key === 'uninstall') {
      modal.confirm({
        title: 'Uninstall Plugin',
        okText: 'Uninstall',
        cancelText: 'Cancel',
        content: 'Are you sure you want to uninstall this plugin?',
        onOk: async () => {
          try {
            await v1PluginInstalledRemoveCreate(plugin.id)
            message.success('Plugin uninstalled successfully')
            onUninstall()
          } catch (error) {
            message.error('Failed to uninstall plugin')
          }
        },
      })
    }
  }

  return (
    <CustomCard key={plugin.id}>
      <CustomCard.Header
        title={plugin.name}
        subTitle={`Installed at ${formatDate(
          plugin.pluginInstalled.installedAt
        )}`}
        icon={
          <CustomAvatar
            iconUUID={plugin.iconUUID}
            shape="square"
            size={54}
            defaultIcon={<PluginColor fontSize={36} />}
          />
        }
      />
      <CustomCard.Content>{plugin.description}</CustomCard.Content>
      <CustomCard.Footer
        tags={
          plugin.pluginInstalled.updateAvailable ? (
            <Tag color="success">Update available</Tag>
          ) : null
        }
        handleMenuClick={handleMenuClick}
        dropdownItems={[
          {
            key: 'configure',
            label: 'Configure',
          },
          {
            key: 'uninstall',
            label: 'Uninstall',
          },
          {
            key: 'update',
            label: 'Update',
            disabled: !plugin.pluginInstalled.updateAvailable,
          },
        ]}
      />
    </CustomCard>
  )
}

export default InstalledPluginCard
