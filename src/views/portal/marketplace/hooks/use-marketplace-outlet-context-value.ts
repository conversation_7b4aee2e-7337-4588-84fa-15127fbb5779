// Define the data type for the Marketplace context

import type { OutletContext } from 'hooks/use-outlet-context-value'
import useOutletContextValue from 'hooks/use-outlet-context-value'

type MarketplaceData = null
export type MarketplaceOutletContext = OutletContext<MarketplaceData>

const useMarketplaceOutletContextValue = (): MarketplaceOutletContext => {
  return useOutletContextValue<MarketplaceData>(null)
}

export default useMarketplaceOutletContextValue
