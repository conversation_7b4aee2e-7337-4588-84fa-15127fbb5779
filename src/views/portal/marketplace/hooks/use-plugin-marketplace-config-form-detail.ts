import { v1PluginMarketplaceConfigurationFormDetail } from 'api/Api'
import {
  PluginFormSelectorType,
  ResponsesGetPluginMarketplaceConfigurationFormResponse,
} from 'api/data-contracts'
import { useCallback, useEffect, useRef, useState } from 'react'
import type { Result } from 'types/utils'
import invariant from 'utils/invariant'
import type { FetchOptions } from './types'

// Default empty state for the configuration form response
const EMPTY_CONFIG_FORM: ResponsesGetPluginMarketplaceConfigurationFormResponse =
  {
    configurationForm: {
      formItemId: '',
      path: [],
      type: PluginFormSelectorType.FormTypeRoot,
      children: [],
      props: {
        formItemConfig: {
          name: 'root',
          required: false,
        },
      },
    },
  }

interface UsePluginMarketplaceConfigFormDetailReturn {
  result: Result<ResponsesGetPluginMarketplaceConfigurationFormResponse>
  isLoading: boolean
  fetchData: (
    options?: FetchOptions<
      number,
      ResponsesGetPluginMarketplaceConfigurationFormResponse
    >
  ) => Promise<void>
}

export function usePluginMarketplaceConfigFormDetail(): UsePluginMarketplaceConfigFormDetailReturn {
  const abortControllerRef = useRef<AbortController | null>(null)
  const [result, setResult] = useState<
    Result<ResponsesGetPluginMarketplaceConfigurationFormResponse>
  >({
    success: true,
    data: EMPTY_CONFIG_FORM,
  })

  const [isLoading, setIsLoading] = useState(false)

  const fetchData = useCallback(
    async (
      options?: FetchOptions<
        number,
        ResponsesGetPluginMarketplaceConfigurationFormResponse
      >
    ) => {
      invariant(options?.params, 'Plugin ID is required')

      // If there is a previous request when fetchData is called, cancel the previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      const currentController = new AbortController()
      abortControllerRef.current = currentController

      try {
        setIsLoading(true)

        const response = await v1PluginMarketplaceConfigurationFormDetail(
          options.params,
          {
            signal: currentController.signal,
          }
        )

        if (currentController === abortControllerRef.current) {
          setResult({
            success: true,
            data: response.data,
          })
          options?.onSuccess?.(response.data)
        }
      } catch (err) {
        if (
          currentController === abortControllerRef.current &&
          !currentController.signal.aborted
        ) {
          setResult({
            success: false,
            error: err as Error,
          })
          options?.onError?.(err as Error)
        }
      } finally {
        if (currentController === abortControllerRef.current) {
          setIsLoading(false)
        }
      }
    },
    []
  )

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  return {
    result,
    isLoading,
    fetchData,
  }
}
