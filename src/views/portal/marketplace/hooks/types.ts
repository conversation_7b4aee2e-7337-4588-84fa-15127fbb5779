import type { WithPaginationRequest } from 'types/common'
import type { StringifiedArray } from 'types/utils'

/**
 * Common interface for fetch options in plugin hooks
 * Used for both individual fetch operations and hook initialization
 */
export interface FetchOptions<T, R = any> {
  params?: T
  onSuccess?: (result: R) => void
  onError?: (error: Error) => void
}

/**
 * Common type for plugin list parameters
 */
export type PluginListParams = WithPaginationRequest<{
  /** Filter by category ID(s), comma-separated for multiple values */
  category_ids?: StringifiedArray
  /** Filter by organization ID(s), comma-separated for multiple values */
  organization_ids?: StringifiedArray
  /** Search by plugin name */
  search_plugin_name?: StringifiedArray
}>
