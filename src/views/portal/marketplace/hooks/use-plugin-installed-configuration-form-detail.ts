import { v1PluginInstalledConfigurationFormDetail } from 'api/Api'
import {
  PluginFormSelectorType,
  ResponsesGetPluginInstalledConfigurationFormResponse,
} from 'api/data-contracts'
import { useCallback, useEffect, useRef, useState } from 'react'
import type { Result } from 'types/utils'
import invariant from 'utils/invariant'
import type { FetchOptions } from './types'

// Default empty state for the configuration form response
const EMPTY_CONFIG_FORM: ResponsesGetPluginInstalledConfigurationFormResponse =
  {
    configurationForm: {
      formItemId: '',
      path: [],
      type: PluginFormSelectorType.FormTypeRoot,
      children: [],
      props: {
        formItemConfig: {
          name: 'root',
          required: false,
        },
      },
    },
    configurationFormInputs: {},
  }

// Parameters for the installed plugin configuration form API
interface InstalledConfigFormParams {
  pluginId: number
}

interface UsePluginInstalledConfigFormDetailReturn {
  result: Result<ResponsesGetPluginInstalledConfigurationFormResponse>
  isLoading: boolean
  fetchData: (
    options?: FetchOptions<
      InstalledConfigFormParams,
      ResponsesGetPluginInstalledConfigurationFormResponse
    >
  ) => Promise<void>
}

export function usePluginInstalledConfigFormDetail(): UsePluginInstalledConfigFormDetailReturn {
  const abortControllerRef = useRef<AbortController | null>(null)
  const [result, setResult] = useState<
    Result<ResponsesGetPluginInstalledConfigurationFormResponse>
  >({
    success: true,
    data: EMPTY_CONFIG_FORM,
  })

  const [isLoading, setIsLoading] = useState(false)

  const fetchData = useCallback(
    async (
      options?: FetchOptions<
        InstalledConfigFormParams,
        ResponsesGetPluginInstalledConfigurationFormResponse
      >
    ) => {
      invariant(options?.params, 'Plugin parameters are required')
      const { pluginId } = options.params

      // If there is a previous request when fetchData is called, cancel the previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      const currentController = new AbortController()
      abortControllerRef.current = currentController

      try {
        setIsLoading(true)

        const response = await v1PluginInstalledConfigurationFormDetail(
          pluginId,
          {
            signal: currentController.signal,
          }
        )

        if (currentController === abortControllerRef.current) {
          setResult({
            success: true,
            data: response.data,
          })
          options?.onSuccess?.(response.data)
        }
      } catch (err) {
        if (
          currentController === abortControllerRef.current &&
          !currentController.signal.aborted
        ) {
          setResult({
            success: false,
            error: err as Error,
          })
          options?.onError?.(err as Error)
        }
      } finally {
        if (currentController === abortControllerRef.current) {
          setIsLoading(false)
        }
      }
    },
    []
  )

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  return {
    result,
    isLoading,
    fetchData,
  }
}
