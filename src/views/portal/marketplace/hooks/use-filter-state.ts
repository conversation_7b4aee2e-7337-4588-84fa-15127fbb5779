import * as z from '@zod/mini'
import { parseAsJson, parseAsString, useQueryStates } from 'nuqs'

const sortTypeSchema = z.object({
  name: z.optional(z.enum(['asc', 'desc'])),
  category: z.optional(z.enum(['asc', 'desc'])),
  published_at: z.optional(z.enum(['asc', 'desc'])),
  installed_at: z.optional(z.enum(['asc', 'desc'])),
})

const filterTypeSchema = z.object({
  organization_ids: z.optional(z.array(z.string())),
  category_ids: z.optional(z.array(z.string())),
})

/**
 *
 * All query states bound to the same key will be synchronized across components.
 * @example // to use it in another component:
 * const [{ search, sort }, setFilterState] = useFilterUrlState()
 */
export function useFilterUrlState() {
  const [state, setFilterState] = useQueryStates({
    search: parseAsString.withDefault(''),
    sort: parseAs<PERSON>son(sortTypeSchema.parse).withDefault({}),
    filter: parseAs<PERSON>son(filterTypeSchema.parse).withDefault({}),
  })

  return [state, setFilterState] as const
}
