import PageMainLayout from 'layouts/portal/page-main-layout'
import { Outlet } from 'react-router-dom'
import useMarketplaceOutletContextValue from './hooks/use-marketplace-outlet-context-value'

export function MarketplaceLayout() {
  const context = useMarketplaceOutletContextValue()
  const { routeStates } = context

  return (
    <PageMainLayout
      title="Marketplace"
      caption={routeStates.caption}
      actions={routeStates.actionItems}
      noBack
      noHeaderBottomLine
    >
      <Outlet context={context} />
    </PageMainLayout>
  )
}
