import { AgentTypeAgentType, PluginPluginType } from 'api/data-contracts'

export interface MockPlugin {
  id: number
  uuid: string
  name: string
  description: string
  type: PluginPluginType
  endpoint: string
  category: {
    id: number
    name: string
    description: string
    iconID: number
    icon: {
      id: number
      url: string
    }
  }
  icon: {
    id: number
    url: string
  }
  organizationID: number
  createdAt: string
  updatedAt: string
  downloads: number
  rating: number
  tags: string[]
}

export const mockData: MockPlugin[] = [
  {
    id: 1,
    uuid: '550e8400-e29b-41d4-a716-************',
    name: 'GPT-4 Assistant',
    description:
      'Powerful AI assistant powered by GPT-4 for natural language processing and generation',
    type: PluginPluginType.External,
    endpoint: 'https://api.example.com/gpt4-assistant',
    category: {
      id: 1,
      name: 'AI & Machine Learning',
      description: 'Artificial Intelligence and Machine Learning plugins',
      iconID: 1,
      icon: {
        id: 1,
        url: '/icons/ai-category.svg',
      },
    },
    icon: {
      id: 101,
      url: '/icons/gpt4-assistant.svg',
    },
    organizationID: 1,
    createdAt: '2024-01-15T08:00:00Z',
    updatedAt: '2024-03-20T10:30:00Z',
    downloads: 15234,
    rating: 4.8,
    tags: ['AI', 'NLP', 'Assistant'],
  },
  {
    id: 2,
    uuid: '550e8400-e29b-41d4-a716-************',
    name: 'Data Visualization Pro',
    description:
      'Create beautiful and interactive data visualizations with ease',
    type: PluginPluginType.External,
    endpoint: 'https://api.example.com/data-viz',
    category: {
      id: 2,
      name: 'Data & Analytics',
      description: 'Data processing and visualization tools',
      iconID: 2,
      icon: {
        id: 2,
        url: '/icons/data-category.svg',
      },
    },
    icon: {
      id: 102,
      url: '/icons/data-viz.svg',
    },
    organizationID: 1,
    createdAt: '2024-02-01T09:15:00Z',
    updatedAt: '2024-03-19T14:20:00Z',
    downloads: 8756,
    rating: 4.6,
    tags: ['Data', 'Visualization', 'Charts'],
  },
  {
    id: 3,
    uuid: '550e8400-e29b-41d4-a716-************',
    name: 'Security Scanner',
    description:
      'Comprehensive security scanning and vulnerability assessment tool',
    type: PluginPluginType.Internal,
    endpoint: 'https://api.example.com/security-scanner',
    category: {
      id: 3,
      name: 'Security & Compliance',
      description: 'Security, authentication, and compliance tools',
      iconID: 3,
      icon: {
        id: 3,
        url: '/icons/security-category.svg',
      },
    },
    icon: {
      id: 103,
      url: '/icons/security-scanner.svg',
    },
    organizationID: 1,
    createdAt: '2024-01-20T11:30:00Z',
    updatedAt: '2024-03-18T16:45:00Z',
    downloads: 12453,
    rating: 4.9,
    tags: ['Security', 'Scanner', 'Compliance'],
  },
  {
    id: 4,
    uuid: '550e8400-e29b-41d4-a716-************',
    name: 'Workflow Automator',
    description: 'Automate repetitive tasks and create custom workflows',
    type: PluginPluginType.External,
    endpoint: 'https://api.example.com/workflow-automator',
    category: {
      id: 4,
      name: 'Automation',
      description: 'Workflow and task automation tools',
      iconID: 4,
      icon: {
        id: 4,
        url: '/icons/automation-category.svg',
      },
    },
    icon: {
      id: 104,
      url: '/icons/workflow-automator.svg',
    },
    organizationID: 1,
    createdAt: '2024-02-15T13:45:00Z',
    updatedAt: '2024-03-17T09:30:00Z',
    downloads: 6789,
    rating: 4.7,
    tags: ['Automation', 'Workflow', 'Productivity'],
  },
]

export const agentMockData = [
  {
    id: 1,
    uuid: '550e8400-e29b-41d4-a716-************',
    name: 'GPT Assistant',
    description:
      'A powerful AI assistant for natural language processing and generation',
    agentType: AgentTypeAgentType.Chatbot,
    iconId: 101,
    iconUuid: 'icon-550e8400-e29b-41d4-a716-************',
    organizationId: 1,
    updatedAt: '2024-03-20T10:30:00Z',
    createdAt: '2024-01-15T08:00:00Z',
  },
  {
    id: 2,
    uuid: '550e8400-e29b-41d4-a716-************',
    name: 'Data Analyzer',
    description: 'Analyze and visualize data with ease',
    agentType: AgentTypeAgentType.SmartAPI,
    iconId: 102,
    iconUuid: 'icon-550e8400-e29b-41d4-a716-************',
    organizationId: 1,
    updatedAt: '2024-03-19T14:20:00Z',
    createdAt: '2024-02-01T09:15:00Z',
  },
  {
    id: 3,
    uuid: '550e8400-e29b-41d4-a716-************',
    name: 'Security Bot',
    description: 'Automated security scanning and monitoring',
    agentType: AgentTypeAgentType.Chatbot,
    iconId: 103,
    iconUuid: 'icon-550e8400-e29b-41d4-a716-************',
    organizationId: 1,
    updatedAt: '2024-03-18T16:45:00Z',
    createdAt: '2024-01-20T11:30:00Z',
  },
]
