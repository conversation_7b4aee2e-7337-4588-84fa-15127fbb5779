export type BaseChatExtraDataType = {
  termsAndConditions: string
  userGuide: string
  headerTitle: string
  subheader: string
  inputPlaceholder: string
  disclaimerText: string
  newChatButtonText: string
  faqButtonText: string
  conversationOpener: string
  openingQuestions: string[]
  maxCharLength: number

  feedbackOperationLike: string
  feedbackOperationCancelLike: string
  feedbackOperationUnlike: string
  feedbackOperationCancelUnlike: string
  feedbackOperationLikeOptions: string
  feedbackOperationUnlikeOptions: string
  feedbackOperationPlaceholder: string
}

export type ChatQueryType = Partial<{
  uuid: string
  agentID: string
}>
