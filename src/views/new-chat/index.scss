.chat {
  &-page-wrapper {
    height: 100vh;
    width: 100vw;
    display: flex;
    align-items: center;
    justify-content: center;
    background-repeat: no-repeat;
    background-size: cover;

    transform: all 1s;
  }

  &-loading {
    max-height: unset !important;
    height: 100vh !important;
    background-color: white;
  }

  &-wrapper {
    display: flex;
    flex-flow: column nowrap;
    width: 100%;
    height: 100vh;
    padding: 40px;
    box-sizing: border-box;
    max-width: 1440px;
  }

  &-box {
    &-wrapper {
      flex: 1;

      padding-top: 12px;
      margin-bottom: 12px;
      overflow-x: hidden;
      overflow-y: auto;
      word-break: break-word;
    }
  }

  &-input-area-wrapper {
    padding-top: 12px;
    margin-bottom: 12px;
  }
  &-input-area-exceed-limited {
    border: 1px solid #ff4d4f !important;
  }

  &-footer {
    padding-top: 12px;
    height: 32px;
    display: inline-grid;
    justify-content: center;
    justify-items: center;
  }

  // Status Change
  &-started-conversion {
    animation: fadeOutAndCollapse 1s forwards;
    overflow: hidden;
  }
}

@keyframes fadeOutAndCollapse {
  0% {
    opacity: 1;
    max-height: 50px;
    padding: 20px 0;
  }
  50% {
    opacity: 0;
    max-height: 50px;
    padding: 20px 0;
  }
  100% {
    opacity: 0;
    max-height: 0;
    padding: 0;
    display: none;
  }
}
