.navbar {
  &-wrapper {
    box-sizing: border-box;
    min-height: 48px;
    height: 48px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 0 8px;
    column-gap: 16px;
  }

  &-icon {
    font-size: 24px;
    border-radius: 50%;

    &:hover {
      background-color: rgba(0, 0, 0, 0.06);
    }
  }

  &-new-chat {
    border-radius: 50px;
    font-size: 14px;

    &-text {
    }

    &-hide {
      display: none;
    }

    &-icon {
      cursor: pointer;
      display: inline-block;
      background-image: url('/assets/images/file1.svg');
      background-repeat: no-repeat;
      width: 20px;
      height: 20px;
    }
  }
}
