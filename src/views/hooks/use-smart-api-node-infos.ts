import { v1SmartApiRequestsWorkflowNodeInfosDetail } from 'api/Api'
import { type GenieCoreAppHandlersSmartApiResponsesNodeInfoResponse } from 'api/data-contracts'
import { useEffect, useState } from 'react'

interface UseNodeInfosResult {
  loading: boolean
  nodeInfos?: GenieCoreAppHandlersSmartApiResponsesNodeInfoResponse[]
}

export const useSmartApiNodeInfos = (msgId?: string): UseNodeInfosResult => {
  const [loading, setLoading] = useState(false)
  const [nodeInfos, setNodeInfos] =
    useState<GenieCoreAppHandlersSmartApiResponsesNodeInfoResponse[]>()

  useEffect(() => {
    if (!msgId) {
      return
    }

    let ignore = false
    setNodeInfos(undefined)
    setLoading(true)

    const fetchNodeInfos = async () => {
      try {
        const response = await v1SmartApiRequestsWorkflowNodeInfosDetail(msgId)
        if (!ignore) {
          setNodeInfos(response.data)
        }
      } catch (error) {
        if (!ignore) {
          console.error('Failed to fetch node infos:', error)
        }
      } finally {
        if (!ignore) {
          setLoading(false)
        }
      }
    }

    fetchNodeInfos()

    return () => {
      ignore = true
    }
  }, [msgId])

  return {
    loading,
    nodeInfos,
  }
}
