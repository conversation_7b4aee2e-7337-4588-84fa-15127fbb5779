import { v1ChatConversationsWorkflowNodeInfosDetail } from 'api/Api'
import { type GenieCoreAppHandlersChatResponsesNodeInfoResponse } from 'api/data-contracts'
import { useEffect, useState } from 'react'

interface UseNodeInfosResult {
  loading: boolean
  nodeInfos?: GenieCoreAppHandlersChatResponsesNodeInfoResponse[]
}

export const useChatNodeInfos = (msgId?: string): UseNodeInfosResult => {
  const [loading, setLoading] = useState(false)
  const [nodeInfos, setNodeInfos] =
    useState<GenieCoreAppHandlersChatResponsesNodeInfoResponse[]>()

  useEffect(() => {
    if (!msgId) {
      return
    }

    const fetchNodeInfos = async () => {
      try {
        setLoading(true)
        const response = await v1ChatConversationsWorkflowNodeInfosDetail(msgId)
        setNodeInfos(response.data)
      } catch (error) {
        console.error('Failed to fetch node infos:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchNodeInfos()
  }, [msgId])

  return {
    loading,
    nodeInfos,
  }
}
