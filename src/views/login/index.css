.login-wrapper {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--genie-login-page-bg);
}

.login-box {
  min-height: 540px;
  border-radius: 30px;
  padding: 13vh 0 0;
}

.login-box .login-form {
  height: 540px;
  border-radius: 30px;
  position: relative;
  text-align: center;
}

.login-box .login-form .logo {
  width: 120px;
  padding: 20px;
}

.login-box .login-form .logo img {
  width: 120px;
}

.login-box .login-form .fields {
  width: 360px;
  margin: auto;
  padding: 20px;
}

/* Setup 2FA specific styles */
.login-box .login-form .fields.setup-2fa {
  width: 683px;
  margin: 0 auto;
  padding: 40px;
}

.login-box .login-form .fields.setup-2fa .setup-2fa-content {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 20px;
}

.login-box .login-form .fields.setup-2fa .ant-qrcode {
  width: 283px;
  height: 284px;
  border-radius: 9px;
}

.login-box .login-form .fields.setup-2fa .setup-instructions {
  flex: 1;
  text-align: left;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.login-box .login-form .fields.setup-2fa .setup-instructions h3 {
  font-size: 20px;
  font-weight: 700;
  line-height: 30px;
  letter-spacing: -0.02em;
  white-space: nowrap;
  color: #2a2c2f;
  margin: 0;
}

.login-box .login-form .fields.setup-2fa .setup-instructions p {
  font-size: 16px;
  line-height: 24px;
  letter-spacing: -0.02em;
  color: #2a2c2f;
  margin: 0;
}

.login-box .login-form .fields.setup-2fa .ant-input {
  height: 40px;
  border: 1px solid #eaecef;
  border-radius: 8px;
  box-shadow: 0px 1px 2px rgba(112, 126, 155, 0.05);
}

.login-box .login-form .fields.setup-2fa .ant-btn {
  height: 40px;
  font-weight: 700;
  font-size: 16px;
  border-radius: 8px;
  flex: 1;
}

.login-box .login-form .fields .ant-input,
.login-box .login-form .fields .ant-input-password {
  width: 360px;
}

/* Fix password field autofill background */
.login-box .login-form .fields .ant-input-affix-wrapper input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 30px var(--genie-input-autofill-bg) inset !important;
}

.login-box
  .login-form
  .fields
  .ant-input-affix-wrapper:has(input:-webkit-autofill) {
  background-color: var(--genie-input-autofill-bg) !important;
  color: var(--ant-color-text) !important;
  -webkit-text-fill-color: var(--ant-color-text) !important;
}

/* Exception for setup 2FA inputs */
.login-box .login-form .fields.setup-2fa .ant-input,
.login-box .login-form .fields.setup-2fa .ant-input-password {
  width: 100%;
}

.login-box .login-form .ant-form-item {
  margin-bottom: 0;
}

.login-box .login-form .fields .title {
  font-size: 24px;
  font-weight: bolder;
  text-align: center;
  margin: 32px;
}

.login-box .login-form .fields .title p {
  font-size: 12px;
  font-weight: normal;
}

.login-box .login-form .fields .field-group {
  padding-bottom: 18px;
}

/** login button */
.login-box .circle-button-wrapper {
  display: flex;
  justify-content: center;
}

.login-box .circle-button-wrapper .login-btn.ant-btn-circle {
  width: 40px;
  height: 40px;
  background-color: #2a2c2f;
}

.login-box .circle-button-wrapper .login-btn.ant-btn-circle:hover {
  background-color: #3a3c3f;
}

/** error styles */
.login-box .login-form .fields .ant-form-item-explain-error {
  text-align: left;
  font-size: 12px;
}

/* label  styles */
.login-box .login-form .fields .ant-form-item-label {
  padding: 0;
  height: 27px; /* use height instead of padding because it is absolutely positioned */
}

.login-box .login-form .fields .ant-form-item-label > label {
  font-size: 14px;
}

/** two factor auth form item styles*/
.login-box .login-form .fields .two-factor-auth-form-item {
  margin-top: 60px;
  margin-bottom: 6px;
}

.login-box .login-form .fields .two-factor-auth-form-item .ant-form-item-label {
  height: auto;
}

.login-box
  .login-form
  .fields
  .two-factor-auth-form-item
  .ant-form-item-label
  > label {
  font-size: 20px;
  font-weight: 400;
  margin-bottom: 10px;
}

/** button styles */
.login-box .login-form .fields .ant-btn {
  font-size: 16px;
}

.login-box .login-form .fields .qr-code {
  padding: 20px;
  border-radius: 14px;
}
