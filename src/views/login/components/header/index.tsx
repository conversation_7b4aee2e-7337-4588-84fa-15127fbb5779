import BrandLogo from 'assets/images/icon-with-name.svg'
import { GridBackground } from '../grid-bg'
import styles from './index.scss'

interface LoginHeaderProps {
  title?: string
  subtitle?: string
}

export function LoginHeader({ title, subtitle }: LoginHeaderProps) {
  return (
    <header className={styles.loginHeader}>
      <GridBackground />
      <BrandLogo className={styles.logo} />
      {title && <h1 className={styles.title}>{title}</h1>}
      {subtitle && <h2 className={styles.subtitle}>{subtitle}</h2>}
    </header>
  )
}
