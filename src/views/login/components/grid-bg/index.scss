.gridBgContainer {
  $grid-bg-url: '/assets/images/grid-bg.png';
  $grid-size: 800px auto;

  position: absolute;
  height: 540px;
  width: 70vw;
  top: 8%;
  left: 50%;
  transform: translateY(-50%) translateX(-50%);
  background: url($grid-bg-url) center/$grid-size no-repeat;

  &::before {
    content: '';
    inset: 0;
    position: absolute;
    background: radial-gradient(#3278d999, #d9d9d900);
    mask: url($grid-bg-url) center/$grid-size no-repeat;
  }
}
