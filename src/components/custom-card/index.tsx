import React from 'react'
import { Content } from './components/content'
import { Footer } from './components/footer'
import { CardHeader } from './components/header'
import styles from './index.module.scss'
import cls from 'classnames'

interface CustomCardProps {
  children: React.ReactNode
  className?: string
  onClick?: (...args: any[]) => void
}

interface CustomCardComponent extends React.FC<CustomCardProps> {
  Header: typeof CardHeader
  Content: typeof Content
  Footer: typeof Footer
}

const CustomCard: CustomCardComponent = ({ children, className, onClick }) => {
  return (
    <div
      className={cls(styles.customCard, className)}
      onClick={onClick}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          onClick?.(e)
        }
      }}
      aria-label="Card"
    >
      {children}
    </div>
  )
}

CustomCard.Header = CardHeader
CustomCard.Content = Content
CustomCard.Footer = Footer
export default CustomCard
