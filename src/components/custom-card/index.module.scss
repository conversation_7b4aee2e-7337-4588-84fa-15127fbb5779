.customCard {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 16px;
  width: 384px;
  height: 207px;
  border: 1px solid var(--ant-color-border);
  border-radius: 8px;
  text-align: left;
  background-color: transparent;
  margin: 0;
  overflow: visible;
  text-transform: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.customCard:hover {
  box-shadow: 1px 3px 3px 0px var(--ant-color-border);
  cursor: pointer;
  :global(.actionButtonsVisible) {
    opacity: 1;
    transform: scale(1);
    pointer-events: auto;
  }
}
