import { Typography } from 'antd'
import classNames from 'classnames'
import styles from './index.module.scss'

interface CardHeaderProps {
  title: React.ReactNode
  subTitle: React.ReactNode
  icon: React.ReactNode
  className?: string
}

export function CardHeader({
  title,
  subTitle,
  icon,
  className,
}: Readonly<CardHeaderProps>) {
  return (
    <header className={classNames(styles.header, className)}>
      <div className={styles.titleWrapper}>
        <Typography.Title
          level={5}
          ellipsis={{ tooltip: title }}
          className={styles.title}
        >
          {title}
        </Typography.Title>
        <Typography.Paragraph className={styles.subTitle}>
          {subTitle}
        </Typography.Paragraph>
      </div>
      {icon && <figure className={styles.icon}>{icon}</figure>}
    </header>
  )
}
