.header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0;
  width: 100%;
  height: 54px;
  margin-bottom: 16px;
  gap: 16px;

  .titleWrapper {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding: 0;
    gap: 8px;
    width: 186px;
    margin: 0 auto;
  }

  .title {
    padding: 0;
    margin: 0;
    width: 100%;
    height: 24px;
    font-family: 'Helvetica', sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: -0.02em;
    color: var(--ant-color-text);

    &:global(.ant-typography) {
      margin: 0;
    }
  }

  .subTitle {
    width: 100%;
    height: 18px;
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 18px;
    letter-spacing: 0.02em;
    color: var(--ant-color-text-tertiary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .icon {
    width: 54px;
    height: 54px;
    background-color: var(--ant-color-bg-container);
    border-radius: 7px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    position: relative;

    :global(.ant-avatar) {
      background-color: var(--ant-bg-container);
    }
  }
}
