import { Typography } from 'antd'
import styles from './index.module.scss'

import classNames from 'classnames'

interface ContentProps {
  children: React.ReactNode
  className?: string
}

export function Content({ children, className }: Readonly<ContentProps>) {
  return (
    <section className={classNames(styles.content, className)}>
      <Typography.Paragraph ellipsis={{ rows: 3 }}>
        {children}
      </Typography.Paragraph>
    </section>
  )
}
export default Content
