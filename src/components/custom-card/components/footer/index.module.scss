.footer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  width: 100%;
  height: 36px;
}

.footerContent {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
}

.tags {
  display: block;
}

.actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0;
  gap: 8px;
  height: 36px;
  border-radius: 8px;
  margin-left: auto;

  :global(.ant-btn) {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 8px;
    gap: 8px;
    width: 36px;
    height: 36px;
    border-radius: 8px;
  }

  .actionButtons {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    opacity: 0;
    transform: scale(0.9);
    pointer-events: none;
    transition:
      opacity 0.2s ease,
      transform 0.2s ease;
  }

  // Make the EllipsisOutlined icon dots thicker
  .ellipsisButton {
    :global(.anticon-ellipsis) {
      svg {
        width: 1.2em;
        height: 1.2em;

        path {
          stroke-width: 20;
          stroke: currentColor;
        }
      }
    }
  }
}
