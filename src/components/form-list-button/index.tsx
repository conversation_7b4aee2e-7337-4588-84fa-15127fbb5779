import { Button, ButtonProps } from 'antd'
import classNames from 'classnames'
import React from 'react'
import styles from './index.module.scss'

interface FormListButtonProps extends Omit<ButtonProps, 'children'> {
  /** The text to display in the button */
  text: string
  /** Optional icon component to display before the text */
  icon?: React.ReactNode
}

const FormListButton: React.FC<FormListButtonProps> = ({
  text,
  icon,
  className,
  ...buttonProps
}) => {
  return (
    <Button
      className={classNames(styles.addButton, className)}
      variant="dashed"
      color="default"
      {...buttonProps}
    >
      {icon}
      {text}
    </Button>
  )
}

export default FormListButton
