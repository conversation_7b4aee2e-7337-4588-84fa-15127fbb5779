.dynamic-form-item-render {
  &-form-wrapper {
    height: 100%;
    display: flex;
    flex-flow: column;

    > div:first-child {
      border-top: unset;
    }
  }

  &-wrapper {
    background-color: var(--ant-layout-body-bg);
    border-radius: 8px;
    overflow: auto;
  }

  &-header {
    &-wrapper {
      display: flex;
      flex-flow: column nowrap;
      padding: 8px 12px;

      :global {
        .ant-form-item {
          margin-block-end: 0;
        }
      }
    }

    &-title {
      display: flex;
      flex-flow: row nowrap;
      margin-block-end: 8px;

      &-avatar {
        color: black;

        &-wrapper {
          background-color: #eaecef;
        }
      }
    }
  }

  &-empty {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    font-weight: 500;
    font-size: 14px;
    color: var(--ant-color-text-secondary);
  }
}
