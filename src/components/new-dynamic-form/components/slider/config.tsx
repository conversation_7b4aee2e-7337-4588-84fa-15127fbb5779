import { ConfigSectionType } from 'components/new-dynamic-form/types'
import { getCommonConfig } from '../constants'
import { PluginPluginDataType } from 'api/data-contracts'
import { ALL_DATA_TYPE } from 'constants/common'

const ItemSliderConfig: ConfigSectionType[] = [
  ...getCommonConfig([
    {
      sectionLabel: 'generalConfig',
      items: [
        {
          itemLabel: 'DataType',
          itemName: 'dataType',
          type: 'select',
          options: Object.values(ALL_DATA_TYPE).map((i) => ({
            label: i,
            value: i,
          })),
          defaultValue: PluginPluginDataType.Number,
          disabled: true,
        },
      ],
    },
  ]),
  {
    sectionLabel: 'sliderConfig',
    items: [
      {
        itemLabel: 'DefaultValue',
        itemName: 'defaultValue',
        type: 'inputNumber',
      },
      { itemLabel: 'Max', itemName: 'max', type: 'inputNumber' },
      { itemLabel: 'Min', itemName: 'min', type: 'inputNumber' },
      { itemLabel: 'Step', itemName: 'step', type: 'inputNumber' },
    ],
  },
]

export default ItemSliderConfig
