import { memo, useMemo } from 'react'
import { Switch, SwitchProps } from 'antd'
import { FormItemType } from 'components/new-dynamic-form/types'
import ComponentWrapper from '../lib/component-wrapper'
import { omitBy } from 'lodash-es'
import { getCommonDefaultValue } from '../constants'
import ItemSwitchConfig from './config'

type ItemSwitchProps = { switchConfig?: SwitchProps } & Record<string, any>

const ItemSwitch = (props: ItemSwitchProps) => {
  const { switchConfig } = props

  const filterProps = useMemo(
    () => omitBy(props, (_, k) => k !== 'onChange' && /[A-Z]/.test(k)),
    [props]
  )

  return <Switch {...filterProps} {...switchConfig} />
}

export default ComponentWrapper(memo(ItemSwitch))
export const getDefaultValue = () => {
  const defaultValDataType = ItemSwitchConfig.find(
    (c) => c.sectionLabel === 'generalConfig'
  )?.items.find((i) => i.itemName === 'dataType')?.defaultValue

  const defaultValSwitchValue = ItemSwitchConfig.find(
    (c) => c.sectionLabel === 'switchConfig'
  )?.items.find((i) => i.itemName === 'defaultValue')?.defaultValue

  return getCommonDefaultValue('switch', {
    props: {
      generalConfig: { dataType: defaultValDataType },
      switchConfig: { defaultValue: defaultValSwitchValue },
    },
  })
}
