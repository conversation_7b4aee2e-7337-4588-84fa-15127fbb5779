import { ConfigSectionType } from 'components/new-dynamic-form/types'
import { getCommonConfig } from '../constants'
import { PluginPluginDataType } from 'api/data-contracts'
import { ALL_DATA_TYPE } from 'constants/common'

const ItemConditionConfig: ConfigSectionType[] = [
  ...getCommonConfig([
    {
      sectionLabel: 'generalConfig',
      items: [
        {
          itemLabel: 'DataType',
          itemName: 'dataType',
          type: 'select',
          options: Object.values(ALL_DATA_TYPE).map((i) => ({
            label: i,
            value: i,
          })),
          defaultValue: PluginPluginDataType.String,
          disabled: true,
        },
      ],
    },
  ]),
  {
    sectionLabel: 'conditionConfig',
    items: [
      { itemLabel: 'Have Else', itemName: 'hasElse', type: 'switch' },
      {
        itemLabel: 'Max',
        itemName: 'max',
        type: 'inputNumber',
        min: 0,
      },
    ],
  },
]

export default ItemConditionConfig
