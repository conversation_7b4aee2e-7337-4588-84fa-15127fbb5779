import { memo, useEffect, useMemo, useState } from 'react'
import { merge, omitBy } from 'lodash-es'
import { Select, Space, TreeSelect } from 'antd'
import { getCommonDefaultValue } from '../constants'

import ItemValuePickerConfig from './config'
import ComponentWrapper from '../lib/component-wrapper'
import { ALL_DATA_TYPE } from 'constants/common'

import styles from './index.scss'
import { PluginPluginDataType } from 'api/data-contracts'
import { ValueInput } from 'views/components/value-input'
import { useFormContext } from 'components/new-dynamic-form/context'
import { handleFilterTypeTreeData } from 'components/new-dynamic-form/util'

type ValuePickerValueType = {
  dataType: PluginPluginDataType
  type: 'input' | 'reference'
  value?: any
}

type ItemValuePickerProps = {
  valuePickerConfig?: { showDataType: boolean }

  onChange?: (val: ValuePickerValueType) => void
  value?: ValuePickerValueType
  id?: string
}

const defaultValue: ValuePickerValueType = {
  dataType: PluginPluginDataType.String,
  type: 'input',
  value: '',
}

const ItemValuePicker = (props: ItemValuePickerProps) => {
  const { valuePickerConfig, value, onChange } = props
  const [curValue, setCurValue] = useState(value ?? defaultValue)
  const { referenceTreeData = [] } = useFormContext() ?? {}

  const filterProps = useMemo(
    () => omitBy(props, (_, k) => k !== 'onChange' && /[A-Z]/.test(k)),
    [props]
  )

  const handleChange = (type: keyof ValuePickerValueType, val: string) => {
    if (type !== 'value') {
      setCurValue({
        ...merge(curValue, { [type]: val }),
        value: undefined,
      })
      onChange?.({
        ...merge(curValue, { [type]: val }),
        value: undefined,
      })
    } else {
      setCurValue(merge({}, curValue, { [type]: val }))
      onChange?.(merge({}, curValue, { [type]: val }))
    }
  }

  useEffect(() => {
    if (value) setCurValue(value)
  }, [value])

  return (
    <Space.Compact block className={styles.inputReferenceWrapper}>
      {valuePickerConfig?.showDataType && (
        <Select
          key={'dataType'}
          popupMatchSelectWidth={false}
          style={{ width: 'auto' }}
          defaultValue={curValue?.dataType ?? PluginPluginDataType.String}
          options={ALL_DATA_TYPE.map((i) => ({ label: i, value: i }))}
          onChange={(v) => handleChange('dataType', v)}
        />
      )}
      <Select
        key={'type'}
        popupMatchSelectWidth={false}
        defaultValue={curValue?.type ?? 'input'}
        style={{ width: 'auto' }}
        options={[
          { label: 'Input', value: 'input' },
          { label: 'Reference', value: 'reference' },
        ]}
        rootClassName={styles.inputReferenceSelector}
        onChange={(v) => handleChange('type', v)}
      />
      {curValue.type === 'input' ? (
        <ValueInput
          type={curValue.dataType}
          required
          placeholder={'Input Value'}
          onChange={(e) => {
            const v = e?.target?.value ?? e
            handleChange('value', v)
          }}
          value={curValue.value}
        />
      ) : (
        <TreeSelect
          disabled={!referenceTreeData.length}
          {...filterProps}
          value={
            !referenceTreeData.length
              ? 'Data form parent nodes'
              : curValue.value
          }
          treeData={handleFilterTypeTreeData(
            referenceTreeData as any,
            curValue.dataType
          )}
          onChange={(value) => handleChange('value', value)}
          popupMatchSelectWidth={false}
        />
      )}
    </Space.Compact>
  )
}

export { ItemValuePicker }
export default ComponentWrapper(memo(ItemValuePicker))
export const getDefaultValue = () => {
  const defaultValDataType = ItemValuePickerConfig.find(
    (c) => c.sectionLabel === 'generalConfig'
  )?.items.find((i) => i.itemName === 'dataType')?.defaultValue
  return getCommonDefaultValue('valuePicker', {
    props: {
      formItemConfig: { layout: 'vertical' },
      generalConfig: { dataType: defaultValDataType },
    },
  })
}
export { associateAttribute } from './config'
