import {
  ConfigSectionType,
  FormItemType,
} from 'components/new-dynamic-form/types'
import { getCommonConfig } from '../constants'
import { ALL_DATA_TYPE } from 'constants/common'
import { PluginPluginDataType } from 'api/data-contracts'
import { cloneDeep, get, set } from 'lodash-es'

const ItemValuePickerConfig: ConfigSectionType[] = [
  ...getCommonConfig([
    {
      sectionLabel: 'generalConfig',
      items: [
        {
          itemLabel: 'DataType',
          itemName: 'dataType',
          type: 'select',
          options: Object.values(ALL_DATA_TYPE).map((i) => ({
            label: i,
            value: i,
          })),
          defaultValue: PluginPluginDataType.Object,
          disabled: true,
        },
      ],
    },
  ]),
  {
    sectionLabel: 'valuePickerConfig',
    items: [
      {
        itemLabel: 'ShowDataType',
        itemName: 'showDataType',
        type: 'switch',
        formItemConfigProps: {
          initialValue: false,
        },
      },
      { itemLabel: 'Disabled', itemName: 'disabled', type: 'switch' },
    ],
  },
]

export const associateAttribute = (item: FormItemType) => {
  console.error(item)

  const newItem = cloneDeep(item)
  const showDataType = get(newItem, 'props.valuePickerConfig.dataType')
  const generalConfig = get(newItem, 'props.generalConfig')
  // generalConfig.
  // set(newItem, 'props.generalConfig.dataType.items', 'Array<Object>')
  return newItem
}

export default ItemValuePickerConfig
