import { ConfigSectionType } from 'components/new-dynamic-form/types'
import { getCommonConfig } from '../constants'
import { PluginPluginDataType } from 'api/data-contracts'
import { ALL_DATA_TYPE } from 'constants/common'

const ItemRadioConfig: ConfigSectionType[] = [
  ...getCommonConfig([
    {
      sectionLabel: 'generalConfig',
      items: [
        {
          itemLabel: 'DataType',
          itemName: 'dataType',
          type: 'select',
          options: Object.values(ALL_DATA_TYPE).map((i) => ({
            label: i,
            value: i,
          })),
          defaultValue: PluginPluginDataType.String,
          disabled: true,
        },
      ],
    },
  ]),
  {
    sectionLabel: 'radioConfig',
    items: [
      { itemLabel: 'Disabled', itemName: 'disabled', type: 'switch' },
      { itemLabel: 'DefaultValue', itemName: 'defaultValue', type: 'input' },
      {
        itemLabel: 'Options',
        itemName: 'options',
        type: 'compactInput',
        defaultValue: [
          [
            { label: 'Label', value: '' },
            { label: 'Value', value: '' },
          ],
        ],
        minCount: 1,
      },
    ],
  },
]

export default ItemRadioConfig
