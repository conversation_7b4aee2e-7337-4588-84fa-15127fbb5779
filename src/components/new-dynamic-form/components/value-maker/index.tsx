import { memo, useEffect, useMemo, useState } from 'react'
import { cloneDeep, merge, omitBy } from 'lodash-es'
import { Input, Select, Space, TreeSelect } from 'antd'
import { getCommonDefaultValue } from '../constants'

import ItemValueMakerConfig from './config'
import ComponentWrapper from '../lib/component-wrapper'
import { ALL_DATA_TYPE } from 'constants/common'

import styles from './index.scss'
import { PluginPluginDataType } from 'api/data-contracts'
import { ValueInput } from 'views/components/value-input'
import { useFormContext } from 'components/new-dynamic-form/context'
import { handleFilterTypeTreeData } from 'components/new-dynamic-form/util'

type ValueMakerValueType = {
  name: string
  dataType: PluginPluginDataType
  type: 'input' | 'reference'
  value?: any
}

type ItemValueMakerProps = {
  valueMakerConfig: { showDataType: boolean }

  onChange?: (val: ValueMakerValueType) => void
  value?: ValueMakerValueType
  id?: string
}

const defaultValue: ValueMakerValueType = {
  name: '',
  dataType: PluginPluginDataType.String,
  type: 'input',
  value: '',
}

const ItemValueMaker = (props: ItemValueMakerProps) => {
  const { value, onChange } = props
  const [curValue, setCurValue] = useState(value ?? defaultValue)
  const { referenceTreeData = [] } = useFormContext() ?? {}

  const filterProps = useMemo(
    () => omitBy(props, (_, k) => k !== 'onChange' && /[A-Z]/.test(k)),
    [props]
  )

  const handleChange = (type: keyof ValueMakerValueType, val: string) => {
    if (type !== 'value') {
      setCurValue((cur) => {
        const curVal = {
          ...merge(cur, { [type]: val }),
          value: undefined,
        }
        onChange?.(curVal)
        return curVal
      })
    } else {
      setCurValue((cur) => {
        const curVal = merge({}, cur, { [type]: val })
        onChange?.(curVal)
        return curVal
      })
    }
  }

  useEffect(() => {
    if (value) setCurValue(value)
  }, [value])

  return (
    <Space.Compact block className={styles.inputReferenceWrapper}>
      <Input
        onChange={(e) => handleChange('name', e.target.value)}
        placeholder="Name"
        className={styles.valueMakerName}
        value={curValue.name}
      />
      <Select
        key={'dataType'}
        popupMatchSelectWidth={false}
        style={{ width: 'auto' }}
        defaultValue={curValue?.dataType ?? PluginPluginDataType.String}
        options={ALL_DATA_TYPE.map((i) => ({ label: i, value: i }))}
        onChange={(v) => handleChange('dataType', v)}
        value={curValue.dataType}
      />
      <Select
        key={'type'}
        popupMatchSelectWidth={false}
        defaultValue={curValue?.type ?? 'input'}
        style={{ width: 'auto' }}
        options={[
          { label: 'Input', value: 'input' },
          { label: 'Reference', value: 'reference' },
        ]}
        rootClassName={styles.inputReferenceSelector}
        onChange={(v) => handleChange('type', v)}
        value={curValue.type}
      />
      <div className={styles.valueMakerValueWrapper}>
        {curValue.type === 'input' ? (
          <ValueInput
            type={curValue.dataType}
            required
            placeholder={'Value'}
            onChange={(e) => {
              const v = e?.target?.value ?? e
              handleChange('value', v)
            }}
            value={curValue.value}
          />
        ) : (
          <TreeSelect
            disabled={!referenceTreeData.length}
            {...filterProps}
            value={
              !referenceTreeData.length
                ? 'Data form parent nodes'
                : curValue.value
            }
            treeData={handleFilterTypeTreeData(
              referenceTreeData as any,
              curValue.dataType
            )}
            onChange={(value) => handleChange('value', value)}
            popupMatchSelectWidth={false}
          />
        )}
      </div>
    </Space.Compact>
  )
}

export default ComponentWrapper(memo(ItemValueMaker))
export const getDefaultValue = () => {
  const defaultValDataType = ItemValueMakerConfig.find(
    (c) => c.sectionLabel === 'generalConfig'
  )?.items.find((i) => i.itemName === 'dataType')?.defaultValue
  return getCommonDefaultValue('valueMaker', {
    props: {
      formItemConfig: { layout: 'vertical' },
      generalConfig: { dataType: defaultValDataType },
    },
  })
}
export { associateAttribute } from './config'
