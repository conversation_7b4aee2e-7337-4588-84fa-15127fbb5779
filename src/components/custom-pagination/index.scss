.custom-pagination {
  &-wrapper {
    :global {
      .ant-pagination-item {
        border-radius: 8px;

        &:not(.ant-pagination-item-active):hover {
          background-color: var(--ant-pagination-hover-bg);
        }
      }
      .ant-pagination-item-active {
        border: unset;

        > a,
        a:hover {
          color: white;
        }
      }
      .ant-pagination-prev,
      .ant-pagination-next {
        display: flex;
        width: 36px;
        height: 36px;
        border-radius: 8px;
        justify-content: center;
        align-items: center;
        background-color: var(--ant-pagination-arrow-bg);

        &:hover {
          background-color: var(--ant-pagination-hover-bg);
        }
      }
    }
  }

  &-arrow {
    color: var(--ant-color-text);
    width: 20px;
    height: 20px;

    &-left {
      transform: rotate(180deg);
    }

    & > *:not(:hover) {
      stroke: var(--ant-color-text);
    }

    & > *:hover {
      stroke: var(--ant-color-text-tertiary);
    }
  }
}
