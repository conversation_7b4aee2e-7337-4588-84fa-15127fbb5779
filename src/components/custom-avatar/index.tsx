import { memo } from 'react'
import { Avatar, AvatarProps } from 'antd'
import { getFileUrl } from 'utils/common'

type CustomAvatarProps = {
  iconUUID?: string
  defaultIcon?: React.ReactNode
} & AvatarProps

const CustomAvatar = (props: CustomAvatarProps) => {
  const { iconUUID, defaultIcon, ...restProps } = props

  return (
    <Avatar
      shape="square"
      size={54}
      src={getFileUrl(iconUUID ?? '')}
      {...restProps}
      icon={defaultIcon}
    />
  )
}
export default memo(CustomAvatar)
