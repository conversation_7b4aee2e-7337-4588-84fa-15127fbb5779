import { Button } from 'antd'

import type { ButtonProps } from 'antd'
import { useState } from 'react'

export interface AsyncButtonProps
  extends Omit<ButtonProps, 'onClick' | 'loading'> {
  onClick: () => Promise<any>
}

export const AsyncButton: React.FC<AsyncButtonProps> = ({
  onClick,
  ...props
}) => {
  const [loading, setLoading] = useState(false)

  const handleClick = async () => {
    setLoading(true)
    try {
      await onClick()
    } finally {
      setLoading(false)
    }
  }

  return <Button {...props} loading={loading} onClick={handleClick} />
}

export default AsyncButton
