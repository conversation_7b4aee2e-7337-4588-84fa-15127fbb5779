import type { FormInstance } from 'antd'
import { useEffect, useRef } from 'react'

export function useResetModalOnCloseModal({
  form,
  open,
  initialValues,
}: {
  form: FormInstance
  open: boolean
  initialValues?: Record<string, any>
}) {
  const prevOpenRef = useRef<boolean | null>(null)

  useEffect(() => {
    prevOpenRef.current = open
  }, [open])

  const prevOpen = prevOpenRef.current
  useEffect(() => {
    if (!open && prevOpen) {
      form.resetFields()
      if (initialValues) {
        form.setFieldsValue(initialValues)
      }
    }
  }, [form, prevOpen, open])
}

export default useResetModalOnCloseModal
