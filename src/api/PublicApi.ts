/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { baseRequest } from '../utils/axios'
import {
  GenieCoreAppHandlersChatResponsesChatSessionResponse,
  GenieCoreAppHandlersPublicRequestsAddFeedbackRequest,
  GenieCoreAppHandlersPublicRequestsLikeUnlikeRequest,
  GenieCoreAppHandlersPublicRequestsSendRequest,
  GenieCoreAppHandlersPublicResponsesChatConversationResponse,
  GenieCoreAppHandlersPublicResponsesDocumentResponse,
  GenieCoreAppHandlersPublicResponsesPublishChannelResponse,
  GenieCoreAppHandlersPublicResponsesSuccessfulResponseMessage,
} from './data-contracts'
import { ContentType, RequestParams } from './http-client'

/**
 * @description Retrieves a list of the most recent conversations for a given session.
 *
 * @tags Public
 * @name V1ChatConversationsList
 * @summary Get recent conversations
 * @request GET:/public-api/v1/chat/conversations
 */
export const v1ChatConversationsList = (
  query: {
    /** Session UUID */
    session_uuid: string
    /** Last ID for pagination */
    last_id?: string
  },
  params: RequestParams = {}
) => {
  return baseRequest.request<GenieCoreAppHandlersPublicResponsesChatConversationResponse[]>({
    url: `/public-api/v1/chat/conversations`,
    method: 'GET',
    params: query,
    headers: { 'Content-Type': ContentType.Json },
    ...params,
  })
} /**
 * @description Retrieves conversation history and generates a PDF file
 *
 * @tags Public
 * @name V1ChatConversationsDownloadList
 * @summary Download conversation history as PDF
 * @request GET:/public-api/v1/chat/conversations/download
 */
export const v1ChatConversationsDownloadList = (
  query: {
    /** Session UUID */
    session_uuid: string
    /**
     * Most recent number of conversations to include
     * @min 1
     * @default 200
     */
    latest_no_conversations: number
    /**
     * Timezone Offset (in hours)
     * @default 0
     */
    timezone_offset?: number
  },
  params: RequestParams = {}
) => {
  return baseRequest.request<File>({
    url: `/public-api/v1/chat/conversations/download`,
    method: 'GET',
    params: query,
    headers: { 'Content-Type': ContentType.Json },
    ...params,
  })
} /**
 * @description Adds feedback to a specific conversation.
 *
 * @tags Public
 * @name V1ChatConversationsFeedbackCreate
 * @summary Add feedback to a conversation
 * @request POST:/public-api/v1/chat/conversations/feedback
 */
export const v1ChatConversationsFeedbackCreate = (
  request: GenieCoreAppHandlersPublicRequestsAddFeedbackRequest,
  params: RequestParams = {}
) => {
  return baseRequest.request<GenieCoreAppHandlersPublicResponsesSuccessfulResponseMessage>({
    url: `/public-api/v1/chat/conversations/feedback`,
    method: 'POST',
    data: request,
    headers: { 'Content-Type': ContentType.Json },
    ...params,
  })
} /**
 * @description Sets or unsets the like status of a conversation.
 *
 * @tags Public
 * @name V1ChatConversationsLikeCreate
 * @summary Like or unlike a conversation
 * @request POST:/public-api/v1/chat/conversations/like
 */
export const v1ChatConversationsLikeCreate = (
  request: GenieCoreAppHandlersPublicRequestsLikeUnlikeRequest,
  params: RequestParams = {}
) => {
  return baseRequest.request<GenieCoreAppHandlersPublicResponsesSuccessfulResponseMessage>({
    url: `/public-api/v1/chat/conversations/like`,
    method: 'POST',
    data: request,
    headers: { 'Content-Type': ContentType.Json },
    ...params,
  })
} /**
 * @description Sends a message to a chat session and returns the response as a stream of Server-Sent Events (SSE).
 *
 * @tags Public
 * @name V1ChatConversationsSendCreate
 * @summary Send chat message (Streaming)
 * @request POST:/public-api/v1/chat/conversations/send
 */
export const v1ChatConversationsSendCreate = (
  request: GenieCoreAppHandlersPublicRequestsSendRequest,
  params: RequestParams = {}
) => {
  return baseRequest.request<string>({
    url: `/public-api/v1/chat/conversations/send`,
    method: 'POST',
    data: request,
    headers: { 'Content-Type': ContentType.Json },
    ...params,
  })
} /**
 * @description Sets or unsets the unlike status of a conversation.
 *
 * @tags Public
 * @name V1ChatConversationsUnlikeCreate
 * @summary Unlike or un-unlike a conversation
 * @request POST:/public-api/v1/chat/conversations/unlike
 */
export const v1ChatConversationsUnlikeCreate = (
  request: GenieCoreAppHandlersPublicRequestsLikeUnlikeRequest,
  params: RequestParams = {}
) => {
  return baseRequest.request<GenieCoreAppHandlersPublicResponsesSuccessfulResponseMessage>({
    url: `/public-api/v1/chat/conversations/unlike`,
    method: 'POST',
    data: request,
    headers: { 'Content-Type': ContentType.Json },
    ...params,
  })
} /**
 * @description Uploads a document file and processes it immediately.
 *
 * @tags Public
 * @name V1ChatConversationsUploadDocumentCreate
 * @summary Upload a document
 * @request POST:/public-api/v1/chat/conversations/upload-document
 */
export const v1ChatConversationsUploadDocumentCreate = (
  data: {
    /**
     * Document file to upload
     * @format binary
     */
    file: File
    /** Channel UUID */
    channel_uuid: string
    /** Session UUID */
    session_uuid?: string
  },
  params: RequestParams = {}
) => {
  return baseRequest.request<GenieCoreAppHandlersPublicResponsesDocumentResponse>({
    url: `/public-api/v1/chat/conversations/upload-document`,
    method: 'POST',
    data: data,
    headers: { 'Content-Type': ContentType.FormData },
    ...params,
  })
} /**
 * @description Retrieves a publish channel's information and its associated workflow by its UUID.
 *
 * @tags Public
 * @name V1ChatPublishChannelList
 * @summary Get publish channel by UUID
 * @request GET:/public-api/v1/chat/publish-channel
 */
export const v1ChatPublishChannelList = (
  query: {
    /** Publish Channel UUID */
    uuid: string
  },
  params: RequestParams = {}
) => {
  return baseRequest.request<GenieCoreAppHandlersPublicResponsesPublishChannelResponse>({
    url: `/public-api/v1/chat/publish-channel`,
    method: 'GET',
    params: query,
    headers: { 'Content-Type': ContentType.Json },
    ...params,
  })
} /**
 * @description Retrieves a list of the most recent chat sessions for a given agent.
 *
 * @tags Public
 * @name V1ChatSessionsRecentList
 * @summary Get recent chat sessions
 * @request GET:/public-api/v1/chat/sessions/recent
 */
export const v1ChatSessionsRecentList = (
  query: {
    /** Channel UUID */
    channel_uuid: string
  },
  params: RequestParams = {}
) => {
  return baseRequest.request<GenieCoreAppHandlersChatResponsesChatSessionResponse[]>({
    url: `/public-api/v1/chat/sessions/recent`,
    method: 'GET',
    params: query,
    headers: { 'Content-Type': ContentType.Json },
    ...params,
  })
} /**
 * @description Get a specific template by its ID
 *
 * @tags Public
 * @name V1TemplatesContentDetail
 * @summary Get Content Template by ID
 * @request GET:/public-api/v1/templates/{id}/content
 */
export const v1TemplatesContentDetail = (id: number, params: RequestParams = {}) => {
  return baseRequest.request<string>({
    url: `/public-api/v1/templates/${id}/content`,
    method: 'GET',
    headers: { 'Content-Type': ContentType.Json },
    ...params,
  })
}
