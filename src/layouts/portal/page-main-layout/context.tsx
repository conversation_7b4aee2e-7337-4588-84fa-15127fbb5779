import React, { useContext } from 'react'
import { ActionItemProps, MenuItemProps } from './index'

export type MainLayoutContextType = {
  setRegisterEvents: (value: Function[]) => void
  setMenu: (value: MenuItemProps[]) => void
  setCaption: (value: React.ReactNode) => void
  setTitle: (value: React.ReactNode) => void
  setIcon: (value: React.ReactNode) => void
  setActions: (value: ActionItemProps[]) => void
}

export const MainLayoutContext =
  React.createContext<MainLayoutContextType | null>(null)

export const useMainLayoutContext = () => {
  const context = useContext(MainLayoutContext)
  return context
}
