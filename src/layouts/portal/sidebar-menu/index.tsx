import { BuildFilled } from '@ant-design/icons'
import { Menu } from 'antd'
import { useEffect, useState } from 'react'
import Planet from 'assets/images/planet.svg'
import { useExNavigate } from 'hooks/use-ex-navigate'
import { observer } from 'mobx-react'
import {
  BankFilled,
  RobotFilled,
  DatabaseFilled,
  DashboardOutlined,
} from '@ant-design/icons'
import { useGlobalStore } from 'stores/global'

import styles from './index.scss'
import { isLocal } from 'utils/env'

const MenuItems = [
  {
    key: 'dashboard',
    label: 'Dashboard',
    route: '/portal/dashboard',
    icon: <DashboardOutlined />,
  },
  {
    key: 'organization',
    label: 'Organization',
    route: '/portal/organization',
    icon: <BankFilled />,
  },
  {
    key: 'agent',
    label: 'Agent',
    route: '/portal/agent',
    icon: <RobotFilled />,
  },
  {
    key: 'metric',
    label: 'Metric',
    route: '/portal/metric',
    icon: <BuildFilled />,
  },
  {
    key: 'knowledge',
    label: 'Knowledge',
    route: '/portal/knowledge',
    icon: <DatabaseFilled />,
  },
  // {
  //   key: 'plugin',
  //   label: 'My Plugin',
  //   route: '/portal/plugin',
  //   icon: <Union />,
  // },
  {
    key: 'marketplace',
    label: 'Marketplace',
    route: '/portal/marketplace',
    icon: <Planet />,
  },
]

if (isLocal) {
  MenuItems.push({
    key: 'test',
    label: 'Test',
    route: '/portal/test',
    icon: <BuildFilled />,
  })
}

const WhiteListMenuItems = ['organization', 'agent', 'metric', 'knowledge']

const SidebarMenu = () => {
  const navigate = useExNavigate()
  const isDevMode = useGlobalStore((state) => state.isDeveloperMode)
  const [curPath, setCurPath] = useState('')

  const handleMenuClick = (path: string) => {
    const route = MenuItems.find((item) => item.key === path)?.route
    if (!route) return
    navigate(`${route}`)
  }

  useEffect(() => {
    // init the path while page refresh
    handleMenuClick(curPath)
  }, [])

  useEffect(() => {
    setCurPath(
      location.pathname.startsWith('/portal')
        ? location.pathname.split('/').slice(2).join('/')
        : ''
    )
  }, [location.pathname])

  return (
    <Menu
      rootClassName={styles.sidebarMenuWrapper}
      theme="light"
      mode="inline"
      selectedKeys={[curPath.split('/')[0] ?? '']}
      items={
        isDevMode
          ? MenuItems
          : MenuItems.filter((i) => WhiteListMenuItems.includes(i.key))
      }
      onClick={(info) => handleMenuClick(info.key)}
    />
  )
}

export default observer(SidebarMenu)
