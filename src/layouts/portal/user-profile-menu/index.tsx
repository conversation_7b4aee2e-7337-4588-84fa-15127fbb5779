import {
  UserOutlined,
  KeyOutlined,
  SunOutlined,
  MoonOutlined,
} from '@ant-design/icons'
import { MenuProps, Switch } from 'antd'
import { Avatar, AvatarProps, Button, Flex, Menu, Typography } from 'antd'
import LogoutIcon from 'assets/images/logout.svg'
import QrCodeIcon from 'assets/images/qr-code-icon.svg'
import SettingsIcon from 'assets/images/settings.svg'
import cls from 'classnames'
import { useExNavigate } from 'hooks/use-ex-navigate'
import { useEffect, useState } from 'react'
import userStore from 'stores/user'
import { useGlobalStore } from 'stores/global'
import { observer } from 'mobx-react'
import { getFileUrl, isEmpty } from 'utils/common'
import Settings from 'views/portal/settings'
import TwoFactorAuth from 'views/portal/settings/two-factor-auth'
import { DEVELOPER_SETTINGS_URL } from 'constants/common'
import styles from './index.scss'

interface UserProfileMenuProps {
  onLogout?: () => Promise<void>
  className?: string
  customMenuItems?: MenuProps['items']
  onMenuClick?: MenuProps['onClick']
}

interface ProfileModalsProps {
  isSettingsOpen: boolean
  isTwoFactorAuthOpen: boolean
  onSettingsClose: () => void
  onTwoFactorAuthClose: () => void
}

const ProfileModals: React.FC<ProfileModalsProps> = ({
  isSettingsOpen,
  isTwoFactorAuthOpen,
  onSettingsClose,
  onTwoFactorAuthClose,
}) => {
  return (
    <>
      <Settings
        open={isSettingsOpen}
        okText="Save"
        onFinish={onSettingsClose}
        onCancel={onSettingsClose}
      />
      <TwoFactorAuth
        open={isTwoFactorAuthOpen}
        okText="Save"
        onFinish={onTwoFactorAuthClose}
        onCancel={onTwoFactorAuthClose}
      />
    </>
  )
}

interface UserAvatarProps extends Omit<AvatarProps, 'icon'> {
  avatarUUID?: string
  userName?: string
  showFallback?: boolean
}

const UserAvatar: React.FC<UserAvatarProps> = ({
  avatarUUID,
  userName = 'User',
  showFallback = true,
  ...props
}) => {
  const avatarUrl = avatarUUID ? getFileUrl(avatarUUID) : ''
  const hasValidImage = !isEmpty(avatarUrl)

  return (
    <Avatar
      icon={!hasValidImage && showFallback ? <UserOutlined /> : undefined}
      src={hasValidImage ? avatarUrl : undefined}
      alt={`${userName} avatar`}
      style={{
        flexShrink: 0,
        borderRadius: '9px',
        borderColor: '#eff1f4',
        backgroundColor: '#efefef',
        ...props?.style,
      }}
      shape={props?.shape ?? 'square'}
      size={props?.size ?? 'large'}
      {...props}
    />
  )
}

const UserProfileMenu: React.FC<UserProfileMenuProps> = ({
  onLogout,
  className,
  customMenuItems,
  onMenuClick,
}) => {
  const navigator = useExNavigate()
  const { theme, setTheme, isDeveloperMode } = useGlobalStore()
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const [isTwoFactorAuthOpen, setIsTwoFactorAuthOpen] = useState(false)
  const [developerSettingsCounter, setDeveloperSettingsCounter] = useState(0)

  useEffect(() => {
    if (developerSettingsCounter === 1) {
      setTimeout(() => {
        setDeveloperSettingsCounter(0)
      }, 6000)
    } else if (developerSettingsCounter >= 10) {
      navigator(DEVELOPER_SETTINGS_URL)
      setDeveloperSettingsCounter(0)
    }
  }, [developerSettingsCounter])

  const handleClickSettings = () => {
    setIsSettingsOpen(true)
  }

  const handleClickTwoFactorAuth = () => {
    setIsTwoFactorAuthOpen(true)
  }

  const handleLogout = async () => {
    if (onLogout) {
      await onLogout()
    } else {
      await userStore.logout()
      window.location.href = '/login'
    }
  }

  const handleChangeTheme = (theme: 'dark' | 'light') => {
    setTheme(theme)
  }

  const handleMenuItemClick: MenuProps['onClick'] = (info) => {
    if (onMenuClick) {
      onMenuClick(info)
      return
    }

    switch (info.key) {
      case 'settings': {
        handleClickSettings()
        break
      }
      case 'two-factor-auth': {
        handleClickTwoFactorAuth()
        break
      }
      case 'logout': {
        handleLogout()
        break
      }
    }
  }

  const defaultMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      className: styles.userProfileMenuItem,
      disabled: true,
      label: (
        <div className={styles.userProfileWrapper}>
          <UserAvatar
            shape="square"
            avatarUUID={userStore.loginUser?.avatarUUID}
            userName={userStore.loginUser?.name}
            onClick={() => {
              setDeveloperSettingsCounter(developerSettingsCounter + 1)
            }}
          />
          <div className={styles.userProfileInfo}>
            <span>
              <b>{userStore.loginUser?.name}</b>&nbsp;(
              {userStore.loginUser?.roleName})
            </span>
            <span>{userStore.loginUser?.email}</span>
          </div>
        </div>
      ),
    },
    {
      key: 'settings',
      label: (
        <Button
          type="text"
          icon={<SettingsIcon style={{ fontSize: '18px' }} />}
          onClick={handleClickSettings}
          className={cls(styles.menuItemButton, styles.withIcon)}
        >
          Settings
        </Button>
      ),
    },
    {
      key: 'two-factor-auth',
      label: (
        <Button
          type="text"
          icon={<QrCodeIcon style={{ fontSize: '18px' }} />}
          onClick={handleClickTwoFactorAuth}
          className={cls(styles.menuItemButton, styles.withIcon)}
        >
          2FA Setup
        </Button>
      ),
    },
    {
      key: 'api-keys',
      label: (
        <Button
          type="text"
          icon={<KeyOutlined style={{ fontSize: '18px' }} />}
          onClick={() => navigator('/portal/api-keys')}
          className={cls(styles.menuItemButton, styles.withIcon)}
        >
          API Keys
        </Button>
      ),
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      label: (
        <Flex gap={8} justify="space-between" align="center">
          <Button
            type="text"
            icon={<LogoutIcon style={{ fontSize: '18px' }} />}
            onClick={handleLogout}
            className={cls(styles.menuItemButton, styles.withIcon)}
          >
            Logout
          </Button>
          {isDeveloperMode && (
            <Switch
              defaultChecked={theme === 'dark'}
              onClick={(checked, event) => {
                handleChangeTheme(checked ? 'dark' : 'light')
                event.stopPropagation()
              }}
              checkedChildren={<SunOutlined style={{ marginLeft: '-6px' }} />}
              unCheckedChildren={
                <MoonOutlined style={{ marginRight: '-6px' }} />
              }
            />
          )}
        </Flex>
      ),
    },
  ]

  const menuItems = customMenuItems || defaultMenuItems

  return (
    <>
      <Menu
        className={cls(styles.userProfileSiderWrapper, className)}
        selectedKeys={[]}
        getPopupContainer={(node) => node.parentNode as HTMLElement}
        onClick={handleMenuItemClick}
        triggerSubMenuAction="click"
        items={[
          {
            key: 'user',
            label: (
              <Flex className={styles.userProfileSiderLabel}>
                <Flex
                  align="center"
                  gap="17px"
                  className={styles.userProfileSiderLabelContent}
                >
                  <UserAvatar
                    shape="square"
                    size={36}
                    avatarUUID={userStore.loginUser?.avatarUUID}
                    userName={userStore.loginUser?.name}
                  />
                  <Typography.Text ellipsis className={styles.userProfileName}>
                    {userStore.loginUser?.name}
                  </Typography.Text>
                </Flex>
                <Button
                  type="text"
                  className={cls(styles.userProfileLogout)}
                  icon={
                    <LogoutIcon
                      style={{
                        fontSize: '20px',
                      }}
                    />
                  }
                />
              </Flex>
            ),
            expandIcon: <></>,
            children: menuItems,
          },
        ]}
      />
      <ProfileModals
        isSettingsOpen={isSettingsOpen}
        isTwoFactorAuthOpen={isTwoFactorAuthOpen}
        onSettingsClose={() => setIsSettingsOpen(false)}
        onTwoFactorAuthClose={() => setIsTwoFactorAuthOpen(false)}
      />
    </>
  )
}

export default observer(UserProfileMenu)
