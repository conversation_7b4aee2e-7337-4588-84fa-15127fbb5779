import { PluginPluginDataType } from 'api/data-contracts'

export const DEVELOPER_SETTINGS_URL = '/portal/developer-settings'

export const GLOBAL_DATE_FORMAT = 'DD/MM/YYYY'
export const GLOBAL_DATE_TIME_FORMAT = 'DD/MM/YYYY HH:mm:ss'
export const ISO_8601_DATE_FORMAT = 'YYYY-MM-DD'
export const ISO_8601_DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss'
export const BASE_DATA_TYPE = [...Object.values(PluginPluginDataType)] as const
export const ARRAY_DATA_TYPE = [
  ...Object.values(PluginPluginDataType).map<`Array<${PluginPluginDataType}>`>(
    (i) => `Array<${i}>`
  ),
] as const
export const ALL_DATA_TYPE = [...BASE_DATA_TYPE, ...ARRAY_DATA_TYPE]
export const isArrayType = (
  value: string
): value is (typeof ARRAY_DATA_TYPE)[number] => {
  return /^Array<.+>$/.test(value)
}

// List of file types supported by backend for upload
export const supportedFileTypes = [
  { label: 'PDF (.pdf)', value: '.pdf' },
  { label: 'CSV (.csv)', value: '.csv' },
  { label: 'Excel (.xls)', value: '.xls' },
  { label: 'Excel (.xlsx)', value: '.xlsx' },
  { label: 'PowerPoint (.ppt)', value: '.ppt' },
  { label: 'PowerPoint (.pptx)', value: '.pptx' },
  { label: 'Word (.doc)', value: '.doc' },
  { label: 'Word (.docx)', value: '.docx' },
  { label: 'Text (.txt)', value: '.txt' },
  { label: 'JSON (.json)', value: '.json' },
]
