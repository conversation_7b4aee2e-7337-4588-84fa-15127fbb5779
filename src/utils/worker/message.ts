export type MessageWorkerTypeEnum = 'message' | 'lifecycle' | 'action'
export type MessageWorkerPayloadType = {
  type: string
  data?: Record<string, any>
}
export type MessageWorkerMessage = {
  msgType: MessageWorkerTypeEnum
  payload: MessageWorkerPayloadType
}

export default class MessageWorkerBase {
  private _targetWindow: MessageEventSource = window.parent
  public get targetWindow(): MessageEventSource {
    return this._targetWindow
  }
  public set targetWindow(s: MessageEventSource) {
    this._targetWindow = s
  }

  constructor() {}

  private initTargetWindow = (e: MessageEvent) => {
    if (!this.targetWindow && e.source) this.targetWindow = e.source
  }

  private defaultGetMessage = (e: MessageEvent) => {
    // console.log('From parent message received:', e.data)
    this.initTargetWindow(e)
  }

  register = (fn?: (e: MessageEvent) => any) => {
    window.addEventListener(
      'message',
      fn
        ? (e) => {
            this.initTargetWindow(e)
            fn(e)
          }
        : this.defaultGetMessage
    )
  }

  remove = (fn?: (e: MessageEvent) => any) => {
    window.removeEventListener('message', fn ? fn : this.defaultGetMessage)
  }

  postMessage = (
    message: MessageWorkerMessage,
    targetOrigin = '*',
    targetWindow = this.targetWindow
  ) => {
    // console.log('Message to parent sent:', message)
    targetWindow?.postMessage(message, { targetOrigin })
  }
}
