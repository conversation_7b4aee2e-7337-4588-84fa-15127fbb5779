import { ApiErrorCodes } from './api-error-codes'

const API_ERROR_MESSAGES: Partial<Record<ApiErrorCodes, string>> = {
  // General errors (100xxx)
  [ApiErrorCodes.InvalidInput]: 'Please check your input and try again',
  [ApiErrorCodes.DatabaseException]:
    'We encountered a database error. Please try again later',
  [ApiErrorCodes.FailedToProcessDocument]: 'Failed to process document',
  [ApiErrorCodes.AuthenticationRequired]: 'Authentication required',
  [ApiErrorCodes.InvalidToken]: 'Invalid token',
  [ApiErrorCodes.FetchingFileFromRequest]: 'Error retrieving file from request',
  [ApiErrorCodes.FileValidationFailed]:
    'The file you uploaded is invalid or corrupted',
  [ApiErrorCodes.SavingUploadedFile]: 'Failed to save the uploaded file',
  [ApiErrorCodes.PermissionDenied]:
    'You do not have permission to perform this action',
  [ApiErrorCodes.RecordNotFound]: 'The requested record could not be found',

  // Global errors (200xxx)
  [ApiErrorCodes.OrganizationNotFound]: 'Organization not found',
  [ApiErrorCodes.OrganizationExpired]: 'Your organization  has expired',
  [ApiErrorCodes.OrganizationDisabled]: 'Your organization has been disabled',
  [ApiErrorCodes.InvalidCredentials]: 'Invalid email or password',
  [ApiErrorCodes.UserDisabled]: 'Your account has been disabled',
  [ApiErrorCodes.TwoFactorOTPRequired]: 'Two-factor authentication required',
  [ApiErrorCodes.UserChangePasswordRequired]:
    'Please change your password to continue',
  [ApiErrorCodes.UserSetup2FARequired]:
    'Please set up two-factor authentication to continue',
  [ApiErrorCodes.TwoFactorAuthenticationFailed]:
    'Invalid two-factor authentication code',

  // User module errors (300xxx)
  [ApiErrorCodes.AgentNotFound]: 'Agent not found',
  [ApiErrorCodes.IndexNotFound]: 'Index not found',
  [ApiErrorCodes.PublishChannelNotFound]: 'Publish channel not found',
  [ApiErrorCodes.WorkflowNotFound]: 'Workflow not found',

  // File module errors (400xxx)
  [ApiErrorCodes.FileNotFound]: 'File not found',
  [ApiErrorCodes.UpdateFilenameFailed]: 'Failed to update filename',
  [ApiErrorCodes.DeleteFileFailed]: 'Failed to delete file',
  [ApiErrorCodes.FetchFilesFailed]: 'Failed to retrieve files',

  // User management errors (500xxx)
  [ApiErrorCodes.UserAlreadyExists]:
    'An account with this email already exists',
  [ApiErrorCodes.UserNotFound]: 'User not found',
  [ApiErrorCodes.SessionExpired]:
    'Your session has expired. Please log in again',
  [ApiErrorCodes.Invalid2FACode]: 'Invalid two-factor authentication code',
  [ApiErrorCodes.UserNotEligible]:
    'You are not eligible to perform this action',

  // Question/Metrics errors (600xxx)
  [ApiErrorCodes.QuestionNotFound]: 'Question not found',
  [ApiErrorCodes.MetricsNotFound]: 'Metrics not found',
  [ApiErrorCodes.RoleNotFound]: 'Role not found',

  // API Key errors (700xxx)
  [ApiErrorCodes.APIKeyNotFound]: 'API key not found',
  [ApiErrorCodes.CreateAPIKeyFailed]: 'Failed to create API key',
  [ApiErrorCodes.DeleteAPIKeyFailed]: 'Failed to delete API key',
  [ApiErrorCodes.FetchAPIKeysFailed]: 'Failed to retrieve API keys',
  [ApiErrorCodes.InvalidAPIKey]: 'Invalid API key',
  [ApiErrorCodes.UpdateAPIKeyFailed]: 'Failed to update API key',
}

/**
 * Gets a user-friendly error message from an error object
 * 1. Checks if there is a code and if our ERROR_MESSAGES mapping has that error message
 * 2. If there is, returns that message
 * 3. If there is no corresponding code, checks if 'message' is available and uses that instead
 * 4. Falls back to a default message if no specific message can be determined
 */
export function getApiErrorMessage(error: unknown) {
  const defaultMessage = 'Oops! Something went wrong. Please contact support.'

  // If error is not an object, return default message
  if (!error || typeof error !== 'object') {
    return defaultMessage
  }

  // Check for API error format with code property
  if ('code' in error && typeof error.code === 'number') {
    // First check if we have a predefined message for this error code
    const errorCode = error.code as ApiErrorCodes
    if (API_ERROR_MESSAGES[errorCode]) {
      return API_ERROR_MESSAGES[errorCode]
    }
  }

  // If no matching error code or no code property, check for message property
  if ('message' in error && typeof error.message === 'string') {
    return error.message
  }

  return defaultMessage
}

export function getApiErrorCode(error: unknown) {
  if (error && typeof error === 'object') {
    if ('code' in error && typeof error.code === 'number') {
      return error.code
    }
  }
}
