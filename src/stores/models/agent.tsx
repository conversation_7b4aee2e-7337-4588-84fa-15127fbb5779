import { AgentTypeAgentType } from 'api/data-contracts'

// TODO: update Agent type to ResponsesAgentResponse from src/api/data-contracts.ts
export interface Agent {
  id: number
  uuid: string
  agentName: string
  agentType: AgentTypeAgentType
  description: string
  agentIconUUID: string
  agentIconID: number
  organizationId: number
  updatedAt: string
  createdAt: string
}

export interface AgentList {
  totalCount: number
  agents: Agent[]
}
