// Enum for channel types
export enum ChannelType {
  ChatPage = 'chat_page',
  ChatBubble = 'chat_bubble',
}

// Interface for a workflow node
export interface WorkflowNode {
  id: string
  type: string
  data: {
    data: {
      label: string
      description: string
      input: Array<{
        name: string
        type: string
        dataType: string
        value: any
        reference: string
      }>
      output: Array<{
        name: string
        type: string
        description: string
        required: boolean
        children: any[]
      }>
      intentBranch: any[]
      conditionBranch: any[]
      branchOutput: any
    }
  }
  position: {
    x: number
    y: number
  }
  measured?: {
    width: number
    height: number
  }
  selected?: boolean
  zIndex?: number
}

// Interface for a workflow edge
export interface WorkflowEdge {
  id: string
  source: string
  target: string
  sourceHandle: null
  targetHandle: null
  type: string
  animated: boolean
  style: {
    strokeWidth: number
  }
  markerEnd: {
    type: string
  }
}

// Interface for a single channel with workflow
export interface ChannelWithWorkflow {
  id: number
  uuid: string
  title: string
  agentID: number
  organizationID: number
  extraData: string
  nodes: WorkflowNode[]
  edges: WorkflowEdge[]
  workflowVersion: string
  channelType: ChannelType
  enabled: boolean
  createdAt: string
  updatedAt: string
}
export interface InputItem {
  name: string
  type: string
  dataType: string
  value: any
  reference: string
}

export interface ExtraDataItem {
  [key: string]: any
}

export interface ChatSession {
  id: number
  uuid: string
  title: string
  agentID: number
  userID: number
  organizationID: number
  createdAt: Date
  updatedAt: Date
}

export interface Conversation {
  id: string
  sessionUUID: string
  userID: string
  organizationID: string
  content: string
  role: string
  tokens: number
  createdAt: string
  error: boolean
  isLoading: boolean
  currentNodeLabel: string
  currentNodeID: string
  startTime: string
  endTime: string
  like: boolean
  unlike: boolean
  document: {
    document_id: string
    file_name: string
  }
}
export interface SendResponse {
  uuid: string
  title: string
  answer: string
  requestMessageID: string
  responseMessageID: string
  promptTokens: number
  completionTokens: number
  nodeID: string
  nodeLabel: string
  nodeType: string
  startTime: string
  endTime: string
  status: string
  errorMessage: string
  nodeStatus: {
    nodeID: string
    startTime: string
    endTime: string
    output: {
      text: {
        type: string
        value: string
      }
    }
  }
}
