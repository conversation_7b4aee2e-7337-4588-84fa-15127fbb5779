export enum PermissionType {
  Dashboard = 'dashboard',

  OrganizationManagement = 'organization',

  UserManagement = 'user',
  UserList = 'user_list',
  UserCreate = 'user_create',
  UserUpdate = 'user_update',
  UserDelete = 'user_delete',

  RoleManagement = 'role',
  RoleList = 'role_list',
  RoleCreate = 'role_create',
  RoleUpdate = 'role_update',
  RoleDelete = 'role_delete',

  KnowledgeManagement = 'knowledge',
  KnowledgeList = 'knowledge_list',
  KnowledgeCreate = 'knowledge_create',
  KnowledgeUpdate = 'knowledge_update',
  KnowledgeDelete = 'knowledge_delete',

  IntentManagement = 'intent',
  IntentList = 'intent_list',
  IntentCreate = 'intent_create',
  IntentUpdate = 'intent_update',
  IntentDelete = 'intent_delete',

  AgentManagement = 'agent',
  AgentList = 'agent_list',
  AgentCreate = 'agent_create',
  AgentUpdate = 'agent_update',
  AgentDelete = 'agent_delete',

  IntegrationManagement = 'integration',
}

export interface Permission {
  id: number
  organization_type: string
  parent_id: number
  name: string
  identity: PermissionType
  created_at: string
  updated_at: string
  subs?: Permission[]
}

export interface PermissionTree {
  permissions: Permission[]
}
