import { makeAutoObservable } from 'mobx'
import apiConfig from 'services/api'
import {
  createApiRequest,
  createStreamingApiRequest,
  RequestData,
} from 'services/request'
import { AxiosResponse } from 'axios'
import {
  ChannelWithWorkflow,
  WorkflowNode,
  WorkflowEdge,
  Conversation,
  SendResponse,
} from './models/public-chat'

import { getUserLanguage } from 'utils/common'
import dayjs from 'dayjs'
import { assign } from 'lodash-es'

class PublicChatStore {
  channel: ChannelWithWorkflow | undefined = undefined
  conversations: Conversation[] = []
  sessionUUID: string = ''
  constructor() {
    makeAutoObservable(this)
  }

  GetChannel(uuid: string): Promise<AxiosResponse<ChannelWithWorkflow>> {
    const params: RequestData = {
      queryParams: {
        uuid,
      },
    }

    return new Promise((resolve, reject) => {
      createApiRequest(apiConfig.channelGetByUUID, params)
        .then((response: AxiosResponse<ChannelWithWorkflow>) => {
          const parsedData = {
            ...response.data,
            nodes: this.parseJSON(response.data.nodes) as WorkflowNode[],
            edges: this.parseJSON(response.data.edges) as WorkflowEdge[],
          }
          this.channel = parsedData
          resolve({ ...response, data: parsedData })
        })
        .catch((error) => {
          reject(new Error(error.message ?? 'Error fetching channel'));
        })
    })
  }

  setDefaultConversation(conversations: Conversation[]) {
    this.conversations = conversations
  }

  send(
    query: Partial<Conversation>,
    params?: Record<string, any>
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const tempConversation: Conversation = assign(
        {
          id: '',
          sessionUUID: this.sessionUUID,
          userID: '',
          organizationID: '',
          content: query.content ?? '',
          role: 'user',
          tokens: 0,
          createdAt: dayjs().toISOString(),
          error: false,
          isLoading: false,
          currentNodeID: '',
          currentNodeLabel: '',
          startTime: '',
          endTime: '',
          like: false,
          unlike: false,
          document: {
            document_id: '',
            file_name: '',
          },
        },
        query
      )

      const newConversation: Conversation = {
        id: '',
        sessionUUID: this.sessionUUID,
        userID: '',
        organizationID: '',
        content: '',
        role: 'assistant',
        tokens: 0,
        createdAt: dayjs().toISOString(),
        error: false,
        isLoading: true,
        currentNodeID: '',
        currentNodeLabel: '',
        startTime: '',
        endTime: '',
        like: false,
        unlike: false,
        document: {
          document_id: '',
          file_name: '',
        },
      }

      this.conversations.push(tempConversation)
      this.conversations.push(newConversation)

      const commonParams = {
        uuid: this.channel?.uuid,
        userPrompt: query.content ?? '',
        sessionUUID: this.sessionUUID,
        language: getUserLanguage(),
        documentID: query?.document?.document_id ?? '',
        imageID: '',
        audioID: '',
      }

      createStreamingApiRequest(apiConfig.publicChatSend, {
        body: assign(commonParams, params),
        onMessage: (parsedData: SendResponse) => {
          if (parsedData.status !== 'completed') {
            this.conversations[this.conversations.length - 1].content +=
              parsedData.answer
          } else {
            tempConversation.id = parsedData.requestMessageID
            tempConversation.tokens = parsedData.promptTokens
            this.conversations[this.conversations.length - 1].id =
              parsedData.responseMessageID
          }
          this.conversations[this.conversations.length - 1].currentNodeID =
            parsedData.nodeID
          this.conversations[this.conversations.length - 1].currentNodeLabel =
            parsedData.nodeLabel
          this.conversations[this.conversations.length - 1].startTime =
            parsedData.startTime
          this.conversations[this.conversations.length - 1].endTime =
            parsedData.endTime
          if (!this.sessionUUID) {
            this.sessionUUID = parsedData.uuid
          }
        },
        onError: (error) => {
          console.error('Error occurred:', error)
          this.conversations[this.conversations.length - 1].error = true
          this.conversations[this.conversations.length - 1].isLoading = false
          reject(new Error('Error sending message'))
        },
        onComplete: () => {
          this.conversations[this.conversations.length - 1].error = false
          this.conversations[this.conversations.length - 1].isLoading = false
          resolve()
        },
      }).catch((error) => {
        console.error('Request failed:', error)
        reject(new Error(error.message ?? 'Error sending message'))
      })
    })
  }

  like(id: string, status: boolean): Promise<void> {
    return new Promise((resolve, reject) => {
      const params: RequestData = {
        body: { _id: id, status },
      }

      const conversationIndex = this.conversations.findIndex(
        (conv) => conv.id === id
      )
      if (conversationIndex === -1) return

      if (status && this.conversations[conversationIndex].unlike) {
        this.unlike(id, false)
        this.conversations[conversationIndex].unlike = false
      }

      createApiRequest(apiConfig.publicChatLike, params)
        .then(() => {
          this.conversations[conversationIndex].like = status
          resolve()
        })
        .catch((error) => {
          console.error('Error occurred while liking:', error)
          reject(new Error(error.message || 'Error liking message'))
        })
    })
  }

  unlike(id: string, status: boolean): Promise<void> {
    return new Promise((resolve, reject) => {
      const params: RequestData = {
        body: { _id: id, status },
      }

      const conversationIndex = this.conversations.findIndex(
        (conv) => conv.id === id
      )
      if (conversationIndex === -1) return
      if (status && this.conversations[conversationIndex].like) {
        this.like(id, false)
        this.conversations[conversationIndex].like = false
      }

      createApiRequest(apiConfig.publicChatUnlike, params)
        .then(() => {
          this.conversations[conversationIndex].unlike = status
          resolve()
        })
        .catch((error) => {
          console.error('Error occurred while disliking:', error)
          reject(new Error(error.message ?? 'Error disliking message'))
        })
    })
  }

  private parseJSON(data: string | any[]): any[] {
    if (typeof data === 'string') {
      try {
        return JSON.parse(data)
      } catch (error) {
        console.error('Error parsing JSON:', error)
        return []
      }
    }
    return data
  }
}

export default new PublicChatStore()
