import { AxiosResponse } from 'axios'
import { makeAutoObservable, runInAction } from 'mobx'
import apiConfig from 'services/api'
import { createApiRequest, RequestData } from 'services/request'
import { CustomNodeProps } from '../views/portal/agent/studio/workflow/model'
import { API, APIList } from './models/api'
import { Intent, IntentList } from './models/intent'
import { KnowledgeList } from './models/knowledge'
import { Model, RerankModel } from './models/model'
import { v1WorkflowGetLatestWorkflowByAgentList } from 'api/Api'
import {
  ResponsesLLMResponse,
  ResponsesWorkflowResponse,
} from 'api/data-contracts'

class WorkflowStore {
  selectedNode: CustomNodeProps | null = null
  selectedPanel: string = ''
  toolNodeVisible: boolean = false
  toolNodeSource: string = ''
  toolNodeTarget: string = ''
  toolNodeSourceHandleID: string | null | undefined = ''
  toolNodeTargetHandleID: string | null | undefined = ''

  apiList: APIList = {
    totalCount: 0,
    apis: [],
  }

  intentList: IntentList = {
    totalCount: 0,
    intents: [],
  }

  knowledgeList: KnowledgeList = {
    totalCount: 0,
    knowledges: [],
  }

  models: Model[] = []
  rerankModels: RerankModel[] = []

  workflow: ResponsesWorkflowResponse | null = null

  constructor() {
    makeAutoObservable(this)
  }

  selectNode(node: CustomNodeProps | null = null) {
    this.selectedNode = node
  }

  selectPanel(panel: string = '') {
    this.selectedPanel = panel
  }

  setModels(data: ResponsesLLMResponse[]) {
    runInAction(() => {
      this.models = data
    })
  }

  toggleToolNode(
    visible: boolean,
    toolNodeSource: string,
    toolNodeTarget: string,
    toolNodeSourceHandleID: string | null | undefined,
    toolNodeTargetHandleID: string | null | undefined
  ) {
    this.toolNodeVisible = visible
    this.toolNodeSource = toolNodeSource
    this.toolNodeTarget = toolNodeTarget
    this.toolNodeSourceHandleID = toolNodeSourceHandleID
    this.toolNodeTargetHandleID = toolNodeTargetHandleID
  }

  getAPIsByAgentID(agentID: number): Promise<AxiosResponse<any>> {
    const params: RequestData = {
      queryParams: {
        agentID,
      },
    }

    return new Promise((resolve, reject) => {
      createApiRequest(apiConfig.apiAllList, params)
        .then(
          (response: AxiosResponse<{ totalCount: number; apis: API[] }>) => {
            this.apiList = {
              totalCount: response.data.totalCount,
              apis: response.data.apis,
            }
            resolve(response)
          }
        )
        .catch((error) => {
          reject(new Error(error.message ?? 'Error fetching APIs'))
        })
    })
  }

  getIntentsByAgentID(agentID: number): Promise<AxiosResponse<any>> {
    const params: RequestData = {
      queryParams: {
        agentID,
      },
    }

    return new Promise((resolve, reject) => {
      createApiRequest(apiConfig.intentAllList, params)
        .then(
          (
            response: AxiosResponse<{ totalCount: number; intents: Intent[] }>
          ) => {
            this.intentList = {
              totalCount: response.data.totalCount,
              intents: response.data.intents,
            }
            resolve(response)
          }
        )
        .catch((error) => {
          reject(new Error(error.message ?? 'Error fetching Intents'))
        })
    })
  }

  getKnowledges(page: number, size: number): Promise<AxiosResponse<any>> {
    return new Promise((resolve, reject) => {
      const params: RequestData = {
        queryParams: {
          page,
          size,
        },
      }

      createApiRequest(apiConfig.knowledgeList, params)
        .then((response: AxiosResponse<KnowledgeList>) => {
          this.knowledgeList = {
            totalCount: response.data.totalCount,
            knowledges: response.data.knowledges,
          }
          resolve(response)
        })
        .catch((error) => {
          reject(new Error(error.message ?? 'Error fetching Knowledges'))
        })
    })
  }

  // getLLMModels(): Promise<void> {
  //   return new Promise((resolve, reject) => {
  //     createApiRequest(apiConfig.llmModels)
  //       .then((response) => {
  //         const formattedModels: Model[] = response.data.map((item: any) => ({
  //           id: item.id,
  //           name: `${item.name} - Context: ${item.contextWindow} tokens`,
  //           developer: item.developer,
  //         }))
  //         this.setModels(formattedModels)
  //         resolve()
  //       })
  //       .catch((error) => {
  //         reject(error)
  //       })
  //   })
  // }

  getRerankModels(): Promise<void> {
    return new Promise((resolve, reject) => {
      createApiRequest(apiConfig.rerankModels)
        .then((response) => {
          const formattedModels: RerankModel[] = response.data.map(
            (item: any) => ({
              id: item.id,
              name: `${item.name}`,
              developer: item.developer,
            })
          )
          this.rerankModels = formattedModels
          resolve()
        })
        .catch((error) => {
          reject(new Error(error.message ?? 'Error fetching Rerank Models'))
        })
    })
  }

  async getLatestWorkflowsByAgentID(agentID: number) {
    const workflowResp = await v1WorkflowGetLatestWorkflowByAgentList({
      agentID,
    })
    if (!workflowResp.data) return
    this.workflow = workflowResp.data
    return this.workflow
  }

  save(id: number, edges: string, nodes: string): Promise<AxiosResponse<any>> {
    const params: RequestData = {
      body: {
        id,
        edges,
        nodes,
      },
    }

    return new Promise((resolve, reject) => {
      createApiRequest(apiConfig.workflowSave, params)
        .then((response: AxiosResponse<ResponsesWorkflowResponse>) => {
          this.workflow = response.data
          resolve(response)
        })
        .catch((error) => {
          reject(new Error(error.message ?? 'Error saving workflow'))
        })
    })
  }

  publish(
    id: number,
    edges: string,
    nodes: string
  ): Promise<AxiosResponse<any>> {
    const params: RequestData = {
      body: {
        id,
        edges,
        nodes,
      },
    }

    return new Promise((resolve, reject) => {
      createApiRequest(apiConfig.workflowPublish, params)
        .then((response: AxiosResponse<ResponsesWorkflowResponse>) => {
          this.workflow = response.data
          resolve(response)
        })
        .catch((error) => {
          reject(new Error(error.message ?? 'Error publishing workflow'))
        })
    })
  }
}

export default new WorkflowStore()
