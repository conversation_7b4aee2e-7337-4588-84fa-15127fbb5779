import RootNode from 'views/portal/agent/studio/new-workflow/nodes/root'
import type BaseNode from 'views/portal/agent/studio/new-workflow/lib/base-node'

export const getAllNodes = (
  node: RootNode | BaseNode | null
): Record<string, BaseNode> => {
  if (!node) return {}
  const result: Record<string, BaseNode> = {}
  const traverseTree = (currentNode: BaseNode) => {
    Reflect.set(result, currentNode.node.data.id, currentNode)

    if (currentNode instanceof RootNode) {
      currentNode.freeNodes.forEach((node) => traverseTree(node))
    }

    if (
      currentNode.node.data.children &&
      currentNode.node.data.children.length > 0
    ) {
      currentNode.node.data.children.forEach((child: BaseNode) => {
        traverseTree(child)
      })
    }
  }

  traverseTree(node)
  return result
}
