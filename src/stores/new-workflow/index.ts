import { cloneDeep, throttle } from 'lodash-es'
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

import { isDev } from 'utils/env'
import { getAllNodes } from './utils'

import { CheckListItemType } from 'views/portal/agent/studio/new-workflow/components/checklist/types'
import type { CreateNodeSidebarProps } from 'views/portal/agent/studio/new-workflow/components/create-node-sidebar'
import type BaseNode from 'views/portal/agent/studio/new-workflow/lib/base-node'
import type RootNode from 'views/portal/agent/studio/new-workflow/nodes/root'

type SidebarConfigType = {
  open: boolean
  type: 'nodeDetail' | 'createNode' | 'createLoopNode' | ''
  createNodeInfo?: CreateNodeSidebarProps
}

type WorkflowState = {
  workflowTree: RootNode | null
  allNodes: Record<string, BaseNode> | null
  currentNodeId: string
  sidebarConfig: SidebarConfigType
  checkListData: CheckListItemType[]

  setNode: (id: string, node: BaseNode) => void
  setNodeThrottle: (id: string, node: BaseNode) => void
  setWorkflowTree: (workflowTree: RootNode) => void
  setCurrentNodeId: (id: string) => void
  setSidebarConfig: (config: Partial<SidebarConfigType>) => void
  refreshWorkflowTree: () => void
  setCheckListData: (data: CheckListItemType[]) => void
}

export const useWorkflowStore = create<WorkflowState>()(
  devtools(
    (set, get) => ({
      workflowTree: null,
      allNodes: null,
      currentNodeId: '',
      sidebarConfig: { open: false, type: '' },
      checkListData: [],

      setWorkflowTree: (workflowTree) => {
        const allNodes = getAllNodes(workflowTree)
        return set({ workflowTree, allNodes }, false, 'setWorkflowTree')
      },
      setNode: (id, node) => {
        if (!get().allNodes) return
        const allNodes = { ...get().allNodes, [id]: node }
        return set({ allNodes }, false, 'setNode')
      },
      setNodeThrottle: throttle((id, node) => {
        if (!get().allNodes) return
        const allNodes = { ...get().allNodes, [id]: node }
        return set({ allNodes }, false, 'setNode')
      }, 300),
      setCurrentNodeId: (id) => {
        return set({ currentNodeId: id }, false, 'setCurrentNodeId')
      },
      setSidebarConfig: (config) => {
        return set(
          { sidebarConfig: { ...get().sidebarConfig, ...config } },
          false,
          'setSidebar'
        )
      },
      refreshWorkflowTree: () => {
        const newTree = cloneDeep(get().workflowTree)
        if (newTree) get().setWorkflowTree(newTree)
      },
      setCheckListData: throttle((data) => {
        return set({ checkListData: data }, false, 'setCheckListData')
      }, 1000),
    }),
    {
      name: 'Workflow Store',
      enabled: isDev,
      trace: true,
    }
  )
)
