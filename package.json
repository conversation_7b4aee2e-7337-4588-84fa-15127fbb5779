{"name": "universal-portal", "version": "0.1.0", "private": true, "scripts": {"idl": "node scripts/idl/index.js", "start": "REACT_APP_IS_LOCAL=true node scripts/start.js", "start-c": "REACT_APP_IS_LOCAL=true CIRCULAR_DETECT=true node scripts/start.js", "build": "node scripts/build.js", "test": "node scripts/test.js", "build:dev": "env-cmd -f .env.dev node scripts/build.js"}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "^1.4.0", "@atlaskit/pragmatic-drag-and-drop-react-drop-indicator": "^1.1.3", "@babel/core": "^7.16.0", "@microlink/react-json-view": "^1.26.1", "@monaco-editor/react": "^4.6.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.3", "@svgr/webpack": "^5.5.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@xyflow/react": "^12.0.3", "@zod/mini": "^4.0.0-beta.20250414T061543", "antd": "^5.21.3", "axios": "^1.6.8", "babel-jest": "^27.4.2", "babel-loader": "^8.2.3", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "^10.0.1", "bfj": "^7.0.2", "browserslist": "^4.18.1", "camelcase": "^6.2.1", "case-sensitive-paths-webpack-plugin": "^2.4.0", "classnames": "^2.5.1", "clipboard-copy": "^4.0.1", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^3.2.0", "dayjs": "^1.11.11", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "echarts": "^5.5.1", "echarts-for-react": "^3.0.2", "eslint": "^8.3.0", "eslint-config-react-app": "^7.0.1", "eslint-webpack-plugin": "^3.1.1", "file-loader": "^6.2.0", "fs-extra": "^10.0.0", "highlight.js": "^11.9.0", "html-webpack-plugin": "^5.5.0", "identity-obj-proxy": "^3.0.0", "jest": "^27.4.3", "jest-resolve": "^27.4.2", "jest-watch-typeahead": "^1.0.0", "lodash-es": "^4.17.21", "mini-css-extract-plugin": "^2.4.5", "mobx": "^6.12.3", "mobx-persist": "^0.4.1", "mobx-persist-store": "^1.1.5", "mobx-react": "^9.1.1", "monaco-editor": "^0.50.0", "monaco-editor-webpack-plugin": "^7.1.0", "nanoid": "^5.1.4", "nuqs": "^2.4.2", "postcss": "^8.4.4", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^6.2.1", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^7.0.1", "prismjs": "^1.30.0", "prompts": "^2.4.2", "react": "^18.2.0", "react-app-polyfill": "^3.0.0", "react-dev-utils": "^12.0.1", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-markdown": "^9.0.1", "react-refresh": "^0.11.0", "react-router-dom": "^6.23.0", "react-simple-code-editor": "^0.13.1", "react-sticky-box": "^2.0.5", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.0", "resolve": "^1.20.0", "resolve-url-loader": "^4.0.0", "sass": "^1.77.8", "sass-loader": "^12.3.0", "semver": "^7.3.5", "source-map-loader": "^3.0.0", "style-loader": "^3.3.1", "tailwindcss": "^3.0.2", "terser-webpack-plugin": "^5.2.5", "typescript": "^5.8.3", "uuid": "^9.0.1", "web-vitals": "^2.1.4", "webpack": "^5.64.4", "webpack-dev-server": "^4.6.0", "webpack-manifest-plugin": "^4.0.2", "workbox-webpack-plugin": "^6.4.1", "zustand": "^5.0.3"}, "devDependencies": {"@babel/plugin-transform-private-property-in-object": "^7.25.9", "@types/jest": "^27.5.2", "@types/lodash-es": "^4.17.12", "@types/node": "^16.18.96", "@types/prismjs": "^1.26.4", "@types/react": "^18.2.79", "@types/react-dom": "^18.2.25", "@types/react-helmet": "^6.1.11", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^9.0.8", "circular-dependency-plugin": "^5.2.2", "env-cmd": "^10.1.0", "eslint-plugin-import": "^2.31.0", "swagger-typescript-api": "^13.0.22"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}