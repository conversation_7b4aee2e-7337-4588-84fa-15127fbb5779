pipelines:
  custom:
    codeguru-security:
    - step:
        image: public.ecr.aws/l6c8c5q3/codegurusecurity-actions-public:latest
        oidc: true
        script:
          - export AWS_ROLE_ARN=arn:aws:iam::879381271672:role/CodeGuruSecurityBitbucketAccessRole
          - export AWS_WEB_IDENTITY_TOKEN_FILE=$(pwd)/web-identity-token
          - echo $BITBUCKET_STEP_OIDC_TOKEN > $(pwd)/web-identity-token
          - python /usr/app/codeguru/command.py --source_path . --aws_region ap-southeast-1 --scan_name CGS-Bitbucket-$BITBUCKET_REPO_SLUG --fail_on_severity Critical
          - cat codeguru-security-results.sarif.json
