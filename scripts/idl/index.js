// https://github.com/acacode/swagger-typescript-api
const { generateApi } = require('swagger-typescript-api')
const path = require('path')

const PATH_TO_OUTPUT_DIR = path.resolve(process.cwd(), './src/api')

generateApi({
  output: PATH_TO_OUTPUT_DIR,
  httpClientType: false,
  modular: true,
  url: 'https://dev-genie.001.gs/api/v1/swagger/doc.json',
  templates: path.resolve(process.cwd(), './scripts/idl/templates'),
})
