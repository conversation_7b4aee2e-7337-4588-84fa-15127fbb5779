'use strict'

const config = {
  plugins: ['import'],
  rules: {
    'import/no-cycle': ['error', { maxDepth: Infinity }],
    'import/no-self-import': 'error',
    'import/no-useless-path-segments': ['error', { noUselessIndex: true }],
  },
  settings: {
    'import/resolver': {
      node: {
        extensions: ['.js', '.jsx', '.ts', '.tsx'],
      },
    },
  },
  extends: [
    '@babel/plugin-transform-private-property-in-object',
    'react-app',
    'react-app/jest',
  ],
}

module.exports = config
